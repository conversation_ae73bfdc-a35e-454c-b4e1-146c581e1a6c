body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    font-size: 14px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

a {
    color: #09f;
    text-decoration: none;
}

a:hover,
a:focus {
    color: #09f;
    text-decoration: none;
}

blockquote {
    padding: 0 0 0 15px;
    margin: 0 0 18px;
    border-left: 5px solid #EEE;
}

img + br {
    display: block;
    padding: 4px 0;
    content: ' ';
}

body p {
    margin-bottom: 1em;
}

iframe {
    border: none;
}

img {
    max-width: 100%;
}

img[data-word-image] {
    cursor: pointer;
}

pre {
    margin: .5em 0;
    padding: .4em .6em;
    border-radius: 8px;
    background: #f8f8f8;
    line-height: 1.5;
}

/*交互操作*/
img {
    cursor: pointer;
}

.edui-quick-operate-active {
    background: #E6ECFF;
}
