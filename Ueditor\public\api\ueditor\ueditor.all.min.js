/*!
 * UEditor
 * version: ueditor xunruicms fix
 */
if(void 0===uedtiro_baidumap_ak)var uedtiro_baidumap_ak="";if(void 0===ueditor_lang)var ueditor_lang={labelMap:{anchor:"锚点",undo:"撤销",redo:"重做",bold:"加粗",indent:"首行缩进",snapscreen:"截图",italic:"斜体",underline:"下划线",strikethrough:"删除线",subscript:"下标",fontborder:"字符边框",superscript:"上标",formatmatch:"格式刷",source:"源代码",blockquote:"引用",pasteplain:"纯文本粘贴模式",selectall:"全选",print:"打印",preview:"预览",horizontal:"分隔线",removeformat:"清除格式",time:"时间",date:"日期",unlink:"取消链接",insertrow:"前插入行",insertcol:"前插入列",mergeright:"右合并单元格",mergedown:"下合并单元格",deleterow:"删除行",deletecol:"删除列",splittorows:"拆分成行",splittocols:"拆分成列",splittocells:"完全拆分单元格",deletecaption:"删除表格标题",inserttitle:"插入标题",mergecells:"合并多个单元格",deletetable:"删除表格",cleardoc:"清空文档",insertparagraphbeforetable:"表格前插入行",insertcode:"代码语言",fontfamily:"字体",fontsize:"字号",paragraph:"段落格式",simpleupload:"单图上传",insertimage:"多图上传",edittable:"表格属性",edittd:"单元格属性",link:"超链接",emotion:"表情",spechars:"特殊字符",searchreplace:"查询替换",map:"Baidu地图",gmap:"Google地图",insertvideo:"视频",help:"帮助",justifyleft:"居左对齐",justifyright:"居右对齐",justifycenter:"居中对齐",justifyjustify:"两端对齐",forecolor:"字体颜色",backcolor:"背景色",insertorderedlist:"有序列表",insertunorderedlist:"无序列表",fullscreen:"全屏",directionalityltr:"从左向右输入",directionalityrtl:"从右向左输入",rowspacingtop:"段前距",rowspacingbottom:"段后距",pagebreak:"分页",insertframe:"插入Iframe",imagenone:"默认",imageleft:"左浮动",imageright:"右浮动",attachment:"附件",imagecenter:"居中",wordimage:"图片转存",lineheight:"行间距",edittip:"编辑提示",customstyle:"自定义标题",autotypeset:"自动排版",webapp:"百度应用",touppercase:"字母大写",tolowercase:"字母小写",background:"背景",template:"模板",scrawl:"涂鸦",music:"音乐",inserttable:"插入表格",drafts:"在尾部插入换行标签",charts:"图表"},insertorderedlist:{num:"1,2,3...",num1:"1),2),3)...",num2:"(1),(2),(3)...",cn:"一,二,三....",cn1:"一),二),三)....",cn2:"(一),(二),(三)....",decimal:"1,2,3...","lower-alpha":"a,b,c...","lower-roman":"i,ii,iii...","upper-alpha":"A,B,C...","upper-roman":"I,II,III..."},insertunorderedlist:{circle:"○ 大圆圈",disc:"● 小黑点",square:"■ 小方块 ",dash:"— 破折号",dot:" 。 小圆圈"},paragraph:{p:"段落",h1:"标题 1",h2:"标题 2",h3:"标题 3",h4:"标题 4",h5:"标题 5",h6:"标题 6"},fontfamily:{songti:"宋体",kaiti:"楷体",heiti:"黑体",lishu:"隶书",yahei:"微软雅黑",andaleMono:"andale mono",arial:"arial",arialBlack:"arial black",comicSansMs:"comic sans ms",impact:"impact",timesNewRoman:"times new roman"},customstyle:{tc:"标题居中",tl:"标题居左",im:"强调",hi:"明显强调"},autoupload:{exceedSizeError:"文件大小超出限制",exceedTypeError:"文件格式不允许",jsonEncodeError:"服务器返回格式错误",loading:"正在上传...",loadError:"上传错误",errorLoadConfig:"后端配置项没有正常加载，上传插件不能正常使用！"},simpleupload:{exceedSizeError:"文件大小超出限制",exceedTypeError:"文件格式不允许",jsonEncodeError:"服务器返回格式错误",loading:"正在上传...",loadError:"上传错误",errorLoadConfig:"后端配置项没有正常加载，上传插件不能正常使用！"},elementPathTip:"元素路径",wordCountTip:"字数统计",wordCountMsg:"当前已输入{#count}个字符, 您还可以输入{#leave}个字符。 ",wordOverFlowMsg:'<span style="color:red;">字数超出最大允许值，服务器可能拒绝保存！</span>',ok:"确认",cancel:"取消",closeDialog:"关闭对话框",tableDrag:"表格拖动必须引入uiUtils.js文件！",autofloatMsg:"工具栏浮动依赖编辑器UI，您首先需要引入UI文件!",loadconfigError:"获取后台配置项请求出错，上传功能将不能正常使用！",loadconfigFormatError:"后台配置项返回格式出错，上传功能将不能正常使用！",loadconfigHttpError:"请求后台配置项http错误，上传功能将不能正常使用！",snapScreen_plugin:{browserMsg:"仅支持IE浏览器！",callBackErrorMsg:"服务器返回数据有误，请检查配置项之后重试。",uploadErrorMsg:"截图上传失败，请检查服务器端环境! "},insertcode:{as3:"ActionScript 3",bash:"Bash/Shell",cpp:"C/C++",css:"CSS",cf:"ColdFusion","c#":"C#",delphi:"Delphi",diff:"Diff",erlang:"Erlang",groovy:"Groovy",html:"HTML",java:"Java",jfx:"JavaFX",js:"JavaScript",pl:"Perl",php:"PHP",plain:"Plain Text",ps:"PowerShell",python:"Python",ruby:"Ruby",scala:"Scala",sql:"SQL",vb:"Visual Basic",xml:"XML"},confirmClear:"确定清空当前文档么？",contextMenu:{delete:"删除",selectall:"全选",deletecode:"删除代码",cleardoc:"清空文档",confirmclear:"确定清空当前文档么？",unlink:"删除超链接",paragraph:"段落格式",edittable:"表格属性",aligntd:"单元格对齐方式",aligntable:"表格对齐方式",tableleft:"左浮动",tablecenter:"居中显示",tableright:"右浮动",edittd:"单元格属性",setbordervisible:"设置表格边线可见",justifyleft:"左对齐",justifyright:"右对齐",justifycenter:"居中对齐",justifyjustify:"两端对齐",table:"表格",inserttable:"插入表格",deletetable:"删除表格",insertparagraphbefore:"前插入段落",insertparagraphafter:"后插入段落",deleterow:"删除当前行",deletecol:"删除当前列",insertrow:"前插入行",insertcol:"左插入列",insertrownext:"后插入行",insertcolnext:"右插入列",insertcaption:"插入表格名称",deletecaption:"删除表格名称",inserttitle:"插入表格标题行",deletetitle:"删除表格标题行",inserttitlecol:"插入表格标题列",deletetitlecol:"删除表格标题列",averageDiseRow:"平均分布各行",averageDisCol:"平均分布各列",mergeright:"向右合并",mergeleft:"向左合并",mergedown:"向下合并",mergecells:"合并单元格",splittocells:"完全拆分单元格",splittocols:"拆分成列",splittorows:"拆分成行",tablesort:"表格排序",enablesort:"设置表格可排序",disablesort:"取消表格可排序",reversecurrent:"逆序当前",orderbyasc:"按ASCII字符升序",reversebyasc:"按ASCII字符降序",orderbynum:"按数值大小升序",reversebynum:"按数值大小降序",borderbk:"边框底纹",setcolor:"表格隔行变色",unsetcolor:"取消表格隔行变色",setbackground:"选区背景隔行",unsetbackground:"取消选区背景",redandblue:"红蓝相间",threecolorgradient:"三色渐变",copy:"复制(Ctrl + c)",copymsg:"浏览器不支持,请使用 'Ctrl + c'",paste:"粘贴(Ctrl + v)",pastemsg:"浏览器不支持,请使用 'Ctrl + v'"},copymsg:"浏览器不支持,请使用 'Ctrl + c'",pastemsg:"浏览器不支持,请使用 'Ctrl + v'",anthorMsg:"链接",clearColor:"清空颜色",standardColor:"标准颜色",themeColor:"主题颜色",property:"属性",default:"默认",modify:"修改",justifyleft:"左对齐",justifyright:"右对齐",justifycenter:"居中",justify:"默认",clear:"清除",anchorMsg:"锚点",delete:"删除",clickToUpload:"点击上传",unset:"尚未设置语言文件",t_row:"行",t_col:"列",more:"更多",pasteOpt:"粘贴选项",pasteSourceFormat:"保留源格式",tagFormat:"只保留标签",pasteTextFormat:"只保留文本",autoTypeSet:{mergeLine:"合并空行",delLine:"清除空行",removeFormat:"清除格式",indent:"首行缩进",alignment:"对齐方式",imageFloat:"图片浮动",removeFontsize:"清除字号",removeFontFamily:"清除字体",removeHtml:"清除冗余HTML代码",pasteFilter:"粘贴过滤",run:"执行",symbol:"符号转换",bdc2sb:"全角转半角",tobdc:"半角转全角"},background:{static:{lang_background_normal:"背景设置",lang_background_local:"在线图片",lang_background_set:"选项",lang_background_none:"无背景色",lang_background_colored:"有背景色",lang_background_color:"颜色设置",lang_background_netimg:"网络图片",lang_background_align:"对齐方式",lang_background_position:"精确定位",repeatType:{options:["居中","横向重复","纵向重复","平铺","自定义"]}},noUploadImage:"当前未上传过任何图片！",toggleSelect:"单击可切换选中状态\n原图尺寸: "},insertimage:{static:{lang_tab_remote:"插入图片",lang_tab_upload:"本地上传",lang_tab_online:"在线管理",lang_tab_search:"图片搜索",lang_input_url:"地 址：",lang_input_size:"大 小：",lang_input_width:"宽度",lang_input_height:"高度",lang_input_border:"边 框：",lang_input_vhspace:"边 距：",lang_input_title:"描 述：",lang_input_align:"图片浮动方式：",lang_imgLoading:"　图片加载中……",lang_start_upload:"开始上传",lock:{title:"锁定宽高比例"},searchType:{title:"图片类型",options:["新闻","壁纸","表情","头像"]},searchTxt:{value:"请输入搜索关键词"},searchBtn:{value:"百度一下"},searchReset:{value:"清空搜索"},noneAlign:{title:"无浮动"},leftAlign:{title:"左浮动"},rightAlign:{title:"右浮动"},centerAlign:{title:"居中独占一行"}},uploadSelectFile:"点击选择图片",uploadAddFile:"继续添加",uploadStart:"开始上传",uploadPause:"暂停上传",uploadContinue:"继续上传",uploadRetry:"重试上传",uploadDelete:"删除",uploadTurnLeft:"向左旋转",uploadTurnRight:"向右旋转",uploadPreview:"预览中",uploadNoPreview:"不能预览",updateStatusReady:"选中_张图片，共_KB。",updateStatusConfirm:"已成功上传_张照片，_张照片上传失败",updateStatusFinish:"共_张（_KB），_张上传成功",updateStatusError:"，_张上传失败。",errorNotSupport:"WebUploader 不支持您的浏览器！如果你使用的是IE浏览器，请尝试升级 flash 播放器。",errorLoadConfig:"后端配置项没有正常加载，上传插件不能正常使用！",errorExceedSize:"文件大小超出",errorFileType:"文件格式不允许",errorInterrupt:"文件传输中断",errorUploadRetry:"上传失败，请重试",errorHttp:"http请求错误",errorServerUpload:"服务器返回出错",remoteLockError:"宽高不正确,不能所定比例",numError:"请输入正确的长度或者宽度值！例如：123，400",imageUrlError:"不允许的图片格式或者图片域！",imageLoadError:"图片加载失败！请检查链接地址或网络状态！",searchRemind:"请输入搜索关键词",searchLoading:"图片加载中，请稍后……",searchRetry:" :( ，抱歉，没有找到图片！请重试一次！"},attachment:{static:{lang_tab_upload:"上传附件",lang_tab_online:"在线附件",lang_start_upload:"开始上传",lang_drop_remind:"可以将文件拖到这里，单次最多可选100个文件"},uploadSelectFile:"点击选择文件",uploadAddFile:"继续添加",uploadStart:"开始上传",uploadPause:"暂停上传",uploadContinue:"继续上传",uploadRetry:"重试上传",uploadDelete:"删除",uploadTurnLeft:"向左旋转",uploadTurnRight:"向右旋转",uploadPreview:"预览中",updateStatusReady:"选中_个文件，共_KB。",updateStatusConfirm:"已成功上传_个文件，_个文件上传失败",updateStatusFinish:"共_个（_KB），_个上传成功",updateStatusError:"，_张上传失败。",errorNotSupport:"WebUploader 不支持您的浏览器！如果你使用的是IE浏览器，请尝试升级 flash 播放器。",errorLoadConfig:"后端配置项没有正常加载，上传插件不能正常使用！",errorExceedSize:"文件大小超出",errorFileType:"文件格式不允许",errorInterrupt:"文件传输中断",errorUploadRetry:"上传失败，请重试",errorHttp:"http请求错误",errorServerUpload:"服务器返回出错"},insertvideo:{static:{lang_tab_insertV:"插入视频",lang_tab_searchV:"搜索视频",lang_tab_uploadV:"上传视频",lang_tab_online:"在线管理",lang_video_url:"视频网址",lang_video_size:"视频尺寸",lang_videoW:"宽度",lang_videoH:"高度",lang_alignment:"对齐方式",videoSearchTxt:{value:"请输入搜索关键字！"},videoType:{options:["全部","热门","娱乐","搞笑","体育","科技","综艺"]},videoSearchBtn:{value:"百度一下"},videoSearchReset:{value:"清空结果"},lang_input_fileStatus:" 当前未上传文件",startUpload:{style:"background:url(upload.png) no-repeat;"},lang_upload_size:"视频尺寸",lang_upload_width:"宽度",lang_upload_height:"高度",lang_upload_alignment:"对齐方式",lang_format_advice:"建议使用mp4格式."},numError:"请输入正确的数值，如123,400",floatLeft:"左浮动",floatRight:"右浮动",'"default"':"默认",block:"独占一行",urlError:"输入的视频地址有误，请检查后再试！",loading:" &nbsp;视频加载中，请等待……",clickToSelect:"点击选中",goToSource:"访问源视频",noVideo:" &nbsp; &nbsp;抱歉，找不到对应的视频，请重试！",browseFiles:"浏览文件",uploadSuccess:"上传成功!",delSuccessFile:"从成功队列中移除",delFailSaveFile:"移除保存失败文件",statusPrompt:" 个文件已上传！ ",flashVersionError:"当前Flash版本过低，请更新FlashPlayer后重试！",flashLoadingError:"Flash加载失败!请检查路径或网络状态",fileUploadReady:"等待上传……",delUploadQueue:"从上传队列中移除",limitPrompt1:"单次不能选择超过",limitPrompt2:"个文件！请重新选择！",delFailFile:"移除失败文件",fileSizeLimit:"文件大小超出限制！",emptyFile:"空文件无法上传！",fileTypeError:"文件类型不允许！",unknownError:"未知错误！",fileUploading:"上传中，请等待……",cancelUpload:"取消上传",netError:"网络错误",failUpload:"上传失败!",serverIOError:"服务器IO错误！",noAuthority:"无权限！",fileNumLimit:"上传个数限制",failCheck:"验证失败，本次上传被跳过！",fileCanceling:"取消中，请等待……",stopUploading:"上传已停止……",uploadSelectFile:"点击选择文件",uploadAddFile:"继续添加",uploadStart:"开始上传",uploadPause:"暂停上传",uploadContinue:"继续上传",uploadRetry:"重试上传",uploadDelete:"删除",uploadTurnLeft:"向左旋转",uploadTurnRight:"向右旋转",uploadPreview:"预览中",updateStatusReady:"选中_个文件，共_KB。",updateStatusConfirm:"成功上传_个，_个失败",updateStatusFinish:"共_个(_KB)，_个成功上传",updateStatusError:"，_张上传失败。",errorNotSupport:"WebUploader 不支持您的浏览器！如果你使用的是IE浏览器，请尝试升级 flash 播放器。",errorLoadConfig:"后端配置项没有正常加载，上传插件不能正常使用！",errorExceedSize:"文件大小超出",errorFileType:"文件格式不允许",errorInterrupt:"文件传输中断",errorUploadRetry:"上传失败，请重试",errorHttp:"http请求错误",errorServerUpload:"服务器返回出错"},webapp:{tip1:"本功能由百度APP提供，如看到此页面，请各位站长首先申请百度APPKey!",tip2:"申请完成之后请至ueditor.config.js中配置获得的appkey! ",applyFor:"点此申请",anthorApi:"百度API"},template:{static:{lang_template_bkcolor:"背景颜色",lang_template_clear:"保留原有内容",lang_template_select:"选择模板"},blank:"空白文档",blog:"博客文章",resume:"个人简历",richText:"图文混排",sciPapers:"科技论文"},scrawl:{static:{lang_input_previousStep:"上一步",lang_input_nextsStep:"下一步",lang_input_clear:"清空",lang_input_addPic:"添加背景",lang_input_ScalePic:"缩放背景",lang_input_removePic:"删除背景",J_imgTxt:{title:"添加背景图片"}},noScarwl:"尚未作画，白纸一张~",scrawlUpLoading:"涂鸦上传中,别急哦~",continueBtn:"继续",imageError:"糟糕，图片读取失败了！",backgroundUploading:"背景图片上传中,别急哦~"},music:{static:{lang_input_tips:"输入歌手/歌曲/专辑，搜索您感兴趣的音乐！",J_searchBtn:{value:"搜索歌曲"}},emptyTxt:"未搜索到相关音乐结果，请换一个关键词试试。",chapter:"歌曲",singer:"歌手",special:"专辑",listenTest:"试听"},anchor:{static:{lang_input_anchorName:"锚点名字："}},charts:{static:{lang_data_source:"数据源：",lang_chart_format:"图表格式：",lang_data_align:"数据对齐方式",lang_chart_align_same:"数据源与图表X轴Y轴一致",lang_chart_align_reverse:"数据源与图表X轴Y轴相反",lang_chart_title:"图表标题",lang_chart_main_title:"主标题：",lang_chart_sub_title:"子标题：",lang_chart_x_title:"X轴标题：",lang_chart_y_title:"Y轴标题：",lang_chart_tip:"提示文字",lang_cahrt_tip_prefix:"提示文字前缀：",lang_cahrt_tip_description:"仅饼图有效， 当鼠标移动到饼图中相应的块上时，提示框内的文字的前缀",lang_chart_data_unit:"数据单位",lang_chart_data_unit_title:"单位：",lang_chart_data_unit_description:"显示在每个数据点上的数据的单位， 比如： 温度的单位 ℃",lang_chart_type:"图表类型：",lang_prev_btn:"上一个",lang_next_btn:"下一个"}},emotion:{static:{lang_input_choice:"精选",lang_input_Tuzki:"兔斯基",lang_input_BOBO:"BOBO",lang_input_lvdouwa:"绿豆蛙",lang_input_babyCat:"baby猫",lang_input_bubble:"泡泡",lang_input_youa:"有啊"}},gmap:{static:{lang_input_address:"地址",lang_input_search:"搜索",address:{value:"北京"}},searchError:"无法定位到该地址!"},help:{static:{lang_input_about:"关于UEditor",lang_input_shortcuts:"快捷键",lang_input_introduction:"UEditor是由百度web前端研发部开发的所见即所得富文本web编辑器，具有轻量，可定制，注重用户体验等特点。开源基于BSD协议，允许自由使用和修改代码。",lang_Txt_shortcuts:"快捷键",lang_Txt_func:"功能",lang_Txt_bold:"给选中字设置为加粗",lang_Txt_copy:"复制选中内容",lang_Txt_cut:"剪切选中内容",lang_Txt_Paste:"粘贴",lang_Txt_undo:"重新执行上次操作",lang_Txt_redo:"撤销上一次操作",lang_Txt_italic:"给选中字设置为斜体",lang_Txt_underline:"给选中字加下划线",lang_Txt_selectAll:"全部选中",lang_Txt_visualEnter:"软回车",lang_Txt_fullscreen:"全屏"}},insertframe:{static:{lang_input_address:"地址：",lang_input_width:"宽度：",lang_input_height:"高度：",lang_input_isScroll:"允许滚动条：",lang_input_frameborder:"显示框架边框：",lang_input_alignMode:"对齐方式：",align:{title:"对齐方式",options:["默认","左对齐","右对齐","居中"]}},enterAddress:"请输入地址!"},link:{static:{lang_input_text:"文本内容：",lang_input_url:"链接地址：",lang_input_title:"标题：",lang_input_target:"是否在新窗口打开："},validLink:"只支持选中一个链接时生效",httpPrompt:"您输入的超链接中不包含http等协议名称，默认将为您添加http://前缀"},map:{static:{lang_city:"城市",lang_address:"地址",city:{value:"北京"},lang_search:"搜索",lang_dynamicmap:"插入动态地图"},cityMsg:"请选择城市",errorMsg:"抱歉，找不到该位置！"},searchreplace:{static:{lang_tab_search:"查找",lang_tab_replace:"替换",lang_search1:"查找",lang_search2:"查找",lang_replace:"替换",lang_searchReg:"支持正则表达式，添加前后斜杠标示为正则表达式，例如“/表达式/”",lang_searchReg1:"支持正则表达式，添加前后斜杠标示为正则表达式，例如“/表达式/”",lang_case_sensitive1:"区分大小写",lang_case_sensitive2:"区分大小写",nextFindBtn:{value:"下一个"},preFindBtn:{value:"上一个"},nextReplaceBtn:{value:"下一个"},preReplaceBtn:{value:"上一个"},repalceBtn:{value:"替换"},repalceAllBtn:{value:"全部替换"}},getEnd:"已经搜索到文章末尾！",getStart:"已经搜索到文章头部",countMsg:"总共替换了{#count}处！"},snapscreen:{static:{lang_showMsg:"截图功能需要首先安装UEditor截图插件！ ",lang_download:"点此下载",lang_step1:"第一步，下载UEditor截图插件并运行安装。",lang_step2:"第二步，插件安装完成后即可使用，如不生效，请重启浏览器后再试！"}},spechars:{static:{},tsfh:"特殊字符",lmsz:"罗马字符",szfh:"数学字符",rwfh:"日文字符",xlzm:"希腊字母",ewzm:"俄文字符",pyzm:"拼音字母",yyyb:"英语音标",zyzf:"其他"},edittable:{static:{lang_tableStyle:"表格样式",lang_insertCaption:"添加表格名称行",lang_insertTitle:"添加表格标题行",lang_insertTitleCol:"添加表格标题列",lang_orderbycontent:"使表格内容可排序",lang_tableSize:"自动调整表格尺寸",lang_autoSizeContent:"按表格文字自适应",lang_autoSizePage:"按页面宽度自适应",lang_example:"示例",lang_borderStyle:"表格边框",lang_color:"颜色:"},captionName:"表格名称",titleName:"标题",cellsName:"内容",errorMsg:"有合并单元格，不可排序"},edittip:{static:{lang_delRow:"删除整行",lang_delCol:"删除整列"}},edittd:{static:{lang_tdBkColor:"背景颜色:"}},formula:{static:{}},wordimage:{static:{lang_resave:"转存步骤",uploadBtn:{src:"upload.png",alt:"上传"},clipboard:{style:"background: url(copy.png) -153px -1px no-repeat;"},lang_step:"1、点击顶部复制按钮，将地址复制到剪贴板；2、点击添加照片按钮，在弹出的对话框中使用Ctrl+V粘贴地址；3、点击打开后选择图片上传流程。"},fileType:"图片",flashError:"FLASH初始化失败，请检查FLASH插件是否正确安装！",netError:"网络连接错误，请重试！",copySuccess:"图片地址已经复制！",flashI18n:{}},autosave:{saving:"保存中...",success:"本地保存成功"}};!function(){UEDITOR_CONFIG=window.UEDITOR_CONFIG||{};var baidu=window.baidu||{};window.baidu=baidu,window.UE=baidu.editor=window.UE||{},UE.plugins={},UE.commands={},UE.instants={},UE.I18N={},UE._customizeUI={},UE.version="1.5.0",UE.I18N.lang=ueditor_lang;var dom=UE.dom={},browser=UE.browser=function(){var e=navigator.userAgent.toLowerCase(),t=window.opera,i={ie:/(msie\s|trident.*rv:)([\w.]+)/.test(e),opera:!!t&&t.version,webkit:e.indexOf(" applewebkit/")>-1,mac:e.indexOf("macintosh")>-1,quirks:"BackCompat"==document.compatMode};i.gecko="Gecko"==navigator.product&&!i.webkit&&!i.opera&&!i.ie;var n=0;if(i.ie){var o=e.match(/(?:msie\s([\w.]+))/),r=e.match(/(?:trident.*rv:([\w.]+))/);n=o&&r&&o[1]&&r[1]?Math.max(1*o[1],1*r[1]):o&&o[1]?1*o[1]:r&&r[1]?1*r[1]:0,i.ie11Compat=11==document.documentMode,i.ie9Compat=9==document.documentMode,i.ie8=!!document.documentMode,i.ie8Compat=8==document.documentMode,i.ie7Compat=7==n&&!document.documentMode||7==document.documentMode,i.ie6Compat=n<7||i.quirks,i.ie9above=n>8,i.ie9below=n<9,i.ie11above=n>10,i.ie11below=n<11}if(i.gecko){var a=e.match(/rv:([\d\.]+)/);a&&(n=1e4*(a=a[1].split("."))[0]+100*(a[1]||0)+1*(a[2]||0))}return/chrome\/(\d+\.\d)/i.test(e)&&(i.chrome=+RegExp.$1),/(\d+\.\d)?(?:\.\d)?\s+safari\/?(\d+\.\d+)?/i.test(e)&&!/chrome/i.test(e)&&(i.safari=+(RegExp.$1||RegExp.$2)),i.opera&&(n=parseFloat(t.version())),i.webkit&&(n=parseFloat(e.match(/ applewebkit\/(\d+)/)[1])),i.version=n,i.isCompatible=!i.mobile&&(i.ie&&n>=6||i.gecko&&n>=10801||i.opera&&n>=9.5||i.air&&n>=1||i.webkit&&n>=522||!1),i}(),ie=browser.ie,webkit=browser.webkit,gecko=browser.gecko,opera=browser.opera,utils=UE.utils={each:function(e,t,i){if(null!=e)if(e.length===+e.length){for(var n=0,o=e.length;n<o;n++)if(!1===t.call(i,e[n],n,e))return!1}else for(var r in e)if(e.hasOwnProperty(r)&&!1===t.call(i,e[r],r,e))return!1},makeInstance:function(e){var t=new Function;return t.prototype=e,e=new t,t.prototype=null,e},extend:function(e,t,i){if(t)for(var n in t)i&&e.hasOwnProperty(n)||(e[n]=t[n]);return e},extend2:function(e){for(var t=arguments,i=1;i<t.length;i++){var n=t[i];for(var o in n)e.hasOwnProperty(o)||(e[o]=n[o])}return e},inherits:function(e,t){var i=e.prototype,n=utils.makeInstance(t.prototype);return utils.extend(n,i,!0),e.prototype=n,n.constructor=e},bind:function(e,t){return function(){return e.apply(t,arguments)}},defer:function(e,t,i){var n;return function(){i&&clearTimeout(n),n=setTimeout(e,t)}},indexOf:function(e,t,i){var n=-1;return i=this.isNumber(i)?i:0,this.each(e,function(e,o){if(o>=i&&e===t)return n=o,!1}),n},removeItem:function(e,t){for(var i=0,n=e.length;i<n;i++)e[i]===t&&(e.splice(i,1),i--)},trim:function(e){return e.replace(/(^[ \t\n\r]+)|([ \t\n\r]+$)/g,"")},listToMap:function(e){if(!e)return{};e=utils.isArray(e)?e:e.split(",");for(var t,i=0,n={};t=e[i++];)n[t.toUpperCase()]=n[t]=1;return n},unhtml:function(e,t){return e?e.replace(t||/[&<">'](?:(amp|lt|quot|gt|#39|nbsp|#\d+);)?/g,function(e,t){return t?e:{"<":"&lt;","&":"&amp;",'"':"&quot;",">":"&gt;","'":"&#39;"}[e]}):""},unhtmlForUrl:function(e,t){return e?e.replace(t||/[<">']/g,function(e){return{"<":"&lt;","&":"&amp;",'"':"&quot;",">":"&gt;","'":"&#39;"}[e]}):""},html:function(e){return e?e.replace(/&((g|l|quo)t|amp|#39|nbsp);/g,function(e){return{"&lt;":"<","&amp;":"&","&quot;":'"',"&gt;":">","&#39;":"'","&nbsp;":" "}[e]}):""},cssStyleToDomStyle:(test=document.createElement("div").style,cache={float:null!=test.cssFloat?"cssFloat":null!=test.styleFloat?"styleFloat":"float"},function(e){return cache[e]||(cache[e]=e.toLowerCase().replace(/-./g,function(e){return e.charAt(1).toUpperCase()}))}),loadFile:function(){var e=[];function t(t,i){try{for(var n,o=0;n=e[o++];)if(n.doc===t&&n.url==(i.src||i.href))return n}catch(e){return null}}return function(i,n,o){var r=t(i,n);if(r)r.ready?o&&o():r.funs.push(o);else if(e.push({doc:i,url:n.src||n.href,funs:[o]}),i.body){if(!n.id||!i.getElementById(n.id)){var a=i.createElement(n.tag);for(var s in delete n.tag,n)a.setAttribute(s,n[s]);a.onload=a.onreadystatechange=function(){if(!this.readyState||/loaded|complete/.test(this.readyState)){if((r=t(i,n)).funs.length>0){r.ready=1;for(var e;e=r.funs.pop();)e()}a.onload=a.onreadystatechange=null}},a.onerror=function(){},i.getElementsByTagName("head")[0].appendChild(a)}}else{var l=[];for(var s in n)"tag"!=s&&l.push(s+'="'+n[s]+'"');i.write("<"+n.tag+" "+l.join(" ")+" ></"+n.tag+">")}}}(),isEmptyObject:function(e){if(null==e)return!0;if(this.isArray(e)||this.isString(e))return 0===e.length;for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},fixColor:function(e,t){if(/color/i.test(e)&&/rgba?/.test(t)){var i=t.split(",");if(i.length>3)return"";t="#";for(var n,o=0;n=i[o++];)t+=1==(n=parseInt(n.replace(/[^\d]/gi,""),10).toString(16)).length?"0"+n:n;t=t.toUpperCase()}return t},optCss:function(e){var t,i;function n(e,t){if(!e)return"";var i=e.top,n=e.bottom,o=e.left,r=e.right,a="";if(i&&o&&n&&r)a+=";"+t+":"+(i==n&&n==o&&o==r?i:i==n&&o==r?i+" "+o:o==r?i+" "+o+" "+n:i+" "+r+" "+n+" "+o)+";";else for(var s in e)a+=";"+t+"-"+s+":"+e[s]+";";return a}return e=e.replace(/(padding|margin|border)\-([^:]+):([^;]+);?/gi,function(e,n,o,r){if(1==r.split(" ").length)switch(n){case"padding":return!t&&(t={}),t[o]=r,"";case"margin":return!i&&(i={}),i[o]=r,"";case"border":return"initial"==r?"":e}return e}),(e+=n(t,"padding")+n(i,"margin")).replace(/^[ \n\r\t;]*|[ \n\r\t]*$/,"").replace(/;([ \n\r\t]+)|\1;/g,";").replace(/(&((l|g)t|quot|#39))?;{2,}/g,function(e,t){return t?t+";;":";"})},clone:function(e,t){var i;for(var n in t=t||{},e)e.hasOwnProperty(n)&&("object"==typeof(i=e[n])?(t[n]=utils.isArray(i)?[]:{},utils.clone(e[n],t[n])):t[n]=i);return t},transUnitToPx:function(e){if(!/(pt|cm)/.test(e))return e;var t;switch(e.replace(/([\d.]+)(\w+)/,function(i,n,o){e=n,t=o}),t){case"cm":e=25*parseFloat(e);break;case"pt":e=Math.round(96*parseFloat(e)/72)}return e+(e?"px":"")},domReady:function(){var e=[];function t(t){t.isReady=!0;for(var i;i=e.pop();i());}return function(i,n){var o=(n=n||window).document;i&&e.push(i),"complete"===o.readyState?t(o):(o.isReady&&t(o),browser.ie&&11!=browser.version?(!function(){if(!o.isReady){try{o.documentElement.doScroll("left")}catch(e){return void setTimeout(arguments.callee,0)}t(o)}}(),n.attachEvent("onload",function(){t(o)})):(o.addEventListener("DOMContentLoaded",function(){o.removeEventListener("DOMContentLoaded",arguments.callee,!1),t(o)},!1),n.addEventListener("load",function(){t(o)},!1)))}}(),cssRule:browser.ie&&11!=browser.version?function(e,t,i){var n,o;return void 0===t||t&&t.nodeType&&9==t.nodeType?void 0!==(o=(n=(i=t&&t.nodeType&&9==t.nodeType?t:i||document).indexList||(i.indexList={}))[e])?i.styleSheets[o].cssText:void 0:(o=(n=(i=i||document).indexList||(i.indexList={}))[e],""===t?void 0!==o&&(i.styleSheets[o].cssText="",delete n[e],!0):(void 0!==o?sheetStyle=i.styleSheets[o]:(sheetStyle=i.createStyleSheet("",o=i.styleSheets.length),n[e]=o),void(sheetStyle.cssText=t)))}:function(e,t,i){var n;return void 0===t||t&&t.nodeType&&9==t.nodeType?(n=(i=t&&t.nodeType&&9==t.nodeType?t:i||document).getElementById(e))?n.innerHTML:void 0:(n=(i=i||document).getElementById(e),""===t?!!n&&(n.parentNode.removeChild(n),!0):void(n?n.innerHTML=t:((n=i.createElement("style")).id=e,n.innerHTML=t,i.getElementsByTagName("head")[0].appendChild(n))))},sort:function(e,t){t=t||function(e,t){return e.localeCompare(t)};for(var i=0,n=e.length;i<n;i++)for(var o=i,r=e.length;o<r;o++)if(t(e[i],e[o])>0){var a=e[i];e[i]=e[o],e[o]=a}return e},serializeParam:function(e){var t=[];for(var i in e)if("method"!=i&&"timeout"!=i&&"async"!=i)if("function"!=(typeof e[i]).toLowerCase()&&"object"!=(typeof e[i]).toLowerCase())t.push(encodeURIComponent(i)+"="+encodeURIComponent(e[i]));else if(utils.isArray(e[i]))for(var n=0;n<e[i].length;n++)t.push(encodeURIComponent(i)+"[]="+encodeURIComponent(e[i][n]));return t.join("&")},formatUrl:function(e){var t=e.replace(/&&/g,"&");return t=(t=(t=(t=t.replace(/\?&/g,"?")).replace(/&$/g,"")).replace(/&#/g,"#")).replace(/&+/g,"&")},isCrossDomainUrl:function(e){var t=document.createElement("a");return t.href=e,browser.ie&&(t.href=t.href),!(t.protocol==location.protocol&&t.hostname==location.hostname&&(t.port==location.port||"80"==t.port&&""==location.port||""==t.port&&"80"==location.port))},clearEmptyAttrs:function(e){for(var t in e)""===e[t]&&delete e[t];return e},str2json:function(e){return utils.isString(e)?window.JSON?JSON.parse(e):new Function("return "+utils.trim(e||""))():null},json2str:function(){if(window.JSON)return JSON.stringify;{var e={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};function t(e){return e<10?"0"+e:e}return function(i){switch(typeof i){case"undefined":return"undefined";case"number":return isFinite(i)?String(i):"null";case"string":return/["\\\x00-\x1f]/.test(l=i)&&(l=l.replace(/["\\\x00-\x1f]/g,function(t){var i=e[t];return i||(i=t.charCodeAt(),"\\u00"+Math.floor(i/16).toString(16)+(i%16).toString(16))})),'"'+l+'"';case"boolean":return String(i);default:if(null===i)return"null";if(utils.isArray(i))return function(e){var t,i,n,o=["["],r=e.length;for(i=0;i<r;i++)switch(typeof(n=e[i])){case"undefined":case"function":case"unknown":break;default:t&&o.push(","),o.push(utils.json2str(n)),t=1}return o.push("]"),o.join("")}(i);if(utils.isDate(i))return function(e){return'"'+e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+"T"+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+'"'}(i);var n,o,r=["{"],a=utils.json2str;for(var s in i)if(Object.prototype.hasOwnProperty.call(i,s))switch(typeof(o=i[s])){case"undefined":case"unknown":case"function":break;default:n&&r.push(","),n=1,r.push(a(s)+":"+a(o))}return r.push("}"),r.join("")}var l}}}()},test,cache;utils.each(["String","Function","Array","Number","RegExp","Object","Date"],function(e){UE.utils["is"+e]=function(t){return Object.prototype.toString.apply(t)=="[object "+e+"]"}});var EventBase=UE.EventBase=function(){};function getListener(e,t,i){var n;return t=t.toLowerCase(),(n=e.__allListeners||i&&(e.__allListeners={}))&&(n[t]||i&&(n[t]=[]))}EventBase.prototype={addListener:function(e,t){e=utils.trim(e).split(/\s+/);for(var i,n=0;i=e[n++];)getListener(this,i,!0).push(t)},on:function(e,t){return this.addListener(e,t)},off:function(e,t){return this.removeListener(e,t)},trigger:function(){return this.fireEvent.apply(this,arguments)},removeListener:function(e,t){e=utils.trim(e).split(/\s+/);for(var i,n=0;i=e[n++];)utils.removeItem(getListener(this,i)||[],t)},fireEvent:function(){var e=arguments[0];e=utils.trim(e).split(" ");for(var t,i=0;t=e[i++];){var n,o,r,a=getListener(this,t);if(a)for(r=a.length;r--;)if(a[r]){if(!0===(o=a[r].apply(this,arguments)))return o;void 0!==o&&(n=o)}(o=this["on"+t.toLowerCase()])&&(n=o.apply(this,arguments))}return n}};var dtd=dom.dtd=function(){function e(e){for(var t in e)e[t.toUpperCase()]=e[t];return e}var t=utils.extend2,i=e({isindex:1,fieldset:1}),n=e({input:1,button:1,select:1,textarea:1,label:1}),o=t(e({a:1}),n),r=t({iframe:1},o),a=e({hr:1,ul:1,menu:1,div:1,blockquote:1,noscript:1,table:1,center:1,address:1,dir:1,pre:1,h5:1,dl:1,h4:1,noframes:1,h6:1,ol:1,h1:1,h3:1,h2:1}),s=e({ins:1,del:1,script:1,style:1}),l=t(e({b:1,acronym:1,bdo:1,var:1,"#":1,abbr:1,code:1,br:1,i:1,cite:1,kbd:1,u:1,strike:1,s:1,tt:1,strong:1,q:1,samp:1,em:1,dfn:1,span:1}),s),d=t(e({sub:1,img:1,embed:1,object:1,sup:1,basefont:1,map:1,applet:1,font:1,big:1,small:1}),l),c=t(e({p:1}),d),u=t(e({iframe:1}),d,n),m=e({img:1,embed:1,noscript:1,br:1,kbd:1,center:1,button:1,basefont:1,h5:1,h4:1,samp:1,h6:1,ol:1,h1:1,h3:1,h2:1,form:1,font:1,"#":1,select:1,menu:1,ins:1,abbr:1,label:1,code:1,table:1,script:1,cite:1,input:1,iframe:1,strong:1,textarea:1,noframes:1,big:1,small:1,span:1,hr:1,sub:1,bdo:1,var:1,div:1,object:1,sup:1,strike:1,dir:1,map:1,dl:1,applet:1,del:1,isindex:1,fieldset:1,ul:1,b:1,acronym:1,a:1,blockquote:1,i:1,u:1,s:1,tt:1,address:1,q:1,pre:1,p:1,em:1,dfn:1}),f=t(e({a:0}),u),h=e({tr:1}),p=e({"#":1}),g=t(e({param:1}),m),v=t(e({form:1}),i,r,a,c),b=e({li:1,ol:1,ul:1}),y=e({style:1,script:1}),C=e({base:1,link:1,meta:1,title:1}),N=t(C,y),x=e({head:1,body:1}),w=e({html:1}),U=e({address:1,blockquote:1,center:1,dir:1,div:1,dl:1,fieldset:1,form:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1,hr:1,isindex:1,menu:1,noframes:1,ol:1,p:1,pre:1,table:1,ul:1}),E=e({area:1,base:1,basefont:1,br:1,col:1,command:1,dialog:1,embed:1,hr:1,img:1,input:1,isindex:1,keygen:1,link:1,meta:1,param:1,source:1,track:1,wbr:1});return e({$nonBodyContent:t(w,x,C),$block:U,$inline:f,$inlineWithA:t(e({a:1}),f),$body:t(e({script:1,style:1}),U),$cdata:e({script:1,style:1}),$empty:E,$nonChild:e({iframe:1,textarea:1}),$listItem:e({dd:1,dt:1,li:1}),$list:e({ul:1,ol:1,dl:1}),$isNotEmpty:e({table:1,ul:1,ol:1,dl:1,iframe:1,area:1,base:1,col:1,hr:1,img:1,embed:1,input:1,link:1,meta:1,param:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1}),$removeEmpty:e({a:1,abbr:1,acronym:1,address:1,b:1,bdo:1,big:1,cite:1,code:1,del:1,dfn:1,em:1,font:1,i:1,ins:1,label:1,kbd:1,q:1,s:1,samp:1,small:1,span:1,strike:1,strong:1,sub:1,sup:1,tt:1,u:1,var:1}),$removeEmptyBlock:e({p:1,div:1}),$tableContent:e({caption:1,col:1,colgroup:1,tbody:1,td:1,tfoot:1,th:1,thead:1,tr:1,table:1}),$notTransContent:e({pre:1,script:1,style:1,textarea:1}),html:x,head:N,style:p,script:p,body:v,base:{},link:{},meta:{},title:p,col:{},tr:e({td:1,th:1}),img:{},embed:{},colgroup:e({thead:1,col:1,tbody:1,tr:1,tfoot:1}),noscript:v,td:v,br:{},th:v,center:v,kbd:f,button:t(c,a),basefont:{},h5:f,h4:f,samp:f,h6:f,ol:b,h1:f,h3:f,option:p,h2:f,form:t(i,r,a,c),select:e({optgroup:1,option:1}),font:f,ins:f,menu:b,abbr:f,label:f,table:e({thead:1,col:1,tbody:1,tr:1,colgroup:1,caption:1,tfoot:1}),code:f,tfoot:h,cite:f,li:v,input:{},iframe:v,strong:f,textarea:p,noframes:v,big:f,small:f,span:e({"#":1,br:1,b:1,strong:1,u:1,i:1,em:1,sub:1,sup:1,strike:1,span:1}),hr:f,dt:f,sub:f,optgroup:e({option:1}),param:{},bdo:f,var:f,div:v,object:g,sup:f,dd:v,strike:f,area:{},dir:b,map:t(e({area:1,form:1,p:1}),i,s,a),applet:g,dl:e({dt:1,dd:1}),del:f,isindex:{},fieldset:t(e({legend:1}),m),thead:h,ul:b,acronym:f,b:f,a:t(e({a:1}),u),blockquote:t(e({td:1,tr:1,tbody:1,li:1}),v),caption:f,i:f,u:f,tbody:h,s:f,address:t(r,c),tt:f,legend:f,q:f,pre:t(l,o),p:t(e({a:1}),f),em:f,dfn:f})}();function getDomNode(e,t,i,n,o,r){var a,s=n&&e[t];for(!s&&(s=e[i]);!s&&(a=(a||e).parentNode);){if("BODY"==a.tagName||r&&!r(a))return null;s=a[i]}return s&&o&&!o(s)?getDomNode(s,t,i,!1,o):s}var attrFix=ie&&browser.version<9?{tabindex:"tabIndex",readonly:"readOnly",for:"htmlFor",class:"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder"}:{tabindex:"tabIndex",readonly:"readOnly"},styleBlock=utils.listToMap(["-webkit-box","-moz-box","block","list-item","table","table-row-group","table-header-group","table-footer-group","table-row","table-column-group","table-column","table-cell","table-caption"]),domUtils=dom.domUtils={NODE_ELEMENT:1,NODE_DOCUMENT:9,NODE_TEXT:3,NODE_COMMENT:8,NODE_DOCUMENT_FRAGMENT:11,POSITION_IDENTICAL:0,POSITION_DISCONNECTED:1,POSITION_FOLLOWING:2,POSITION_PRECEDING:4,POSITION_IS_CONTAINED:8,POSITION_CONTAINS:16,fillChar:ie&&"6"==browser.version?"\ufeff":"​",keys:{8:1,46:1,16:1,17:1,18:1,37:1,38:1,39:1,40:1,13:1},getPosition:function(e,t){if(e===t)return 0;var i,n=[e],o=[t];for(i=e;i=i.parentNode;){if(i===t)return 10;n.push(i)}for(i=t;i=i.parentNode;){if(i===e)return 20;o.push(i)}if(n.reverse(),o.reverse(),n[0]!==o[0])return 1;for(var r=-1;n[++r]===o[r];);for(e=n[r],t=o[r];e=e.nextSibling;)if(e===t)return 4;return 2},getNodeIndex:function(e,t){for(var i=e,n=0;i=i.previousSibling;)t&&3==i.nodeType?i.nodeType!=i.nextSibling.nodeType&&n++:n++;return n},inDoc:function(e,t){return 10==domUtils.getPosition(e,t)},findParent:function(e,t,i){if(e&&!domUtils.isBody(e))for(e=i?e:e.parentNode;e;){if(!t||t(e)||domUtils.isBody(e))return t&&!t(e)&&domUtils.isBody(e)?null:e;e=e.parentNode}return null},findParentByTagName:function(e,t,i,n){return t=utils.listToMap(utils.isArray(t)?t:[t]),domUtils.findParent(e,function(e){return t[e.tagName]&&!(n&&n(e))},i)},findParents:function(e,t,i,n){for(var o=t&&(i&&i(e)||!i)?[e]:[];e=domUtils.findParent(e,i);)o.push(e);return n?o:o.reverse()},insertAfter:function(e,t){return e.nextSibling?e.parentNode.insertBefore(t,e.nextSibling):e.parentNode.appendChild(t)},remove:function(e,t){var i,n=e.parentNode;if(n){if(t&&e.hasChildNodes())for(;i=e.firstChild;)n.insertBefore(i,e);n.removeChild(e)}return e},getNextDomNode:function(e,t,i,n){return getDomNode(e,"firstChild","nextSibling",t,i,n)},getPreDomNode:function(e,t,i,n){return getDomNode(e,"lastChild","previousSibling",t,i,n)},isBookmarkNode:function(e){return 1==e.nodeType&&e.id&&/^_baidu_bookmark_/i.test(e.id)},getWindow:function(e){var t=e.ownerDocument||e;return t.defaultView||t.parentWindow},getCommonAncestor:function(e,t){if(e===t)return e;for(var i=[e],n=[t],o=e,r=-1;o=o.parentNode;){if(o===t)return o;i.push(o)}for(o=t;o=o.parentNode;){if(o===e)return o;n.push(o)}for(i.reverse(),n.reverse();i[++r]===n[r];);return 0==r?null:i[r-1]},clearEmptySibling:function(e,t,i){function n(e,t){for(var i;e&&!domUtils.isBookmarkNode(e)&&(domUtils.isEmptyInlineElement(e)||!new RegExp("[^\t\n\r"+domUtils.fillChar+"]").test(e.nodeValue));)i=e[t],domUtils.remove(e),e=i}!t&&n(e.nextSibling,"nextSibling"),!i&&n(e.previousSibling,"previousSibling")},split:function(e,t){var i=e.ownerDocument;if(browser.ie&&t==e.nodeValue.length){var n=i.createTextNode("");return domUtils.insertAfter(e,n)}var o=e.splitText(t);if(browser.ie8){var r=i.createTextNode("");domUtils.insertAfter(o,r),domUtils.remove(r)}return o},isWhitespace:function(e){return!new RegExp("[^ \t\n\r"+domUtils.fillChar+"]").test(e.nodeValue)},getXY:function(e){for(var t=0,i=0;e.offsetParent;)i+=e.offsetTop,t+=e.offsetLeft,e=e.offsetParent;return{x:t,y:i}},on:function(e,t,i){var n=utils.isArray(t)?t:utils.trim(t).split(/\s+/),o=n.length;if(o)for(;o--;)if(t=n[o],e.addEventListener)e.addEventListener(t,i,!1);else{i._d||(i._d={els:[]});var r=t+i.toString(),a=utils.indexOf(i._d.els,e);i._d[r]&&-1!=a||(-1==a&&i._d.els.push(e),i._d[r]||(i._d[r]=function(e){return i.call(e.srcElement,e||window.event)}),e.attachEvent("on"+t,i._d[r]))}e=null},un:function(e,t,i){var n=utils.isArray(t)?t:utils.trim(t).split(/\s+/),o=n.length;if(o)for(;o--;)if(t=n[o],e.removeEventListener)e.removeEventListener(t,i,!1);else{var r=t+i.toString();try{e.detachEvent("on"+t,i._d?i._d[r]:i)}catch(e){}if(i._d&&i._d[r]){var a=utils.indexOf(i._d.els,e);-1!=a&&i._d.els.splice(a,1),0==i._d.els.length&&delete i._d[r]}}},isSameElement:function(e,t){if(e.tagName!=t.tagName)return!1;var i=e.attributes,n=t.attributes;if(!ie&&i.length!=n.length)return!1;for(var o,r,a=0,s=0,l=0;o=i[l++];){if("style"==o.nodeName){if(o.specified&&a++,domUtils.isSameStyle(e,t))continue;return!1}if(ie){if(!o.specified)continue;a++,r=n.getNamedItem(o.nodeName)}else r=t.attributes[o.nodeName];if(!r.specified||o.nodeValue!=r.nodeValue)return!1}if(ie){for(l=0;r=n[l++];)r.specified&&s++;if(a!=s)return!1}return!0},isSameStyle:function(e,t){var i=e.style.cssText.replace(/( ?; ?)/g,";").replace(/( ?: ?)/g,":"),n=t.style.cssText.replace(/( ?; ?)/g,";").replace(/( ?: ?)/g,":");if(browser.opera){if(i=e.style,n=t.style,i.length!=n.length)return!1;for(var o in i)if(!/^(\d+|csstext)$/i.test(o)&&i[o]!=n[o])return!1;return!0}if(!i||!n)return i==n;if(i=i.split(";"),n=n.split(";"),i.length!=n.length)return!1;for(var r,a=0;r=i[a++];)if(-1==utils.indexOf(n,r))return!1;return!0},isBlockElm:function(e){return 1==e.nodeType&&(dtd.$block[e.tagName]||styleBlock[domUtils.getComputedStyle(e,"display")])&&!dtd.$nonChild[e.tagName]},isBody:function(e){return e&&1==e.nodeType&&"body"==e.tagName.toLowerCase()},breakParent:function(e,t){var i,n,o,r=e,a=e;do{for(r=r.parentNode,n?((i=r.cloneNode(!1)).appendChild(n),n=i,(i=r.cloneNode(!1)).appendChild(o),o=i):o=(n=r.cloneNode(!1)).cloneNode(!1);i=a.previousSibling;)n.insertBefore(i,n.firstChild);for(;i=a.nextSibling;)o.appendChild(i);a=r}while(t!==r);return(i=t.parentNode).insertBefore(n,t),i.insertBefore(o,t),i.insertBefore(e,o),domUtils.remove(t),e},isEmptyInlineElement:function(e){if(1!=e.nodeType||!dtd.$removeEmpty[e.tagName])return 0;for(e=e.firstChild;e;){if(domUtils.isBookmarkNode(e))return 0;if(1==e.nodeType&&!domUtils.isEmptyInlineElement(e)||3==e.nodeType&&!domUtils.isWhitespace(e))return 0;e=e.nextSibling}return 1},trimWhiteTextNode:function(e){function t(t){for(var i;(i=e[t])&&3==i.nodeType&&domUtils.isWhitespace(i);)e.removeChild(i)}t("firstChild"),t("lastChild")},mergeChild:function(e,t,i){for(var n,o=domUtils.getElementsByTagName(e,e.tagName.toLowerCase()),r=0;n=o[r++];)if(n.parentNode&&!domUtils.isBookmarkNode(n))if("span"!=n.tagName.toLowerCase())domUtils.isSameElement(e,n)&&domUtils.remove(n,!0);else{if(e===n.parentNode&&(domUtils.trimWhiteTextNode(e),1==e.childNodes.length)){e.style.cssText=n.style.cssText+";"+e.style.cssText,domUtils.remove(n,!0);continue}if(n.style.cssText=e.style.cssText+";"+n.style.cssText,i){var a=i.style;if(a){a=a.split(";");for(var s,l=0;s=a[l++];)n.style[utils.cssStyleToDomStyle(s.split(":")[0])]=s.split(":")[1]}}domUtils.isSameStyle(n,e)&&domUtils.remove(n,!0)}},getElementsByTagName:function(e,t,i){if(i&&utils.isString(i)){var n=i;i=function(e){return domUtils.hasClass(e,n)}}t=utils.trim(t).replace(/[ ]{2,}/g," ").split(" ");for(var o,r=[],a=0;o=t[a++];)for(var s,l=e.getElementsByTagName(o),d=0;s=l[d++];)i&&!i(s)||r.push(s);return r},mergeToParent:function(e){for(var t=e.parentNode;t&&dtd.$removeEmpty[t.tagName];){if(t.tagName==e.tagName||"A"==t.tagName){if(domUtils.trimWhiteTextNode(t),"SPAN"==t.tagName&&!domUtils.isSameStyle(t,e)||"A"==t.tagName&&"SPAN"==e.tagName){if(t.childNodes.length>1||t!==e.parentNode){e.style.cssText=t.style.cssText+";"+e.style.cssText,t=t.parentNode;continue}t.style.cssText+=";"+e.style.cssText,"A"==t.tagName&&(t.style.textDecoration="underline")}if("A"!=t.tagName){t===e.parentNode&&domUtils.remove(e,!0);break}}t=t.parentNode}},mergeSibling:function(e,t,i){function n(e,t,i){var n;if((n=i[e])&&!domUtils.isBookmarkNode(n)&&1==n.nodeType&&domUtils.isSameElement(i,n)){for(;n.firstChild;)"firstChild"==t?i.insertBefore(n.lastChild,i.firstChild):i.appendChild(n.firstChild);domUtils.remove(n)}}!t&&n("previousSibling","firstChild",e),!i&&n("nextSibling","lastChild",e)},unSelectable:ie&&browser.ie9below||browser.opera?function(e){e.onselectstart=function(){return!1},e.onclick=e.onkeyup=e.onkeydown=function(){return!1},e.unselectable="on",e.setAttribute("unselectable","on");for(var t,i=0;t=e.all[i++];)switch(t.tagName.toLowerCase()){case"iframe":case"textarea":case"input":case"select":break;default:t.unselectable="on",e.setAttribute("unselectable","on")}}:function(e){e.style.MozUserSelect=e.style.webkitUserSelect=e.style.msUserSelect=e.style.KhtmlUserSelect="none"},removeAttributes:function(e,t){t=utils.isArray(t)?t:utils.trim(t).replace(/[ ]{2,}/g," ").split(" ");for(var i,n=0;i=t[n++];){switch(i=attrFix[i]||i){case"className":e[i]="";break;case"style":e.style.cssText="";var o=e.getAttributeNode("style");!browser.ie&&o&&e.removeAttributeNode(o)}e.removeAttribute(i)}},createElement:function(e,t,i){return domUtils.setAttributes(e.createElement(t),i)},setAttributes:function(e,t){for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];switch(i){case"class":e.className=n;break;case"style":e.style.cssText=e.style.cssText+";"+n;break;case"innerHTML":e[i]=n;break;case"value":e.value=n;break;default:e.setAttribute(attrFix[i]||i,n)}}return e},getComputedStyle:function(e,t){if("width height top left".indexOf(t)>-1)return e["offset"+t.replace(/^\w/,function(e){return e.toUpperCase()})]+"px";if(3==e.nodeType&&(e=e.parentNode),browser.ie&&browser.version<9&&"font-size"==t&&!e.style.fontSize&&!dtd.$empty[e.tagName]&&!dtd.$nonChild[e.tagName]){var i=e.ownerDocument.createElement("span");i.style.cssText="padding:0;border:0;font-family:simsun;",i.innerHTML=".",e.appendChild(i);var n=i.offsetHeight;return e.removeChild(i),i=null,n+"px"}try{var o=domUtils.getStyle(e,t)||(window.getComputedStyle?domUtils.getWindow(e).getComputedStyle(e,"").getPropertyValue(t):(e.currentStyle||e.style)[utils.cssStyleToDomStyle(t)])}catch(e){return""}return utils.transUnitToPx(utils.fixColor(t,o))},removeClasses:function(e,t){t=utils.isArray(t)?t:utils.trim(t).replace(/[ ]{2,}/g," ").split(" ");for(var i,n=0,o=e.className;i=t[n++];)o=o.replace(new RegExp("\\b"+i+"\\b"),"");(o=utils.trim(o).replace(/[ ]{2,}/g," "))?e.className=o:domUtils.removeAttributes(e,["class"])},addClass:function(e,t){if(e){t=utils.trim(t).replace(/[ ]{2,}/g," ").split(" ");for(var i,n=0,o=e.className;i=t[n++];)new RegExp("\\b"+i+"\\b").test(o)||(o+=" "+i);e.className=utils.trim(o)}},hasClass:function(e,t){if(utils.isRegExp(t))return t.test(e.className);t=utils.trim(t).replace(/[ ]{2,}/g," ").split(" ");for(var i,n=0,o=e.className;i=t[n++];)if(!new RegExp("\\b"+i+"\\b","i").test(o))return!1;return n-1==t.length},preventDefault:function(e){e.preventDefault?e.preventDefault():e.returnValue=!1},removeStyle:function(e,t){browser.ie?("color"==t&&(t="(^|;)"+t),e.style.cssText=e.style.cssText.replace(new RegExp(t+"[^:]*:[^;]+;?","ig"),"")):e.style.removeProperty?e.style.removeProperty(t):e.style.removeAttribute(utils.cssStyleToDomStyle(t)),e.style.cssText||domUtils.removeAttributes(e,["style"])},getStyle:function(e,t){var i=e.style[utils.cssStyleToDomStyle(t)];return utils.fixColor(t,i)},setStyle:function(e,t,i){e.style[utils.cssStyleToDomStyle(t)]=i,utils.trim(e.style.cssText)||this.removeAttributes(e,"style")},setStyles:function(e,t){for(var i in t)t.hasOwnProperty(i)&&domUtils.setStyle(e,i,t[i])},removeDirtyAttr:function(e){for(var t,i=0,n=e.getElementsByTagName("*");t=n[i++];)t.removeAttribute("_moz_dirty");e.removeAttribute("_moz_dirty")},getChildCount:function(e,t){var i=0,n=e.firstChild;for(t=t||function(){return 1};n;)t(n)&&i++,n=n.nextSibling;return i},isEmptyNode:function(e){return!e.firstChild||0==domUtils.getChildCount(e,function(e){return!domUtils.isBr(e)&&!domUtils.isBookmarkNode(e)&&!domUtils.isWhitespace(e)})},clearSelectedArr:function(e){for(var t;t=e.pop();)domUtils.removeAttributes(t,["class"])},scrollToView:function(e,t,i){var n,o,r=(n=t.document,o="CSS1Compat"==n.compatMode,{width:(o?n.documentElement.clientWidth:n.body.clientWidth)||0,height:(o?n.documentElement.clientHeight:n.body.clientHeight)||0}).height,a=-1*r+i;a+=e.offsetHeight||0,a+=domUtils.getXY(e).y;var s=function(e){if("pageXOffset"in e)return{x:e.pageXOffset||0,y:e.pageYOffset||0};var t=e.document;return{x:t.documentElement.scrollLeft||t.body.scrollLeft||0,y:t.documentElement.scrollTop||t.body.scrollTop||0}}(t).y;(a>s||a<s-r)&&t.scrollTo(0,a+(a<0?-20:20))},isBr:function(e){return 1==e.nodeType&&"BR"==e.tagName},isFillChar:function(e,t){if(3!=e.nodeType)return!1;var i=e.nodeValue;return t?new RegExp("^"+domUtils.fillChar).test(i):!i.replace(new RegExp(domUtils.fillChar,"g"),"").length},isStartInblock:function(e){var t,i=e.cloneRange(),n=0,o=i.startContainer;if(1==o.nodeType&&o.childNodes[i.startOffset])for(var r=(o=o.childNodes[i.startOffset]).previousSibling;r&&domUtils.isFillChar(r);)o=r,r=r.previousSibling;for(this.isFillChar(o,!0)&&1==i.startOffset&&(i.setStartBefore(o),o=i.startContainer);o&&domUtils.isFillChar(o);)t=o,o=o.previousSibling;for(t&&(i.setStartBefore(t),o=i.startContainer),1==o.nodeType&&domUtils.isEmptyNode(o)&&1==i.startOffset&&i.setStart(o,0).collapse(!0);!i.startOffset;){if(o=i.startContainer,domUtils.isBlockElm(o)||domUtils.isBody(o)){n=1;break}var a;if(r=i.startContainer.previousSibling){for(;r&&domUtils.isFillChar(r);)a=r,r=r.previousSibling;a?i.setStartBefore(a):i.setStartBefore(i.startContainer)}else i.setStartBefore(i.startContainer)}return n&&!domUtils.isBody(i.startContainer)?1:0},isEmptyBlock:function(e,t){if(1!=e.nodeType)return 0;if(t=t||new RegExp("[  \t\r\n"+domUtils.fillChar+"]","g"),e[browser.ie?"innerText":"textContent"].replace(t,"").length>0)return 0;for(var i in dtd.$isNotEmpty)if(e.getElementsByTagName(i).length)return 0;return 1},setViewportOffset:function(e,t){var i=0|parseInt(e.style.left),n=0|parseInt(e.style.top),o=e.getBoundingClientRect(),r=t.left-o.left,a=t.top-o.top;r&&(e.style.left=i+r+"px"),a&&(e.style.top=n+a+"px")},fillNode:function(e,t){var i=browser.ie?e.createTextNode(domUtils.fillChar):e.createElement("br");t.innerHTML="",t.appendChild(i)},moveChild:function(e,t,i){for(;e.firstChild;)i&&t.firstChild?t.insertBefore(e.lastChild,t.firstChild):t.appendChild(e.firstChild)},hasNoAttributes:function(e){return browser.ie?/^<\w+\s*?>/.test(e.outerHTML):0==e.attributes.length},isCustomeNode:function(e){return 1==e.nodeType&&e.getAttribute("_ue_custom_node_")},isTagNode:function(e,t){return 1==e.nodeType&&new RegExp("\\b"+e.tagName+"\\b","i").test(t)},filterNodeList:function(e,t,i){var n=[];if(!utils.isFunction(t)){var o=t;t=function(e){return-1!=utils.indexOf(utils.isArray(o)?o:o.split(" "),e.tagName.toLowerCase())}}return utils.each(e,function(e){t(e)&&n.push(e)}),0==n.length?null:1!=n.length&&i?n:n[0]},isInNodeEndBoundary:function(e,t){var i=e.startContainer;if(3==i.nodeType&&e.startOffset!=i.nodeValue.length)return 0;if(1==i.nodeType&&e.startOffset!=i.childNodes.length)return 0;for(;i!==t;){if(i.nextSibling)return 0;i=i.parentNode}return 1},isBoundaryNode:function(e,t){for(;!domUtils.isBody(e);)if(e!==(e=e.parentNode)[t])return!1;return!0},fillHtml:browser.ie11below?"&nbsp;":"<br/>"},fillCharReg=new RegExp(domUtils.fillChar,"g");!function(){var e,t=0,i=domUtils.fillChar;function n(e){return!e.collapsed&&1==e.startContainer.nodeType&&e.startContainer===e.endContainer&&e.endOffset-e.startOffset==1}function o(e,t,i,n){return 1==t.nodeType&&(dtd.$empty[t.tagName]||dtd.$nonChild[t.tagName])&&(i=domUtils.getNodeIndex(t)+(e?0:1),t=t.parentNode),e?(n.startContainer=t,n.startOffset=i,n.endContainer||n.collapse(!0)):(n.endContainer=t,n.endOffset=i,n.startContainer||n.collapse(!1)),function(e){e.collapsed=e.startContainer&&e.endContainer&&e.startContainer===e.endContainer&&e.startOffset==e.endOffset}(n),n}function r(e,t){var i,n,o=e.startContainer,r=e.endContainer,a=e.startOffset,s=e.endOffset,l=e.document,d=l.createDocumentFragment();if(1==o.nodeType&&(o=o.childNodes[a]||(i=o.appendChild(l.createTextNode("")))),1==r.nodeType&&(r=r.childNodes[s]||(n=r.appendChild(l.createTextNode("")))),o===r&&3==o.nodeType)return d.appendChild(l.createTextNode(o.substringData(a,s-a))),t&&(o.deleteData(a,s-a),e.collapse(!0)),d;for(var c,u,m=d,f=domUtils.findParents(o,!0),h=domUtils.findParents(r,!0),p=0;f[p]==h[p];)p++;for(var g,v=p;g=f[v];v++){for(c=g.nextSibling,g==o?i||(3==e.startContainer.nodeType?(m.appendChild(l.createTextNode(o.nodeValue.slice(a))),t&&o.deleteData(a,o.nodeValue.length-a)):m.appendChild(t?o:o.cloneNode(!0))):(u=g.cloneNode(!1),m.appendChild(u));c&&c!==r&&c!==h[v];)g=c.nextSibling,m.appendChild(t?c:c.cloneNode(!0)),c=g;m=u}m=d,f[p]||(m.appendChild(f[p-1].cloneNode(!1)),m=m.firstChild);var b;for(v=p;b=h[v];v++){if(c=b.previousSibling,b==r?n||3!=e.endContainer.nodeType||(m.appendChild(l.createTextNode(r.substringData(0,s))),t&&r.deleteData(0,s)):(u=b.cloneNode(!1),m.appendChild(u)),v!=p||!f[p])for(;c&&c!==o;)b=c.previousSibling,m.insertBefore(t?c:c.cloneNode(!0),m.firstChild),c=b;m=u}return t&&e.setStartBefore(h[p]?f[p]?h[p]:f[p-1]:h[p-1]).collapse(!0),i&&domUtils.remove(i),n&&domUtils.remove(n),d}var a=dom.Range=function(e){var t=this;t.startContainer=t.startOffset=t.endContainer=t.endOffset=null,t.document=e,t.collapsed=!0};function s(t,i){try{if(e&&domUtils.inDoc(e,t))if(e.nodeValue.replace(fillCharReg,"").length)e.nodeValue=e.nodeValue.replace(fillCharReg,"");else{var n=e.parentNode;for(domUtils.remove(e);n&&domUtils.isEmptyInlineElement(n)&&(browser.safari?!(domUtils.getPosition(n,i)&domUtils.POSITION_CONTAINS):!n.contains(i));)e=n.parentNode,domUtils.remove(n),n=e}}catch(e){}}function l(e,t){var i;for(e=e[t];e&&domUtils.isFillChar(e);)i=e[t],domUtils.remove(e),e=i}a.prototype={cloneContents:function(){return this.collapsed?null:r(this,0)},deleteContents:function(){var e;return this.collapsed||r(this,1),browser.webkit&&(3!=(e=this.startContainer).nodeType||e.nodeValue.length||(this.setStartBefore(e).collapse(!0),domUtils.remove(e))),this},extractContents:function(){return this.collapsed?null:r(this,2)},setStart:function(e,t){return o(!0,e,t,this)},setEnd:function(e,t){return o(!1,e,t,this)},setStartAfter:function(e){return this.setStart(e.parentNode,domUtils.getNodeIndex(e)+1)},setStartBefore:function(e){return this.setStart(e.parentNode,domUtils.getNodeIndex(e))},setEndAfter:function(e){return this.setEnd(e.parentNode,domUtils.getNodeIndex(e)+1)},setEndBefore:function(e){return this.setEnd(e.parentNode,domUtils.getNodeIndex(e))},setStartAtFirst:function(e){return this.setStart(e,0)},setStartAtLast:function(e){return this.setStart(e,3==e.nodeType?e.nodeValue.length:e.childNodes.length)},setEndAtFirst:function(e){return this.setEnd(e,0)},setEndAtLast:function(e){return this.setEnd(e,3==e.nodeType?e.nodeValue.length:e.childNodes.length)},selectNode:function(e){return this.setStartBefore(e).setEndAfter(e)},selectNodeContents:function(e){return this.setStart(e,0).setEndAtLast(e)},cloneRange:function(){var e=this;return new a(e.document).setStart(e.startContainer,e.startOffset).setEnd(e.endContainer,e.endOffset)},collapse:function(e){var t=this;return e?(t.endContainer=t.startContainer,t.endOffset=t.startOffset):(t.startContainer=t.endContainer,t.startOffset=t.endOffset),t.collapsed=!0,t},shrinkBoundary:function(e){var t,i=this,n=i.collapsed;function o(e){return 1==e.nodeType&&!domUtils.isBookmarkNode(e)&&!dtd.$empty[e.tagName]&&!dtd.$nonChild[e.tagName]}for(;1==i.startContainer.nodeType&&(t=i.startContainer.childNodes[i.startOffset])&&o(t);)i.setStart(t,0);if(n)return i.collapse(!0);if(!e)for(;1==i.endContainer.nodeType&&i.endOffset>0&&(t=i.endContainer.childNodes[i.endOffset-1])&&o(t);)i.setEnd(t,t.childNodes.length);return i},getCommonAncestor:function(e,t){var i=this.startContainer,o=this.endContainer;return i===o?e&&n(this)&&1==(i=i.childNodes[this.startOffset]).nodeType?i:t&&3==i.nodeType?i.parentNode:i:domUtils.getCommonAncestor(i,o)},trimBoundary:function(e){this.txtToElmBoundary();var t=this.startContainer,i=this.startOffset,n=this.collapsed,o=this.endContainer;if(3==t.nodeType){if(0==i)this.setStartBefore(t);else if(i>=t.nodeValue.length)this.setStartAfter(t);else{var r=domUtils.split(t,i);t===o?this.setEnd(r,this.endOffset-i):t.parentNode===o&&(this.endOffset+=1),this.setStartBefore(r)}if(n)return this.collapse(!0)}return e||(i=this.endOffset,3==(o=this.endContainer).nodeType&&(0==i?this.setEndBefore(o):(i<o.nodeValue.length&&domUtils.split(o,i),this.setEndAfter(o)))),this},txtToElmBoundary:function(e){function t(e,t){var i=e[t+"Container"],n=e[t+"Offset"];3==i.nodeType&&(n?n>=i.nodeValue.length&&e["set"+t.replace(/(\w)/,function(e){return e.toUpperCase()})+"After"](i):e["set"+t.replace(/(\w)/,function(e){return e.toUpperCase()})+"Before"](i))}return!e&&this.collapsed||(t(this,"start"),t(this,"end")),this},insertNode:function(e){var t=e,i=1;11==e.nodeType&&(t=e.firstChild,i=e.childNodes.length),this.trimBoundary(!0);var n=this.startContainer,o=this.startOffset,r=n.childNodes[o];return r?n.insertBefore(e,r):n.appendChild(e),t.parentNode===this.endContainer&&(this.endOffset=this.endOffset+i),this.setStartBefore(t)},setCursor:function(e,t){return this.collapse(!e).select(t)},createBookmark:function(e,i){var n,o=this.document.createElement("span");return o.style.cssText="display:none;line-height:0px;",o.appendChild(this.document.createTextNode("‍")),o.id="_baidu_bookmark_start_"+(i?"":t++),this.collapsed||((n=o.cloneNode(!0)).id="_baidu_bookmark_end_"+(i?"":t++)),this.insertNode(o),n&&this.collapse().insertNode(n).setEndBefore(n),this.setStartAfter(o),{start:e?o.id:o,end:n?e?n.id:n:null,id:e}},moveToBookmark:function(e){var t=e.id?this.document.getElementById(e.start):e.start,i=e.end&&e.id?this.document.getElementById(e.end):e.end;return this.setStartBefore(t),domUtils.remove(t),i?(this.setEndBefore(i),domUtils.remove(i)):this.collapse(!0),this},enlarge:function(e,t){var i,n,o=domUtils.isBody,r=this.document.createTextNode("");if(e){for(1==(n=this.startContainer).nodeType?n.childNodes[this.startOffset]?i=n=n.childNodes[this.startOffset]:(n.appendChild(r),i=n=r):i=n;;){if(domUtils.isBlockElm(n)){for(n=i;(i=n.previousSibling)&&!domUtils.isBlockElm(i);)n=i;this.setStartBefore(n);break}i=n,n=n.parentNode}for(1==(n=this.endContainer).nodeType?((i=n.childNodes[this.endOffset])?n.insertBefore(r,i):n.appendChild(r),i=n=r):i=n;;){if(domUtils.isBlockElm(n)){for(n=i;(i=n.nextSibling)&&!domUtils.isBlockElm(i);)n=i;this.setEndAfter(n);break}i=n,n=n.parentNode}r.parentNode===this.endContainer&&this.endOffset--,domUtils.remove(r)}if(!this.collapsed){for(;!(0!=this.startOffset||t&&t(this.startContainer)||o(this.startContainer));)this.setStartBefore(this.startContainer);for(;!(this.endOffset!=(1==this.endContainer.nodeType?this.endContainer.childNodes.length:this.endContainer.nodeValue.length)||t&&t(this.endContainer)||o(this.endContainer));)this.setEndAfter(this.endContainer)}return this},enlargeToBlockElm:function(e){for(;!domUtils.isBlockElm(this.startContainer);)this.setStartBefore(this.startContainer);if(!e)for(;!domUtils.isBlockElm(this.endContainer);)this.setEndAfter(this.endContainer);return this},adjustmentBoundary:function(){if(!this.collapsed){for(;!domUtils.isBody(this.startContainer)&&this.startOffset==this.startContainer[3==this.startContainer.nodeType?"nodeValue":"childNodes"].length&&this.startContainer[3==this.startContainer.nodeType?"nodeValue":"childNodes"].length;)this.setStartAfter(this.startContainer);for(;!domUtils.isBody(this.endContainer)&&!this.endOffset&&this.endContainer[3==this.endContainer.nodeType?"nodeValue":"childNodes"].length;)this.setEndBefore(this.endContainer)}return this},applyInlineStyle:function(e,t,i){if(this.collapsed)return this;this.trimBoundary().enlarge(!1,function(e){return 1==e.nodeType&&domUtils.isBlockElm(e)}).adjustmentBoundary();for(var n,o,r=this.createBookmark(),a=r.end,s=function(e){return 1==e.nodeType?"br"!=e.tagName.toLowerCase():!domUtils.isWhitespace(e)},l=domUtils.getNextDomNode(r.start,!1,s),d=this.cloneRange();l&&domUtils.getPosition(l,a)&domUtils.POSITION_PRECEDING;)if(3==l.nodeType||dtd[e][l.tagName]){for(d.setStartBefore(l),n=l;n&&(3==n.nodeType||dtd[e][n.tagName])&&n!==a;)o=n,n=domUtils.getNextDomNode(n,1==n.nodeType,null,function(t){return dtd[e][t.tagName]});var c,u,m=d.setEndAfter(o).extractContents();if(i&&i.length>0){var f,h;h=f=i[0].cloneNode(!1);for(var p,g=1;p=i[g++];)f.appendChild(p.cloneNode(!1)),f=f.firstChild;c=f}else c=d.document.createElement(e);if(t&&domUtils.setAttributes(c,t),c.appendChild(m),d.insertNode(i?h:c),"span"==e&&t.style&&/text\-decoration/.test(t.style)&&(u=domUtils.findParentByTagName(c,"a",!0))?(domUtils.setAttributes(u,t),domUtils.remove(c,!0),c=u):(domUtils.mergeSibling(c),domUtils.clearEmptySibling(c)),domUtils.mergeChild(c,t),l=domUtils.getNextDomNode(c,!1,s),domUtils.mergeToParent(c),n===a)break}else l=domUtils.getNextDomNode(l,!0,s);return this.moveToBookmark(r)},removeInlineStyle:function(e){if(this.collapsed)return this;e=utils.isArray(e)?e:[e],this.shrinkBoundary().adjustmentBoundary();for(var t=this.startContainer,i=this.endContainer;;){if(1==t.nodeType){if(utils.indexOf(e,t.tagName.toLowerCase())>-1)break;if("body"==t.tagName.toLowerCase()){t=null;break}}t=t.parentNode}for(;;){if(1==i.nodeType){if(utils.indexOf(e,i.tagName.toLowerCase())>-1)break;if("body"==i.tagName.toLowerCase()){i=null;break}}i=i.parentNode}var n,o,r=this.createBookmark();t&&(n=(o=this.cloneRange().setEndBefore(r.start).setStartBefore(t)).extractContents(),o.insertNode(n),domUtils.clearEmptySibling(t,!0),t.parentNode.insertBefore(r.start,t)),i&&(n=(o=this.cloneRange().setStartAfter(r.end).setEndAfter(i)).extractContents(),o.insertNode(n),domUtils.clearEmptySibling(i,!1,!0),i.parentNode.insertBefore(r.end,i.nextSibling));for(var a,s=domUtils.getNextDomNode(r.start,!1,function(e){return 1==e.nodeType});s&&s!==r.end;)a=domUtils.getNextDomNode(s,!0,function(e){return 1==e.nodeType}),utils.indexOf(e,s.tagName.toLowerCase())>-1&&domUtils.remove(s,!0),s=a;return this.moveToBookmark(r)},getClosedNode:function(){var e;if(!this.collapsed){var t=this.cloneRange().adjustmentBoundary().shrinkBoundary();if(n(t)){var i=t.startContainer.childNodes[t.startOffset];i&&1==i.nodeType&&(dtd.$empty[i.tagName]||dtd.$nonChild[i.tagName])&&(e=i)}}return e},select:browser.ie?function(t,n){var o;this.collapsed||this.shrinkBoundary();var r=this.getClosedNode();if(r&&!n){try{(o=this.document.body.createControlRange()).addElement(r),o.select()}catch(e){}return this}var a,d=this.createBookmark(),c=d.start;if((o=this.document.body.createTextRange()).moveToElementText(c),o.moveStart("character",1),this.collapsed){if(!t&&3!=this.startContainer.nodeType){var u=this.document.createTextNode(i),m=this.document.createElement("span");m.appendChild(this.document.createTextNode(i)),c.parentNode.insertBefore(m,c),c.parentNode.insertBefore(u,c),s(this.document,u),e=u,l(m,"previousSibling"),l(c,"nextSibling"),o.moveStart("character",-1),o.collapse(!0)}}else{var f=this.document.body.createTextRange();a=d.end,f.moveToElementText(a),o.setEndPoint("EndToEnd",f)}this.moveToBookmark(d),m&&domUtils.remove(m);try{o.select()}catch(e){}return this}:function(t){var n,o=domUtils.getWindow(this.document),r=o.getSelection();if(browser.gecko?this.document.body.focus():o.focus(),r){if(r.removeAllRanges(),this.collapsed&&!t){var a=this.startContainer,d=a;1==a.nodeType&&(d=a.childNodes[this.startOffset]),3==a.nodeType&&this.startOffset||(d?d.previousSibling&&3==d.previousSibling.nodeType:a.lastChild&&3==a.lastChild.nodeType)||(n=this.document.createTextNode(i),this.insertNode(n),s(this.document,n),l(n,"previousSibling"),l(n,"nextSibling"),e=n,this.setStart(n,browser.webkit?1:0).collapse(!0))}var c=this.document.createRange();if(this.collapsed&&browser.opera&&1==this.startContainer.nodeType)if(d=this.startContainer.childNodes[this.startOffset]){for(;d&&domUtils.isBlockElm(d)&&1==d.nodeType&&d.childNodes[0];)d=d.childNodes[0];d&&this.setStartBefore(d).collapse(!0)}else(d=this.startContainer.lastChild)&&domUtils.isBr(d)&&this.setStartBefore(d).collapse(!0);!function(e){function t(t,i,n){3==t.nodeType&&t.nodeValue.length<i&&(e[n+"Offset"]=t.nodeValue.length)}t(e.startContainer,e.startOffset,"start"),t(e.endContainer,e.endOffset,"end")}(this),c.setStart(this.startContainer,this.startOffset),c.setEnd(this.endContainer,this.endOffset),r.addRange(c)}return this},scrollToView:function(e,t){e=e?window:domUtils.getWindow(this.document);var i=this.document.createElement("span");return i.innerHTML="&nbsp;",this.cloneRange().insertNode(i),domUtils.scrollToView(i,e,t),domUtils.remove(i),this},inFillChar:function(){var e=this.startContainer;return!(!this.collapsed||3!=e.nodeType||e.nodeValue.replace(new RegExp("^"+domUtils.fillChar),"").length+1!=e.nodeValue.length)},createAddress:function(e,t){var i={},n=this;function o(e){for(var i,o=e?n.startContainer:n.endContainer,r=domUtils.findParents(o,!0,function(e){return!domUtils.isBody(e)}),a=[],s=0;i=r[s++];)a.push(domUtils.getNodeIndex(i,t));var l=0;if(t)if(3==o.nodeType){for(var d=o.previousSibling;d&&3==d.nodeType;)l+=d.nodeValue.replace(fillCharReg,"").length,d=d.previousSibling;l+=e?n.startOffset:n.endOffset}else if(o=o.childNodes[e?n.startOffset:n.endOffset])l=domUtils.getNodeIndex(o,t);else for(var c=(o=e?n.startContainer:n.endContainer).firstChild;c;)if(domUtils.isFillChar(c))c=c.nextSibling;else if(l++,3==c.nodeType)for(;c&&3==c.nodeType;)c=c.nextSibling;else c=c.nextSibling;else l=e?domUtils.isFillChar(o)?0:n.startOffset:n.endOffset;return l<0&&(l=0),a.push(l),a}return i.startAddress=o(!0),e||(i.endAddress=n.collapsed?[].concat(i.startAddress):o()),i},moveToAddress:function(e,t){var i=this;function n(e,t){for(var n,o,r,a=i.document.body,s=0,l=e.length;s<l;s++)if(r=e[s],n=a,!(a=a.childNodes[r])){o=r;break}t?a?i.setStartBefore(a):i.setStart(n,o):a?i.setEndBefore(a):i.setEnd(n,o)}return n(e.startAddress,!0),!t&&e.endAddress&&n(e.endAddress),i},equals:function(e){for(var t in this)if(this.hasOwnProperty(t)&&this[t]!==e[t])return!1;return!0},traversal:function(e,t){if(this.collapsed)return this;for(var i=this.createBookmark(),n=i.end,o=domUtils.getNextDomNode(i.start,!1,t);o&&o!==n&&domUtils.getPosition(o,n)&domUtils.POSITION_PRECEDING;){var r=domUtils.getNextDomNode(o,!1,t);e(o),o=r}return this.moveToBookmark(i)}}}(),function(){function e(e,t){var i=domUtils.getNodeIndex;(e=e.duplicate()).collapse(t);var n=e.parentElement();if(!n.hasChildNodes())return{container:n,offset:0};for(var o,r,a=n.children,s=e.duplicate(),l=0,d=a.length-1,c=-1;l<=d;){o=a[c=Math.floor((l+d)/2)],s.moveToElementText(o);var u=s.compareEndPoints("StartToStart",e);if(u>0)d=c-1;else{if(!(u<0))return{container:n,offset:i(o)};l=c+1}}if(-1==c){if(s.moveToElementText(n),s.setEndPoint("StartToStart",e),r=s.text.replace(/(\r\n|\r)/g,"\n").length,a=n.childNodes,!r)return{container:o=a[a.length-1],offset:o.nodeValue.length};for(var m=a.length;r>0;)r-=a[--m].nodeValue.length;return{container:a[m],offset:-r}}if(s.collapse(u>0),s.setEndPoint(u>0?"StartToStart":"EndToStart",e),!(r=s.text.replace(/(\r\n|\r)/g,"\n").length))return dtd.$empty[o.tagName]||dtd.$nonChild[o.tagName]?{container:n,offset:i(o)+(u>0?0:1)}:{container:o,offset:u>0?0:o.childNodes.length};for(;r>0;)try{var f=o;r-=(o=o[u>0?"previousSibling":"nextSibling"]).nodeValue.length}catch(e){return{container:n,offset:i(f)}}return{container:o,offset:u>0?-r:o.nodeValue.length+r}}function t(e){var t;try{t=e.getNative().createRange()}catch(e){return null}var i=t.item?t.item(0):t.parentElement();return(i.ownerDocument||i)===e.document?t:null}(dom.Selection=function(e){var i,n=this;n.document=e,browser.ie9below&&(i=domUtils.getWindow(e).frameElement,domUtils.on(i,"beforedeactivate",function(){n._bakIERange=n.getIERange()}),domUtils.on(i,"activate",function(){try{!t(n)&&n._bakIERange&&n._bakIERange.select()}catch(e){}n._bakIERange=null})),i=e=null}).prototype={rangeInBody:function(e,t){var i=browser.ie9below||t?e.item?e.item():e.parentElement():e.startContainer;return i===this.document.body||domUtils.inDoc(i,this.document)},getNative:function(){var e=this.document;try{return e?browser.ie9below?e.selection:domUtils.getWindow(e).getSelection():null}catch(e){return null}},getIERange:function(){var e=t(this);return!e&&this._bakIERange?this._bakIERange:e},cache:function(){this.clear(),this._cachedRange=this.getRange(),this._cachedStartElement=this.getStart(),this._cachedStartElementPath=this.getStartElementPath()},getStartElementPath:function(){if(this._cachedStartElementPath)return this._cachedStartElementPath;var e=this.getStart();return e?domUtils.findParents(e,!0,null,!0):[]},clear:function(){this._cachedStartElementPath=this._cachedRange=this._cachedStartElement=null},isFocus:function(){try{if(browser.ie9below){var e=t(this);return!(!e||!this.rangeInBody(e))}return!!this.getNative().rangeCount}catch(e){return!1}},getRange:function(){var t=this;function i(e){for(var i=t.document.body.firstChild,n=e.collapsed;i&&i.firstChild;)e.setStart(i,0),i=i.firstChild;e.startContainer||e.setStart(t.document.body,0),n&&e.collapse(!0)}if(null!=t._cachedRange)return this._cachedRange;var n=new baidu.editor.dom.Range(t.document);if(browser.ie9below){var o=t.getIERange();if(o)try{!function(t,i){if(t.item)i.selectNode(t.item(0));else{var n=e(t,!0);i.setStart(n.container,n.offset),0!=t.compareEndPoints("StartToEnd",t)&&(n=e(t,!1),i.setEnd(n.container,n.offset))}}(o,n)}catch(e){i(n)}else i(n)}else{var r=t.getNative();if(r&&r.rangeCount){var a=r.getRangeAt(0),s=r.getRangeAt(r.rangeCount-1);n.setStart(a.startContainer,a.startOffset).setEnd(s.endContainer,s.endOffset),n.collapsed&&domUtils.isBody(n.startContainer)&&!n.startOffset&&i(n)}else{if(this._bakRange&&domUtils.inDoc(this._bakRange.startContainer,this.document))return this._bakRange;i(n)}}return this._bakRange=n},getStart:function(){if(this._cachedStartElement)return this._cachedStartElement;var e,t,i,n,o=browser.ie9below?this.getIERange():this.getRange();if(browser.ie9below){if(!o)return this.document.body.firstChild;if(o.item)return o.item(0);for((e=o.duplicate()).text.length>0&&e.moveStart("character",1),e.collapse(1),t=e.parentElement(),n=i=o.parentElement();i=i.parentNode;)if(i==t){t=n;break}}else if(o.shrinkBoundary(),1==(t=o.startContainer).nodeType&&t.hasChildNodes()&&(t=t.childNodes[Math.min(t.childNodes.length-1,o.startOffset)]),3==t.nodeType)return t.parentNode;return t},getText:function(){var e,t;return this.isFocus()&&(e=this.getNative())?(t=browser.ie9below?e.createRange():e.getRangeAt(0),browser.ie9below?t.text:t.toString()):""},clearRange:function(){this.getNative()[browser.ie9below?"empty":"removeAllRanges"]()}}}(),function(){var e,t=0;function i(e,t){var i;if(t.textarea)if(utils.isString(t.textarea)){for(var n,o=0,r=domUtils.getElementsByTagName(e,"textarea");n=r[o++];)if(n.id=="ueditor_textarea_"+t.options.textarea){i=n;break}}else i=t.textarea;i||(e.appendChild(i=domUtils.createElement(document,"textarea",{name:t.options.textarea,id:"ueditor_textarea_"+t.options.textarea,style:"display:none"})),t.textarea=i),!i.getAttribute("name")&&i.setAttribute("name",t.options.textarea),i.value=t.hasContents()?t.options.allHtmlEnabled?t.getAllHtml():t.getContent(null,null,!0):""}var n=UE.Editor=function(e){var i=this;i.uid=t++,EventBase.call(i),i.commands={},i.options=utils.extend(utils.clone(e||{}),UEDITOR_CONFIG,!0),i.shortcutkeys={},i.inputRules=[],i.outputRules=[],i.setOpt(n.defaultOptions(i)),i.loadServerConfig(),utils.isEmptyObject(UE.I18N)||(i.options.lang=function(e){for(var t in e)return t}(UE.I18N),UE.plugin.load(i),function(e){e.langIsReady=!0,e.fireEvent("langReady")}(i)),UE.instants["ueditorInstant"+i.uid]=i};n.prototype={registerCommand:function(e,t){this.commands[e]=t},ready:function(e){e&&(this.isReady?e.apply(this):this.addListener("ready",e))},setOpt:function(e,t){var i={};utils.isString(e)?i[e]=t:i=e,utils.extend(this.options,i,!0)},getOpt:function(e){return this.options[e]},destroy:function(){var e=this;e.fireEvent("destroy");var t=e.container.parentNode,i=e.textarea;i?i.style.display="":(i=document.createElement("textarea"),t.parentNode.insertBefore(i,t)),i.style.width=e.iframe.offsetWidth+"px",i.style.height=e.iframe.offsetHeight+"px",i.value=e.getContent(),i.id=e.key,t.innerHTML="",domUtils.remove(t);var n=e.key;for(var o in e)e.hasOwnProperty(o)&&delete this[o];UE.delEditor(n)},render:function(e){var t=this.options,i=function(t){return parseInt(domUtils.getComputedStyle(e,t))};if(utils.isString(e)&&(e=document.getElementById(e)),e){t.initialFrameWidth?t.minFrameWidth=t.initialFrameWidth:t.minFrameWidth=t.initialFrameWidth=e.offsetWidth,t.initialFrameHeight?t.minFrameHeight=t.initialFrameHeight:t.initialFrameHeight=t.minFrameHeight=e.offsetHeight,e.style.width=/%$/.test(t.initialFrameWidth)?"100%":t.initialFrameWidth-i("padding-left")-i("padding-right")+"px",e.style.height=/%$/.test(t.initialFrameHeight)?"100%":t.initialFrameHeight-i("padding-top")-i("padding-bottom")+"px",e.style.zIndex=t.zIndex;var n=(ie&&browser.version<9?"":"<!DOCTYPE html>")+"<html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}\nbody{margin:8px;font-family:sans-serif;font-size:16px;}p{margin:5px 0;}</style>"+(t.iframeCssUrl?"<link rel='stylesheet' type='text/css' href='"+utils.unhtml(t.iframeCssUrl)+"'/>":"")+(t.initialStyle?"<style>"+t.initialStyle+"</style>":"")+"</head><body class='view' ></body><script type='text/javascript' "+(ie?"defer='defer'":"")+" id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant"+this.uid+"'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);<\/script></html>";e.appendChild(domUtils.createElement(document,"iframe",{id:"ueditor_"+this.uid,width:"100%",height:"100%",frameborder:"0",src:"javascript:void(function(){document.open();"+(t.customDomain&&document.domain!=location.hostname?'document.domain="'+document.domain+'";':"")+'document.write("'+n+'");document.close();}())'})),e.style.overflow="hidden",setTimeout(function(){/%$/.test(t.initialFrameWidth)&&(t.minFrameWidth=t.initialFrameWidth=e.offsetWidth),/%$/.test(t.initialFrameHeight)&&(t.minFrameHeight=t.initialFrameHeight=e.offsetHeight,e.style.height=t.initialFrameHeight+"px")})}},_setup:function(e){var t,n=this,o=n.options;ie?(e.body.disabled=!0,e.body.contentEditable=!0,e.body.disabled=!1):e.body.contentEditable=!0,e.body.spellcheck=!1,n.document=e,n.window=e.defaultView||e.parentWindow,n.iframe=n.window.frameElement,n.body=e.body,n.selection=new dom.Selection(e),browser.gecko&&(t=this.selection.getNative())&&t.removeAllRanges(),this._initEvents();for(var r=this.iframe.parentNode;!domUtils.isBody(r);r=r.parentNode)if("FORM"==r.tagName){n.form=r,n.options.autoSyncData?domUtils.on(n.window,"blur",function(){i(r,n)}):domUtils.on(r,"submit",function(){i(this,n)});break}if(o.initialContent)if(o.autoClearinitialContent){var a=n.execCommand;n.execCommand=function(){return n.fireEvent("firstBeforeExecCommand"),a.apply(n,arguments)},this._setDefaultContent(o.initialContent)}else this.setContent(o.initialContent,!1,!0);domUtils.isEmptyNode(n.body)&&(n.body.innerHTML=""),o.focus&&setTimeout(function(){n.focus(n.options.focusInEnd),!n.options.autoClearinitialContent&&n._selectionChange()},0),n.container||(n.container=this.iframe.parentNode),o.fullscreen&&n.ui&&n.ui.setFullScreen(!0);try{n.document.execCommand("2D-position",!1,!1)}catch(e){}try{n.document.execCommand("enableInlineTableEditing",!1,!1)}catch(e){}try{n.document.execCommand("enableObjectResizing",!1,!1)}catch(e){}n._bindshortcutKeys(),n.isReady=1,n.fireEvent("ready"),o.onready&&o.onready.call(n),browser.ie9below||domUtils.on(n.window,["blur","focus"],function(e){if("blur"==e.type){n._bakRange=n.selection.getRange();try{n._bakNativeRange=n.selection.getNative().getRangeAt(0),n.selection.getNative().removeAllRanges()}catch(e){n._bakNativeRange=null}}else try{n._bakRange&&n._bakRange.select()}catch(e){}}),browser.gecko&&browser.version<=10902&&(n.body.contentEditable=!1,setTimeout(function(){n.body.contentEditable=!0},100),setInterval(function(){n.body.style.height=n.iframe.offsetHeight-20+"px"},100)),!o.isShow&&n.setHide(),o.readonly&&n.setDisabled()},sync:function(e){var t=e?document.getElementById(e):domUtils.findParent(this.iframe.parentNode,function(e){return"FORM"==e.tagName},!0);t&&i(t,this)},setHeight:function(e,t){e!==parseInt(this.iframe.parentNode.style.height)&&(this.iframe.parentNode.style.height=e+"px"),!t&&(this.options.minFrameHeight=this.options.initialFrameHeight=e),this.body.style.height=e+"px",!t&&this.trigger("setHeight")},addshortcutkey:function(e,t){var i={};t?i[e]=t:i=e,utils.extend(this.shortcutkeys,i)},_bindshortcutKeys:function(){var e=this,t=this.shortcutkeys;e.addListener("keydown",function(i,n){var o=n.keyCode||n.which;for(var r in t)for(var a,s=t[r].split(","),l=0;a=s[l++];){var d=(a=a.split(":"))[0],c=a[1];(/^(ctrl)(\+shift)?\+(\d+)$/.test(d.toLowerCase())||/^(\d+)$/.test(d))&&("ctrl"==RegExp.$1&&(n.ctrlKey||n.metaKey)&&(""==RegExp.$2||n[RegExp.$2.slice(1)+"Key"])&&o==RegExp.$3||o==RegExp.$1)&&(-1!=e.queryCommandState(r,c)&&e.execCommand(r,c),domUtils.preventDefault(n))}})},getContent:function(e,t,i,n,o){if(e&&utils.isFunction(e)&&(t=e,e=""),t?!t():!this.hasContents())return"";this.fireEvent("beforegetcontent");var r=UE.htmlparser(this.body.innerHTML,n);return this.filterOutputRule(r),this.fireEvent("aftergetcontent",e,r),r.toHtml(o)},getAllHtml:function(){var e=this,t=[];if(e.fireEvent("getAllHtml",t),browser.ie&&browser.version>8){var i="";utils.each(e.document.styleSheets,function(e){i+=e.href?'<link rel="stylesheet" type="text/css" href="'+e.href+'" />':"<style>"+e.cssText+"</style>"}),utils.each(e.document.getElementsByTagName("script"),function(e){i+=e.outerHTML})}return"<html><head>"+(e.options.charset?'<meta http-equiv="Content-Type" content="text/html; charset='+e.options.charset+'"/>':"")+(i||e.document.getElementsByTagName("head")[0].innerHTML)+t.join("\n")+"</head><body "+(ie&&browser.version<9?'class="view"':"")+">"+e.getContent(null,null,!0)+"</body></html>"},getPlainTxt:function(){var e=new RegExp(domUtils.fillChar,"g"),t=this.body.innerHTML.replace(/[\n\r]/g,"");return(t=t.replace(/<(p|div)[^>]*>(<br\/?>|&nbsp;)<\/\1>/gi,"\n").replace(/<br\/?>/gi,"\n").replace(/<[^>/]+>/g,"").replace(/(\n)?<\/([^>]+)>/g,function(e,t,i){return dtd.$block[i]?"\n":t||""})).replace(e,"").replace(/\u00a0/g," ").replace(/&nbsp;/g," ")},getContentTxt:function(){var e=new RegExp(domUtils.fillChar,"g");return this.body[browser.ie?"innerText":"textContent"].replace(e,"").replace(/\u00a0/g," ")},setContent:function(e,t,n){var o=this;o.fireEvent("beforesetcontent",e);var r,a,s=UE.htmlparser(e);if(o.filterInputRule(s),e=s.toHtml(),o.body.innerHTML=(t?o.body.innerHTML:"")+e,"p"==o.options.enterTag){var l,d=this.body.firstChild;if(!d||1==d.nodeType&&(dtd.$cdata[d.tagName]||"DIV"==(r=d).tagName&&r.getAttribute("cdata_tag")||domUtils.isCustomeNode(d))&&d===this.body.lastChild)this.body.innerHTML=""+this.body.innerHTML;else for(var c=o.document.createElement("p");d;){for(;d&&(3==d.nodeType||1==d.nodeType&&dtd.p[d.tagName]&&!dtd.$cdata[d.tagName]);)l=d.nextSibling,c.appendChild(d),d=l;if(c.firstChild){if(!d){o.body.appendChild(c);break}d.parentNode.insertBefore(c,d),c=o.document.createElement("p")}d=d.nextSibling}}o.fireEvent("aftersetcontent"),o.fireEvent("contentchange"),!n&&o._selectionChange(),o._bakRange=o._bakIERange=o._bakNativeRange=null,browser.gecko&&(a=this.selection.getNative())&&a.removeAllRanges(),o.options.autoSyncData&&o.form&&i(o.form,o)},focus:function(e){try{var t=this.selection.getRange();if(e){(i=this.body.lastChild)&&1==i.nodeType&&!dtd.$empty[i.tagName]&&(domUtils.isEmptyBlock(i)?t.setStartAtFirst(i):t.setStartAtLast(i),t.collapse(!0)),t.setCursor(!0)}else{var i;if(!t.collapsed&&domUtils.isBody(t.startContainer)&&0==t.startOffset)(i=this.body.firstChild)&&1==i.nodeType&&!dtd.$empty[i.tagName]&&t.setStartAtFirst(i).collapse(!0);t.select(!0)}this.fireEvent("focus selectionchange")}catch(e){}},isFocus:function(){return this.selection.isFocus()},blur:function(){var e=this.selection.getNative();if(e.empty&&browser.ie){var t=document.body.createTextRange();t.moveToElementText(document.body),t.collapse(!0),t.select(),e.empty()}else e.removeAllRanges()},_initEvents:function(){var e=this,t=e.document,i=e.window;e._proxyDomEvent=utils.bind(e._proxyDomEvent,e),domUtils.on(t,["click","contextmenu","mousedown","keydown","keyup","keypress","mouseup","mouseover","mouseout","selectstart"],e._proxyDomEvent),domUtils.on(i,["focus","blur"],e._proxyDomEvent),domUtils.on(e.body,"drop",function(t){browser.gecko&&t.stopPropagation&&t.stopPropagation(),e.fireEvent("contentchange")}),domUtils.on(t,["mouseup","keydown"],function(t){"keydown"==t.type&&(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)||2!=t.button&&e._selectionChange(250,t)})},_proxyDomEvent:function(e){return!1!==this.fireEvent("before"+e.type.replace(/^on/,"").toLowerCase())&&(!1!==this.fireEvent(e.type.replace(/^on/,""),e)&&this.fireEvent("after"+e.type.replace(/^on/,"").toLowerCase()))},_selectionChange:function(t,i){var n,o,r=this,a=!1;browser.ie&&browser.version<9&&i&&"mouseup"==i.type&&(this.selection.getRange().collapsed||(a=!0,n=i.clientX,o=i.clientY));clearTimeout(e),e=setTimeout(function(){if(r.selection&&r.selection.getNative()){var e,t;if(a&&"None"==r.selection.getNative().type){e=r.document.body.createTextRange();try{e.moveToPoint(n,o)}catch(t){e=null}}e&&(t=r.selection.getIERange,r.selection.getIERange=function(){return e}),r.selection.cache(),t&&(r.selection.getIERange=t),r.selection._cachedRange&&r.selection._cachedStartElement&&(r.fireEvent("beforeselectionchange"),r.fireEvent("selectionchange",!!i),r.fireEvent("afterselectionchange"),r.selection.clear())}},t||50)},_callCmdFn:function(e,t){var i,n,o=t[0].toLowerCase();return n=(i=this.commands[o]||UE.commands[o])&&i[e],i&&n||"queryCommandState"!=e?n?n.apply(this,t):void 0:0},execCommand:function(e){e=e.toLowerCase();var t,i=this,n=i.commands[e]||UE.commands[e];return n&&n.execCommand?(n.notNeedUndo||i.__hasEnterExecCommand?(t=this._callCmdFn("execCommand",arguments),!i.__hasEnterExecCommand&&!n.ignoreContentChange&&!i._ignoreContentChange&&i.fireEvent("contentchange")):(i.__hasEnterExecCommand=!0,-1!=i.queryCommandState.apply(i,arguments)&&(i.fireEvent("saveScene"),i.fireEvent.apply(i,["beforeexeccommand",e].concat(arguments)),t=this._callCmdFn("execCommand",arguments),i.fireEvent.apply(i,["afterexeccommand",e].concat(arguments)),i.fireEvent("saveScene")),i.__hasEnterExecCommand=!1),!i.__hasEnterExecCommand&&!n.ignoreContentChange&&!i._ignoreContentChange&&i._selectionChange(),t):null},queryCommandState:function(e){return this._callCmdFn("queryCommandState",arguments)},queryCommandValue:function(e){return this._callCmdFn("queryCommandValue",arguments)},hasContents:function(e){if(e)for(var t,i=0;t=e[i++];)if(this.document.getElementsByTagName(t).length>0)return!0;if(!domUtils.isEmptyBlock(this.body))return!0;for(e=["div"],i=0;t=e[i++];)for(var n,o=domUtils.getElementsByTagName(this.document,t),r=0;n=o[r++];)if(domUtils.isCustomeNode(n))return!0;return!1},reset:function(){this.fireEvent("reset")},setEnabled:function(){var e,t=this;if("false"==t.body.contentEditable){t.body.contentEditable=!0,e=t.selection.getRange();try{e.moveToBookmark(t.lastBk),delete t.lastBk}catch(i){e.setStartAtFirst(t.body).collapse(!0)}e.select(!0),t.bkqueryCommandState&&(t.queryCommandState=t.bkqueryCommandState,delete t.bkqueryCommandState),t.bkqueryCommandValue&&(t.queryCommandValue=t.bkqueryCommandValue,delete t.bkqueryCommandValue),t.fireEvent("selectionchange")}},enable:function(){return this.setEnabled()},setDisabled:function(e){var t=this;e=e?utils.isArray(e)?e:[e]:[],"true"==t.body.contentEditable&&(t.lastBk||(t.lastBk=t.selection.getRange().createBookmark(!0)),t.body.contentEditable=!1,t.bkqueryCommandState=t.queryCommandState,t.bkqueryCommandValue=t.queryCommandValue,t.queryCommandState=function(i){return-1!=utils.indexOf(e,i)?t.bkqueryCommandState.apply(t,arguments):-1},t.queryCommandValue=function(i){return-1!=utils.indexOf(e,i)?t.bkqueryCommandValue.apply(t,arguments):null},t.fireEvent("selectionchange"))},disable:function(e){return this.setDisabled(e)},_setDefaultContent:function(){function e(){var t=this;t.document.getElementById("initContent")&&(t.body.innerHTML="",t.removeListener("firstBeforeExecCommand focus",e),setTimeout(function(){t.focus(),t._selectionChange()},0))}return function(t){this.body.innerHTML='<p id="initContent">'+t+"</p>",this.addListener("firstBeforeExecCommand focus",e)}}(),setShow:function(){var e=this,t=e.selection.getRange();if("none"==e.container.style.display){try{t.moveToBookmark(e.lastBk),delete e.lastBk}catch(i){t.setStartAtFirst(e.body).collapse(!0)}setTimeout(function(){t.select(!0)},100),e.container.style.display=""}},show:function(){return this.setShow()},setHide:function(){this.lastBk||(this.lastBk=this.selection.getRange().createBookmark(!0)),this.container.style.display="none"},hide:function(){return this.setHide()},getLang:function(e){var t=UE.I18N[this.options.lang];if(!t)throw Error("not import language file");e=(e||"").split(".");for(var i,n=0;(i=e[n++])&&(t=t[i]););return t},getContentLength:function(e,t){var i=this.getContent(!1,!1,!0).length;if(e){t=(t||[]).concat(["hr","img","iframe"]),i=this.getContentTxt().replace(/[\t\r\n]+/g,"").length;for(var n,o=0;n=t[o++];)i+=this.document.getElementsByTagName(n).length}return i},addInputRule:function(e){this.inputRules.push(e)},filterInputRule:function(e){for(var t,i=0;t=this.inputRules[i++];)t.call(this,e)},addOutputRule:function(e){this.outputRules.push(e)},filterOutputRule:function(e){for(var t,i=0;t=this.outputRules[i++];)t.call(this,e)},getActionUrl:function(e){var t=this.getOpt(e)||e,i=this.getOpt("imageUrl"),n=this.getOpt("serverUrl");return!n&&i&&(n=i.replace(/^(.*[\/]).+([\.].+)$/,"$1controller$2")),n?(n=n+(-1==n.indexOf("?")?"?":"&")+"action="+(t||""),utils.formatUrl(n)):""}},utils.inherits(n,EventBase)}(),UE.Editor.defaultOptions=function(e){var t=e.options.UEDITOR_ROOT_URL;return{isShow:!0,initialContent:"",initialStyle:"",autoClearinitialContent:!1,iframeCssUrl:t+"themes/iframe.css",textarea:"editorValue",focus:!1,focusInEnd:!0,autoClearEmptyNode:!0,fullscreen:!1,readonly:!1,zIndex:999,imagePopup:!0,enterTag:"p",customDomain:!1,lang:"zh-cn",langPath:t+"lang/",theme:"default",themePath:t+"themes/",allHtmlEnabled:!1,scaleEnabled:!1,tableNativeEditInFF:!1,autoSyncData:!0,fileNameFormat:"{time}{rand:6}"}},function(){UE.Editor.prototype.loadServerConfig=function(){var me=this;function showErrorMsg(e){console&&console.error(e)}setTimeout(function(){try{me.options.imageUrl&&me.setOpt("serverUrl",me.options.imageUrl.replace(/^(.*[\/]).+([\.].+)$/,"$1controller$2"));var configUrl=me.getActionUrl("config"),isJsonp=utils.isCrossDomainUrl(configUrl);me._serverConfigLoaded=!1,configUrl&&UE.ajax.request(configUrl,{method:"GET",dataType:isJsonp?"jsonp":"",onsuccess:function(r){try{var config=isJsonp?r:eval("("+r.responseText+")");utils.extend(me.options,config),me.fireEvent("serverConfigLoaded"),me._serverConfigLoaded=!0}catch(e){showErrorMsg(me.getLang("loadconfigFormatError"))}},onerror:function(){showErrorMsg(me.getLang("loadconfigHttpError"))}})}catch(e){showErrorMsg(me.getLang("loadconfigError"))}})},UE.Editor.prototype.isServerConfigLoaded=function(){return this._serverConfigLoaded||!1},UE.Editor.prototype.afterConfigReady=function(e){if(e&&utils.isFunction(e)){var t=this,i=function(){e.apply(t,arguments),t.removeListener("serverConfigLoaded",i)};t.isServerConfigLoaded()?e.call(t,"serverConfigLoaded"):t.addListener("serverConfigLoaded",i)}}}(),UE.ajax=function(){var e="XMLHttpRequest()";try{new ActiveXObject("Msxml2.XMLHTTP"),e="ActiveXObject('Msxml2.XMLHTTP')"}catch(t){try{new ActiveXObject("Microsoft.XMLHTTP"),e="ActiveXObject('Microsoft.XMLHTTP')"}catch(e){}}var t=new Function("return new "+e);function i(e){var t=[];for(var i in e)if("method"!=i&&"timeout"!=i&&"async"!=i&&"dataType"!=i&&"callback"!=i&&null!=e[i]&&null!=e[i])if("function"!=(typeof e[i]).toLowerCase()&&"object"!=(typeof e[i]).toLowerCase())t.push(encodeURIComponent(i)+"="+encodeURIComponent(e[i]));else if(utils.isArray(e[i]))for(var n=0;n<e[i].length;n++)t.push(encodeURIComponent(i)+"[]="+encodeURIComponent(e[i][n]));return t.join("&")}function n(e,t){var n,o,r,a=t.onsuccess||function(){},s=document.createElement("SCRIPT"),l=t||{},d=l.charset,c=l.jsonp||"callback",u=l.timeOut||0,m=new RegExp("(\\?|&)"+c+"=([^&]*)");utils.isFunction(a)?(n="bd__editor__"+Math.floor(2147483648*Math.random()).toString(36),window[n]=h(0)):utils.isString(a)?n=a:(r=m.exec(e))&&(n=r[2]),(e=e.replace(m,"$1"+c+"="+n)).search(m)<0&&(e+=(e.indexOf("?")<0?"?":"&")+c+"="+n);var f=i(t);function h(e){return function(){try{if(e)l.onerror&&l.onerror();else try{clearTimeout(o),a.apply(window,arguments)}catch(e){}}catch(e){l.onerror&&l.onerror.call(window,e)}finally{l.oncomplete&&l.oncomplete.apply(window,arguments),s.parentNode&&s.parentNode.removeChild(s),window[n]=null;try{delete window[n]}catch(e){}}}}utils.isEmptyObject(t.data)||(f+=(f?"&":"")+i(t.data)),f&&(e=e.replace(/\?/,"?"+f+"&")),s.onerror=h(1),u&&(o=setTimeout(h(1),u)),function(e,t,i){e.setAttribute("type","text/javascript"),e.setAttribute("defer","defer"),i&&e.setAttribute("charset",i),e.setAttribute("src",t),document.getElementsByTagName("head")[0].appendChild(e)}(s,e,d)}return{request:function(e,o){o&&"jsonp"==o.dataType?n(e,o):function(e,n){var o=t(),r=!1,a={method:"POST",timeout:5e3,async:!0,data:{},onsuccess:function(){},onerror:function(){}};if("object"==typeof e&&(e=(n=e).url),o&&e){var s=n?utils.extend(a,n):a,l=i(s);utils.isEmptyObject(s.data)||(l+=(l?"&":"")+i(s.data));var d=setTimeout(function(){4!=o.readyState&&(r=!0,o.abort(),clearTimeout(d))},s.timeout),c=s.method.toUpperCase(),u=e+(-1==e.indexOf("?")?"?":"&")+("POST"==c?"":l+"&noCache="+ +new Date);o.open(c,u,s.async),o.onreadystatechange=function(){4==o.readyState&&(r||200!=o.status?s.onerror(o):s.onsuccess(o))},"POST"==c?(o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.send(l)):o.send(null)}}(e,o)},getJSONP:function(e,t,i){n(e,{data:t,oncomplete:i})}}}();var filterWord=UE.filterWord=function(){function e(e){return e=e.replace(/[\d.]+\w+/g,function(e){return utils.transUnitToPx(e)})}return function(t){return/(class="?Mso|style="[^"]*\bmso\-|w:WordDocument|<(v|o):|lang=)/gi.test(t)?function(t){return t.replace(/[\t\r\n]+/g," ").replace(/<!--[\s\S]*?-->/gi,"").replace(/<v:shape [^>]*>[\s\S]*?.<\/v:shape>/gi,function(t){if(browser.opera)return"";try{if(/Bitmap/i.test(t))return"";var i=t.match(/width:([ \d.]*p[tx])/i)[1],n=t.match(/height:([ \d.]*p[tx])/i)[1],o=t.match(/src=\s*"([^"]*)"/i)[1];return'<img width="'+e(i)+'" height="'+e(n)+'" src="'+o+'" />'}catch(e){return""}}).replace(/<\/?div[^>]*>/g,"").replace(/v:\w+=(["']?)[^'"]+\1/g,"").replace(/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|xml|meta|link|style|\w+:\w+)(?=[\s\/>]))[^>]*>/gi,"").replace(/<p [^>]*class="?MsoHeading"?[^>]*>(.*?)<\/p>/gi,"<p><strong>$1</strong></p>").replace(/\s+(class|lang|align)\s*=\s*(['"]?)([\w-]+)\2/gi,function(e,t,i,n){return"class"==t&&"MsoListParagraph"==n?e:""}).replace(/<(font|span)[^>]*>(\s*)<\/\1>/gi,function(e,t,i){return i.replace(/[\t\r\n ]+/g," ")}).replace(/(<[a-z][^>]*)\sstyle=(["'])([^\2]*?)\2/gi,function(t,i,n,o){for(var r,a=[],s=o.replace(/^\s+|\s+$/,"").replace(/&#39;/g,"'").replace(/&quot;/gi,"'").replace(/[\d.]+(cm|pt)/g,function(e){return utils.transUnitToPx(e)}).split(/;\s*/g),l=0;r=s[l];l++){var d,c,u=r.split(":");if(2==u.length){if(d=u[0].toLowerCase(),c=u[1].toLowerCase(),/^(background)\w*/.test(d)&&0==c.replace(/(initial|\s)/g,"").length||/^(margin)\w*/.test(d)&&/^0\w+$/.test(c))continue;switch(d){case"mso-padding-alt":case"mso-padding-top-alt":case"mso-padding-right-alt":case"mso-padding-bottom-alt":case"mso-padding-left-alt":case"mso-margin-alt":case"mso-margin-top-alt":case"mso-margin-right-alt":case"mso-margin-bottom-alt":case"mso-margin-left-alt":case"mso-height":case"mso-width":case"mso-vertical-align-alt":/<table/.test(i)||(a[l]=d.replace(/^mso-|-alt$/g,"")+":"+e(c));continue;case"horiz-align":a[l]="text-align:"+c;continue;case"vert-align":a[l]="vertical-align:"+c;continue;case"font-color":case"mso-foreground":a[l]="color:"+c;continue;case"mso-background":case"mso-highlight":a[l]="background:"+c;continue;case"mso-default-height":a[l]="min-height:"+e(c);continue;case"mso-default-width":a[l]="min-width:"+e(c);continue;case"mso-padding-between-alt":a[l]="border-collapse:separate;border-spacing:"+e(c);continue;case"text-line-through":"single"!=c&&"double"!=c||(a[l]="text-decoration:line-through");continue;case"mso-zero-height":"yes"==c&&(a[l]="display:none");continue;case"margin":if(!/[1-9]/.test(c))continue}if(/^(mso|column|font-emph|lang|layout|line-break|list-image|nav|panose|punct|row|ruby|sep|size|src|tab-|table-border|text-(?:decor|trans)|top-bar|version|vnd|word-break)/.test(d)||/text\-indent|padding|margin/.test(d)&&/\-[\d.]+/.test(c))continue;a[l]=d+":"+u[1]}}return i+(a.length?' style="'+a.join(";").replace(/;{2,}/g,";")+'"':"")})}(t):t}}();!function(){var e=UE.uNode=function(e){this.type=e.type,this.data=e.data,this.tagName=e.tagName,this.parentNode=e.parentNode,this.attrs=e.attrs||{},this.children=e.children},t={href:1,src:1,_src:1,_href:1,cdata_data:1},i={style:1,script:1},n="    ",o="\n";function r(e,t,i){return e.push(o),t+(i?1:-1)}function a(e,t){for(var i=0;i<t;i++)e.push(n)}function s(e,n,o,l){switch(e.type){case"root":for(var d,c=0;d=e.children[c++];)o&&"element"==d.type&&!dtd.$inlineWithA[d.tagName]&&c>1&&(r(n,l,!0),a(n,l)),s(d,n,o,l);break;case"text":!function(e,t){"pre"==e.parentNode.tagName?t.push(e.data):t.push(i[e.parentNode.tagName]?utils.html(e.data):e.data.replace(/[ ]{2}/g," &nbsp;"))}(e,n);break;case"element":!function(e,i,n,o){var l="";if(e.attrs){l=[];var d=e.attrs;for(var c in d)l.push(c+(void 0!==d[c]?'="'+(t[c]?utils.html(d[c]).replace(/["]/g,function(e){return"&quot;"}):utils.unhtml(d[c]))+'"':""));l=l.join(" ")}i.push("<"+e.tagName+(l?" "+l:"")+(dtd.$empty[e.tagName]?"/":"")+">"),n&&!dtd.$inlineWithA[e.tagName]&&"pre"!=e.tagName&&e.children&&e.children.length&&(o=r(i,o,!0),a(i,o));if(e.children&&e.children.length)for(var u,m=0;u=e.children[m++];)n&&"element"==u.type&&!dtd.$inlineWithA[u.tagName]&&m>1&&(r(i,o),a(i,o)),s(u,i,n,o);dtd.$empty[e.tagName]||(n&&!dtd.$inlineWithA[e.tagName]&&"pre"!=e.tagName&&e.children&&e.children.length&&(o=r(i,o),a(i,o)),i.push("</"+e.tagName+">"))}(e,n,o,l);break;case"comment":!function(e,t){t.push("\x3c!--"+e.data+"--\x3e")}(e,n)}return n}function l(e,t){var i;if("element"==e.type&&e.getAttr("id")==t)return e;if(e.children&&e.children.length)for(var n,o=0;n=e.children[o++];)if(i=l(n,t))return i}function d(e,t,i){if("element"==e.type&&e.tagName==t&&i.push(e),e.children&&e.children.length)for(var n,o=0;n=e.children[o++];)d(n,t,i)}e.createElement=function(t){return/[<>]/.test(t)?UE.htmlparser(t).children[0]:new e({type:"element",children:[],tagName:t})},e.createText=function(e,t){return new UE.uNode({type:"text",data:t?e:utils.unhtml(e||"")})},e.prototype={toHtml:function(e){var t=[];return s(this,t,e,0),t.join("")},innerHTML:function(e){if("element"!=this.type||dtd.$empty[this.tagName])return this;if(utils.isString(e)){if(this.children)for(var t=0;i=this.children[t++];)i.parentNode=null;this.children=[];var i,n=UE.htmlparser(e);for(t=0;i=n.children[t++];)this.children.push(i),i.parentNode=this;return this}return(n=new UE.uNode({type:"root",children:this.children})).toHtml()},innerText:function(t,i){if("element"!=this.type||dtd.$empty[this.tagName])return this;if(t){if(this.children)for(var n,o=0;n=this.children[o++];)n.parentNode=null;return this.children=[],this.appendChild(e.createText(t,i)),this}return this.toHtml().replace(/<[^>]+>/g,"")},getData:function(){return"element"==this.type?"":this.data},firstChild:function(){return this.children?this.children[0]:null},lastChild:function(){return this.children?this.children[this.children.length-1]:null},previousSibling:function(){for(var e,t=this.parentNode,i=0;e=t.children[i];i++)if(e===this)return 0==i?null:t.children[i-1]},nextSibling:function(){for(var e,t=this.parentNode,i=0;e=t.children[i++];)if(e===this)return t.children[i]},replaceChild:function(e,t){if(this.children){e.parentNode&&e.parentNode.removeChild(e);for(var i,n=0;i=this.children[n];n++)if(i===t)return this.children.splice(n,1,e),t.parentNode=null,e.parentNode=this,e}},appendChild:function(e){if("root"==this.type||"element"==this.type&&!dtd.$empty[this.tagName]){this.children||(this.children=[]),e.parentNode&&e.parentNode.removeChild(e);for(var t,i=0;t=this.children[i];i++)if(t===e){this.children.splice(i,1);break}return this.children.push(e),e.parentNode=this,e}},insertBefore:function(e,t){if(this.children){e.parentNode&&e.parentNode.removeChild(e);for(var i,n=0;i=this.children[n];n++)if(i===t)return this.children.splice(n,0,e),e.parentNode=this,e}},insertAfter:function(e,t){if(this.children){e.parentNode&&e.parentNode.removeChild(e);for(var i,n=0;i=this.children[n];n++)if(i===t)return this.children.splice(n+1,0,e),e.parentNode=this,e}},removeChild:function(e,t){if(this.children)for(var i,n=0;i=this.children[n];n++)if(i===e){if(this.children.splice(n,1),i.parentNode=null,t&&i.children&&i.children.length)for(var o,r=0;o=i.children[r];r++)this.children.splice(n+r,0,o),o.parentNode=this;return i}},getAttr:function(e){return this.attrs&&this.attrs[e.toLowerCase()]},setAttr:function(e,t){if(e)if(this.attrs||(this.attrs={}),utils.isObject(e))for(var i in e)e[i]?this.attrs[i.toLowerCase()]=e[i]:delete this.attrs[i];else t?this.attrs[e.toLowerCase()]=t:delete this.attrs[e];else delete this.attrs},getIndex:function(){for(var e,t=this.parentNode,i=0;e=t.children[i];i++)if(e===this)return i;return-1},getNodeById:function(e){var t;if(this.children&&this.children.length)for(var i,n=0;i=this.children[n++];)if(t=l(i,e))return t},getNodesByTagName:function(e){e=utils.trim(e).replace(/[ ]{2,}/g," ").split(" ");var t=[],i=this;return utils.each(e,function(e){if(i.children&&i.children.length)for(var n,o=0;n=i.children[o++];)d(n,e,t)}),t},getStyle:function(e){var t=this.getAttr("style");if(!t)return"";var i=new RegExp("(^|;)\\s*"+e+":([^;]+)","i"),n=t.match(i);return n&&n[0]?n[2]:""},setStyle:function(e,t){function i(e,t){var i=new RegExp("(^|;)\\s*"+e+":([^;]+;?)","gi");n=n.replace(i,"$1"),t&&(n=e+":"+utils.unhtml(t)+";"+n)}var n=this.getAttr("style");if(n||(n=""),utils.isObject(e))for(var o in e)i(o,e[o]);else i(e,t);this.setAttr("style",utils.trim(n))},traversal:function(e){return this.children&&this.children.length&&function e(t,i){if(t.children&&t.children.length)for(var n,o=0;n=t.children[o];)e(n,i),n.parentNode&&(n.children&&n.children.length&&i(n),n.parentNode&&o++);else i(t)}(this,e),this}}}();var htmlparser=UE.htmlparser=function(e,t){var i=/<(?:(?:\/([^>]+)>)|(?:!--([\S|\s]*?)-->)|(?:([^\s\/<>]+)\s*((?:(?:"[^"]*")|(?:'[^']*')|[^"'<>])*)\/?>))/g,n=/([\w\-:.]+)(?:(?:\s*=\s*(?:(?:"([^"]*)")|(?:'([^']*)')|([^\s>]+)))|(?=\s|$))/g,o={b:1,code:1,i:1,u:1,strike:1,s:1,tt:1,strong:1,q:1,samp:1,em:1,span:1,sub:1,img:1,sup:1,font:1,big:1,small:1,iframe:1,a:1,br:1,pre:1};e=e.replace(new RegExp(domUtils.fillChar,"g"),""),t||(e=e.replace(new RegExp("[\\r\\t\\n"+(t?"":" ")+"]*</?(\\w+)\\s*(?:[^>]*)>[\\r\\t\\n"+(t?"":" ")+"]*","g"),function(e,i){return i&&o[i.toLowerCase()]?e.replace(/(^[\n\r]+)|([\n\r]+$)/g,""):e.replace(new RegExp("^[\\r\\n"+(t?"":" ")+"]+"),"").replace(new RegExp("[\\r\\n"+(t?"":" ")+"]+$"),"")}));var r={href:1,src:1},a=UE.uNode,s={td:"tr",tr:["tbody","thead","tfoot"],tbody:"table",th:"tr",thead:"table",tfoot:"table",caption:"table",li:["ul","ol"],dt:"dl",dd:"dl",option:"select"},l={ol:"li",ul:"li"};function d(e,t){if(l[e.tagName]){var i=a.createElement(l[e.tagName]);e.appendChild(i),i.appendChild(a.createText(t)),e=i}else e.appendChild(a.createText(t))}function c(e,t,i){var o;if(o=s[t]){for(var l,d=e;"root"!=d.type;){if(utils.isArray(o)?-1!=utils.indexOf(o,d.tagName):o==d.tagName){e=d,l=!0;break}d=d.parentNode}l||(e=c(e,utils.isArray(o)?o[0]:o))}var u=new a({parentNode:e,type:"element",tagName:t.toLowerCase(),children:dtd.$empty[t]?null:[]});if(i){for(var m,f={};m=n.exec(i);)f[m[1].toLowerCase()]=r[m[1].toLowerCase()]?m[2]||m[3]||m[4]:utils.unhtml(m[2]||m[3]||m[4]);u.attrs=f}return e.children.push(u),dtd.$empty[t]?e:u}for(var u,m,f,h=0,p=0,g=new a({type:"root",children:[]}),v=g;u=i.exec(e);){h=u.index;try{if(h>p&&d(v,e.slice(p,h)),u[3])dtd.$cdata[v.tagName]?d(v,u[0]):v=c(v,u[3].toLowerCase(),u[4]);else if(u[1]){if("root"!=v.type)if(dtd.$cdata[v.tagName]&&!dtd.$cdata[u[1]])d(v,u[0]);else{for(var b=v;"element"==v.type&&v.tagName!=u[1].toLowerCase();)if("root"==(v=v.parentNode).type)throw v=b,"break";v=v.parentNode}}else u[2]&&(m=v,f=u[2],m.children.push(new a({type:"comment",data:f,parentNode:m})))}catch(e){}p=i.lastIndex}return p<e.length&&d(v,e.slice(p)),g},filterNode=UE.filterNode=function(){function e(t,i){switch(t.type){case"text":break;case"element":var n;if(n=i[t.tagName])if("-"===n)t.parentNode.removeChild(t);else if(utils.isFunction(n)){var o=t.parentNode,r=t.getIndex();if(n(t),t.parentNode){if(t.children)for(var a=0;m=t.children[a];)e(m,i),m.parentNode&&a++}else for(a=r;m=o.children[a];)e(m,i),m.parentNode&&a++}else{var s=n.$;if(s&&t.attrs){var l,d={};for(var c in s){if(l=t.getAttr(c),"style"==c&&utils.isArray(s[c])){var u=[];utils.each(s[c],function(e){var i;(i=t.getStyle(e))&&u.push(e+":"+i)}),l=u.join(";")}l&&(d[c]=l)}t.attrs=d}if(t.children)for(a=0;m=t.children[a];)e(m,i),m.parentNode&&a++}else if(dtd.$cdata[t.tagName])t.parentNode.removeChild(t);else{o=t.parentNode,r=t.getIndex();t.parentNode.removeChild(t,!0);var m;for(a=r;m=o.children[a];)e(m,i),m.parentNode&&a++}break;case"comment":t.parentNode.removeChild(t)}}return function(t,i){if(utils.isEmptyObject(i))return t;var n;(n=i["-"])&&utils.each(n.split(" "),function(e){i[e]="-"});for(var o,r=0;o=t.children[r];)e(o,i),o.parentNode&&r++;return t}}(),_plugins;UE.plugin=(_plugins={},{register:function(e,t,i,n){i&&utils.isFunction(i)&&(n=i,i=null),_plugins[e]={optionName:i||e,execFn:t,afterDisabled:n}},load:function(e){utils.each(_plugins,function(t){var i=t.execFn.call(e);!1!==e.options[t.optionName]?i&&utils.each(i,function(t,i){switch(i.toLowerCase()){case"shortcutkey":e.addshortcutkey(t);break;case"bindevents":utils.each(t,function(t,i){e.addListener(i,t)});break;case"bindmultievents":utils.each(utils.isArray(t)?t:[t],function(t){var i=utils.trim(t.type).split(/\s+/);utils.each(i,function(i){e.addListener(i,t.handler)})});break;case"commands":utils.each(t,function(t,i){e.commands[i]=t});break;case"outputrule":e.addOutputRule(t);break;case"inputrule":e.addInputRule(t);break;case"defaultoptions":e.setOpt(t)}}):t.afterDisabled&&t.afterDisabled.call(e)}),utils.each(UE.plugins,function(t){t.call(e)})},run:function(e,t){var i=_plugins[e];i&&i.exeFn.call(t)}});var keymap=UE.keymap={Backspace:8,Tab:9,Enter:13,Shift:16,Control:17,Alt:18,CapsLock:20,Esc:27,Spacebar:32,PageUp:33,PageDown:34,End:35,Home:36,Left:37,Up:38,Right:39,Down:40,Insert:45,Del:46,NumLock:144,Cmd:91,"=":187,"-":189,b:66,i:73,z:90,y:89,v:86,x:88,s:83,n:78},LocalStorage=UE.LocalStorage=function(){var e=window.localStorage||function(){var e=document.createElement("div");if(e.style.display="none",!e.addBehavior)return null;return e.addBehavior("#default#userdata"),{getItem:function(i){var n=null;try{document.body.appendChild(e),e.load(t),n=e.getAttribute(i),document.body.removeChild(e)}catch(e){}return n},setItem:function(i,n){document.body.appendChild(e),e.setAttribute(i,n),e.save(t),document.body.removeChild(e)},removeItem:function(i){document.body.appendChild(e),e.removeAttribute(i),e.save(t),document.body.removeChild(e)}}}()||null,t="localStorage";return{saveLocalData:function(t,i){return!(!e||!i)&&(e.setItem(t,i),!0)},getLocalData:function(t){return e?e.getItem(t):null},removeItem:function(t){e&&e.removeItem(t)}}}(),ROOTKEY,block,getObj,sourceEditors;ROOTKEY="ueditor_preference",UE.Editor.prototype.setPreferences=function(e,t){var i={};utils.isString(e)?i[e]=t:i=e;var n=LocalStorage.getLocalData(ROOTKEY);n&&(n=utils.str2json(n))?utils.extend(n,i):n=i,n&&LocalStorage.saveLocalData(ROOTKEY,utils.json2str(n))},UE.Editor.prototype.getPreferences=function(e){var t=LocalStorage.getLocalData(ROOTKEY);return t&&(t=utils.str2json(t))?e?t[e]:t:null},UE.Editor.prototype.removePreferences=function(e){var t=LocalStorage.getLocalData(ROOTKEY);t&&(t=utils.str2json(t))&&(t[e]=void 0,delete t[e]),t&&LocalStorage.saveLocalData(ROOTKEY,utils.json2str(t))},UE.plugins.defaultfilter=function(){var e=this;e.setOpt({disabledTableInTable:!0}),e.addInputRule(function(t){var i,n=this.options.allowDivTransToP;t.traversal(function(t){if("element"==t.type){if(!dtd.$cdata[t.tagName]&&e.options.autoClearEmptyNode&&dtd.$inline[t.tagName]&&!dtd.$empty[t.tagName]&&(!t.attrs||utils.isEmptyObject(t.attrs)))return void(t.firstChild()?"span"!=t.tagName||t.attrs&&!utils.isEmptyObject(t.attrs)||t.parentNode.removeChild(t,!0):t.parentNode.removeChild(t));switch(t.tagName){case"style":case"script":t.setAttr({cdata_tag:t.tagName,cdata_data:t.innerHTML()||"",_ue_custom_node_:"true"}),t.tagName="div",t.innerHTML("");break;case"a":(i=t.getAttr("href"))&&t.setAttr("_href",i);break;case"img":if((i=t.getAttr("src"))&&/^data:/.test(i)){t.parentNode.removeChild(t);break}t.setAttr("_src",t.getAttr("src"));break;case"span":browser.webkit&&(i=t.getStyle("white-space"))&&/nowrap|normal/.test(i)&&(t.setStyle("white-space",""),e.options.autoClearEmptyNode&&utils.isEmptyObject(t.attrs)&&t.parentNode.removeChild(t,!0)),(i=t.getAttr("id"))&&/^_baidu_bookmark_/i.test(i)&&t.parentNode.removeChild(t);break;case"p":(i=t.getAttr("align"))&&(t.setAttr("align"),t.setStyle("text-align",i)),utils.each(t.children,function(e){if("element"==e.type&&"p"==e.tagName){var i=e.nextSibling();t.parentNode.insertAfter(e,t);for(var n=e;i;){var o=i.nextSibling();t.parentNode.insertAfter(i,n),n=i,i=o}return!1}}),t.firstChild()||t.innerHTML(browser.ie?"&nbsp;":"<br/>");break;case"div":if(t.getAttr("cdata_tag"))break;if((i=t.getAttr("class"))&&/^line number\d+/.test(i))break;if(!n)break;for(var o,r=UE.uNode.createElement("p");o=t.firstChild();)"text"!=o.type&&UE.dom.dtd.$block[o.tagName]?r.firstChild()?(t.parentNode.insertBefore(r,t),r=UE.uNode.createElement("p")):t.parentNode.insertBefore(o,t):r.appendChild(o);r.firstChild()&&t.parentNode.insertBefore(r,t),t.parentNode.removeChild(t);break;case"dl":t.tagName="ul";break;case"dt":case"dd":t.tagName="li";break;case"li":var a=t.getAttr("class");a&&/list\-/.test(a)||t.setAttr();var s=t.getNodesByTagName("ol ul");UE.utils.each(s,function(e){t.parentNode.insertAfter(e,t)});break;case"td":case"th":case"caption":t.children&&t.children.length||t.appendChild(browser.ie11below?UE.uNode.createText(" "):UE.uNode.createElement("br"));break;case"table":e.options.disabledTableInTable&&function(e){for(;e&&"element"==e.type;){if("td"==e.tagName)return!0;e=e.parentNode}return!1}(t)&&(t.parentNode.insertBefore(UE.uNode.createText(t.innerText()),t),t.parentNode.removeChild(t))}}})}),e.addOutputRule(function(t){var i;t.traversal(function(t){if("element"==t.type){if(e.options.autoClearEmptyNode&&dtd.$inline[t.tagName]&&!dtd.$empty[t.tagName]&&(!t.attrs||utils.isEmptyObject(t.attrs)))return void(t.firstChild()?"span"!=t.tagName||t.attrs&&!utils.isEmptyObject(t.attrs)||t.parentNode.removeChild(t,!0):t.parentNode.removeChild(t));switch(t.tagName){case"div":(i=t.getAttr("cdata_tag"))&&(t.tagName=i,t.appendChild(UE.uNode.createText(t.getAttr("cdata_data"))),t.setAttr({cdata_tag:"",cdata_data:"",_ue_custom_node_:""}));break;case"a":(i=t.getAttr("_href"))&&t.setAttr({href:utils.html(i),_href:""});break;case"span":(i=t.getAttr("id"))&&/^_baidu_bookmark_/i.test(i)&&t.parentNode.removeChild(t);break;case"img":(i=t.getAttr("_src"))&&t.setAttr({src:t.getAttr("_src"),_src:""})}}})})},UE.commands.inserthtml={execCommand:function(e,t,i){var n,o,r=this;if(t&&!0!==r.fireEvent("beforeinserthtml",t)){if((o=(n=r.selection.getRange()).document.createElement("div")).style.display="inline",!i){var a=UE.htmlparser(t);r.options.filterRules&&UE.filterNode(a,r.options.filterRules),r.filterInputRule(a),t=a.toHtml()}if(o.innerHTML=utils.trim(t),!n.collapsed){var s=n.startContainer;if(domUtils.isFillChar(s)&&n.setStartBefore(s),s=n.endContainer,domUtils.isFillChar(s)&&n.setEndAfter(s),n.txtToElmBoundary(),n.endContainer&&1==n.endContainer.nodeType&&(s=n.endContainer.childNodes[n.endOffset])&&domUtils.isBr(s)&&n.setEndAfter(s),0==n.startOffset&&(s=n.startContainer,domUtils.isBoundaryNode(s,"firstChild")&&(s=n.endContainer,n.endOffset==(3==s.nodeType?s.nodeValue.length:s.childNodes.length)&&domUtils.isBoundaryNode(s,"lastChild")&&(r.body.innerHTML="",n.setStart(r.body.firstChild,0).collapse(!0)))),!n.collapsed&&n.deleteContents(),1==n.startContainer.nodeType)if((l=n.startContainer.childNodes[n.startOffset])&&domUtils.isBlockElm(l)&&(v=l.previousSibling)&&domUtils.isBlockElm(v)){for(n.setEnd(v,v.childNodes.length).collapse();l.firstChild;)v.appendChild(l.firstChild);domUtils.remove(l)}}var l,d,c,u,m=0;n.inFillChar()&&(l=n.startContainer,domUtils.isFillChar(l)?(n.setStartBefore(l).collapse(!0),domUtils.remove(l)):domUtils.isFillChar(l,!0)&&(l.nodeValue=l.nodeValue.replace(fillCharReg,""),n.startOffset--,n.collapsed&&n.collapse(!0)));var f=domUtils.findParentByTagName(n.startContainer,"li",!0);if(f){for(var h;l=o.firstChild;){for(;l&&(3==l.nodeType||!domUtils.isBlockElm(l)||"HR"==l.tagName);)b=l.nextSibling,n.insertNode(l).collapse(),h=l,l=b;if(l)if(/^(ol|ul)$/i.test(l.tagName)){for(;l.firstChild;)h=l.firstChild,domUtils.insertAfter(f,l.firstChild),f=f.nextSibling;domUtils.remove(l)}else{var p;b=l.nextSibling,p=r.document.createElement("li"),domUtils.insertAfter(f,p),p.appendChild(l),h=l,l=b,f=p}}f=domUtils.findParentByTagName(n.startContainer,"li",!0),domUtils.isEmptyBlock(f)&&domUtils.remove(f),h&&n.setStartAfter(h).collapse(!0).select(!0)}else{for(;l=o.firstChild;){if(m){for(var g=r.document.createElement("p");l&&(3==l.nodeType||!dtd.$block[l.tagName]);)u=l.nextSibling,g.appendChild(l),l=u;g.firstChild&&(l=g)}if(n.insertNode(l),u=l.nextSibling,!m&&l.nodeType==domUtils.NODE_ELEMENT&&domUtils.isBlockElm(l)&&(d=domUtils.findParent(l,function(e){return domUtils.isBlockElm(e)}))&&"body"!=d.tagName.toLowerCase()&&(!dtd[d.tagName][l.nodeName]||l.parentNode!==d)){if(dtd[d.tagName][l.nodeName])for(c=l.parentNode;c!==d;)v=c,c=c.parentNode;else v=d;domUtils.breakParent(l,v||c);var v=l.previousSibling;domUtils.trimWhiteTextNode(v),v.childNodes.length||domUtils.remove(v),!browser.ie&&(b=l.nextSibling)&&domUtils.isBlockElm(b)&&b.lastChild&&!domUtils.isBr(b.lastChild)&&b.appendChild(r.document.createElement("br")),m=1}var b=l.nextSibling;if(!o.firstChild&&b&&domUtils.isBlockElm(b)){n.setStart(b,0).collapse(!0);break}n.setEndAfter(l).collapse()}if(l=n.startContainer,u&&domUtils.isBr(u)&&domUtils.remove(u),domUtils.isBlockElm(l)&&domUtils.isEmptyNode(l))if(u=l.nextSibling)domUtils.remove(l),1==u.nodeType&&dtd.$block[u.tagName]&&n.setStart(u,0).collapse(!0).shrinkBoundary();else try{l.innerHTML=browser.ie?domUtils.fillChar:"<br/>"}catch(e){n.setStartBefore(l),domUtils.remove(l)}try{n.select(!0)}catch(e){}}setTimeout(function(){(n=r.selection.getRange()).scrollToView(r.autoHeightEnabled,r.autoHeightEnabled?domUtils.getXY(r.iframe).y:0),r.fireEvent("afterinserthtml",t)},200)}}},UE.plugins.autotypeset=function(){this.setOpt({autotypeset:{mergeEmptyline:!0,removeClass:!0,removeEmptyline:!1,textAlign:"left",imageBlockLine:"center",pasteFilter:!1,clearFontSize:!1,clearFontFamily:!1,removeEmptyNode:!1,removeTagNames:utils.extend({div:1},dtd.$removeEmpty),indent:!1,indentValue:"2em",bdc2sb:!1,tobdc:!1}});var e,t,i=this,n=i.options.autotypeset,o={selectTdClass:1,pagebreak:1,anchorclass:1},r={li:1},a={div:1,p:1,blockquote:1,center:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1,span:1};n&&(t=i.getPreferences("autotypeset"),utils.extend(i.options.autotypeset,t),n.pasteFilter&&i.addListener("beforepaste",d),i.commands.autotypeset={execCommand:function(){i.removeListener("beforepaste",d),n.pasteFilter&&i.addListener("beforepaste",d),d.call(i),i.sync()}});function s(t,i){return t&&3!=t.nodeType?domUtils.isBr(t)?1:t&&t.parentNode&&a[t.tagName.toLowerCase()]?e&&e.contains(t)||t.getAttribute("pagebreak")?0:i?!domUtils.isEmptyBlock(t):domUtils.isEmptyBlock(t,new RegExp("[\\s"+domUtils.fillChar+"]","g")):void 0:0}function l(e){e.style.cssText||(domUtils.removeAttributes(e,["style"]),"span"==e.tagName.toLowerCase()&&domUtils.hasNoAttributes(e)&&domUtils.remove(e,!0))}function d(t,i){var a,d=this;if(i){if(!n.pasteFilter)return;(a=d.document.createElement("div")).innerHTML=i.html}else a=d.document.body;for(var c,u,m=domUtils.getElementsByTagName(a,"*"),f=0;c=m[f++];)if(!0!==d.fireEvent("excludeNodeinautotype",c)){if(n.clearFontSize&&c.style.fontSize&&(domUtils.removeStyle(c,"font-size"),l(c)),n.clearFontFamily&&c.style.fontFamily&&(domUtils.removeStyle(c,"font-family"),l(c)),s(c)){if(n.mergeEmptyline)for(var h=c.nextSibling,p=domUtils.isBr(c);s(h)&&(h=(v=h).nextSibling,!p||h&&(!h||domUtils.isBr(h)));)domUtils.remove(v);if(n.removeEmptyline&&domUtils.inDoc(c,a)&&!r[c.parentNode.tagName.toLowerCase()]){if(domUtils.isBr(c)&&(h=c.nextSibling)&&!domUtils.isBr(h))continue;domUtils.remove(c);continue}}if(s(c,!0)&&"SPAN"!=c.tagName&&(n.indent&&(c.style.textIndent=n.indentValue),n.textAlign&&(c.style.textAlign=n.textAlign)),n.removeClass&&c.className&&!o[c.className.toLowerCase()]){if(e&&e.contains(c))continue;domUtils.removeAttributes(c,["class"])}if(n.imageBlockLine&&"img"==c.tagName.toLowerCase()&&!c.getAttribute("emotion"))if(i){var g=c;switch(n.imageBlockLine){case"left":case"right":case"none":for(var v,b,y=g.parentNode;dtd.$inline[y.tagName]||"A"==y.tagName;)y=y.parentNode;if("P"==(v=y).tagName&&"center"==domUtils.getStyle(v,"text-align")&&!domUtils.isBody(v)&&1==domUtils.getChildCount(v,function(e){return!domUtils.isBr(e)&&!domUtils.isWhitespace(e)}))if(b=v.previousSibling,h=v.nextSibling,b&&h&&1==b.nodeType&&1==h.nodeType&&b.tagName==h.tagName&&domUtils.isBlockElm(b)){for(b.appendChild(v.firstChild);h.firstChild;)b.appendChild(h.firstChild);domUtils.remove(v),domUtils.remove(h)}else domUtils.setStyle(v,"text-align","");domUtils.setStyle(g,"float",n.imageBlockLine);break;case"center":if("center"!=d.queryCommandValue("imagefloat")){for(y=g.parentNode,domUtils.setStyle(g,"float","none"),v=g;y&&1==domUtils.getChildCount(y,function(e){return!domUtils.isBr(e)&&!domUtils.isWhitespace(e)})&&(dtd.$inline[y.tagName]||"A"==y.tagName);)v=y,y=y.parentNode;var C=d.document.createElement("p");domUtils.setAttributes(C,{style:"text-align:center"}),v.parentNode.insertBefore(C,v),C.appendChild(v),domUtils.setStyle(v,"float","")}}}else{d.selection.getRange().selectNode(c).select(),d.execCommand("imagefloat",n.imageBlockLine)}n.removeEmptyNode&&n.removeTagNames[c.tagName.toLowerCase()]&&domUtils.hasNoAttributes(c)&&domUtils.isEmptyBlock(c)&&domUtils.remove(c)}n.tobdc&&((u=UE.htmlparser(a.innerHTML)).traversal(function(e){"text"==e.type&&(e.data=function(e){e=utils.html(e);for(var t="",i=0;i<e.length;i++)32==e.charCodeAt(i)?t+=String.fromCharCode(12288):e.charCodeAt(i)<127?t+=String.fromCharCode(e.charCodeAt(i)+65248):t+=e.charAt(i);return t}(e.data))}),a.innerHTML=u.toHtml());n.bdc2sb&&((u=UE.htmlparser(a.innerHTML)).traversal(function(e){"text"==e.type&&(e.data=function(e){for(var t="",i=0;i<e.length;i++){var n=e.charCodeAt(i);t+=n>=65281&&n<=65373?String.fromCharCode(e.charCodeAt(i)-65248):12288==n?String.fromCharCode(e.charCodeAt(i)-12288+32):e.charAt(i)}return t}(e.data))}),a.innerHTML=u.toHtml());i&&(i.html=a.innerHTML)}},UE.plugin.register("autosubmit",function(){return{shortcutkey:{autosubmit:"ctrl+13"},commands:{autosubmit:{execCommand:function(){var e=domUtils.findParentByTagName(this.iframe,"form",!1);if(e){if(!1===this.fireEvent("beforesubmit"))return;this.sync(),e.submit()}}}}}}),UE.plugin.register("background",function(){var e,t=this,i="editor_background",n=new RegExp("body[\\s]*\\{(.+)\\}","i");function o(e){var t={},i=e.split(";");return utils.each(i,function(e){var i=e.indexOf(":"),n=utils.trim(e.substr(0,i)).toLowerCase();n&&(t[n]=utils.trim(e.substr(i+1)||""))}),t}function r(e){if(e){var n=[];for(var o in e)e.hasOwnProperty(o)&&n.push(o+":"+e[o]+"; ");utils.cssRule(i,n.length?"body{"+n.join("")+"}":"",t.document)}else utils.cssRule(i,"",t.document)}var a=t.hasContents;return t.hasContents=function(){return!!t.queryCommandValue("background")||a.apply(t,arguments)},{bindEvents:{getAllHtml:function(e,i){var n=this.body,o=domUtils.getComputedStyle(n,"background-image"),r="";r=o.indexOf(t.options.imagePath)>0?o.substring(o.indexOf(t.options.imagePath),o.length-1).replace(/"|\(|\)/gi,""):"none"!=o?o.replace(/url\("?|"?\)/gi,""):"";var a='<style type="text/css">body{',s={"background-color":domUtils.getComputedStyle(n,"background-color")||"#ffffff","background-image":r?"url("+r+")":"","background-repeat":domUtils.getComputedStyle(n,"background-repeat")||"","background-position":browser.ie?domUtils.getComputedStyle(n,"background-position-x")+" "+domUtils.getComputedStyle(n,"background-position-y"):domUtils.getComputedStyle(n,"background-position"),height:domUtils.getComputedStyle(n,"height")};for(var l in s)s.hasOwnProperty(l)&&(a+=l+":"+s[l]+"; ");a+="}</style> ",i.push(a)},aftersetcontent:function(){0==e&&r()}},inputRule:function(t){e=!1,utils.each(t.getNodesByTagName("p"),function(t){var i=t.getAttr("data-background");i&&(e=!0,r(o(i)),t.parentNode.removeChild(t))})},outputRule:function(e){var t=(utils.cssRule(i,this.document)||"").replace(/[\n\r]+/g,"").match(n);t&&e.appendChild(UE.uNode.createElement('<p style="display:none;" data-background="'+utils.trim(t[1].replace(/"/g,"").replace(/[\s]+/g," "))+'"><br/></p>'))},commands:{background:{execCommand:function(e,t){r(t)},queryCommandValue:function(){var e=(utils.cssRule(i,this.document)||"").replace(/[\n\r]+/g,"").match(n);return e?o(e[1]):null},notNeedUndo:!0}}}}),UE.commands.imagefloat={execCommand:function(e,t){var i=this,n=i.selection.getRange();if(!n.collapsed){var o=n.getClosedNode();if(o&&"IMG"==o.tagName)switch(t){case"left":case"right":case"none":for(var r,a,s,l=o.parentNode;dtd.$inline[l.tagName]||"A"==l.tagName;)l=l.parentNode;if("P"==(r=l).tagName&&"center"==domUtils.getStyle(r,"text-align")){if(!domUtils.isBody(r)&&1==domUtils.getChildCount(r,function(e){return!domUtils.isBr(e)&&!domUtils.isWhitespace(e)}))if(a=r.previousSibling,s=r.nextSibling,a&&s&&1==a.nodeType&&1==s.nodeType&&a.tagName==s.tagName&&domUtils.isBlockElm(a)){for(a.appendChild(r.firstChild);s.firstChild;)a.appendChild(s.firstChild);domUtils.remove(r),domUtils.remove(s)}else domUtils.setStyle(r,"text-align","");n.selectNode(o).select()}domUtils.setStyle(o,"float","none"==t?"":t),"none"==t&&domUtils.removeAttributes(o,"align");break;case"center":if("center"!=i.queryCommandValue("imagefloat")){for(l=o.parentNode,domUtils.setStyle(o,"float",""),domUtils.removeAttributes(o,"align"),r=o;l&&1==domUtils.getChildCount(l,function(e){return!domUtils.isBr(e)&&!domUtils.isWhitespace(e)})&&(dtd.$inline[l.tagName]||"A"==l.tagName);)r=l,l=l.parentNode;n.setStartBefore(r).setCursor(!1),(l=i.document.createElement("div")).appendChild(r),domUtils.setStyle(r,"float",""),i.execCommand("insertHtml",'<p id="_img_parent_tmp" style="text-align:center">'+l.innerHTML+"</p>"),null!=(r=i.document.getElementById("_img_parent_tmp"))&&r.hasOwnProperty("id")&&(r.removeAttribute("id"),r=r.firstChild,n.selectNode(r).select(),(s=r.parentNode.nextSibling)&&domUtils.isEmptyNode(s)&&domUtils.remove(s))}}}},queryCommandValue:function(){var e,t,i=this.selection.getRange();return i.collapsed?"none":(e=i.getClosedNode())&&1==e.nodeType&&"IMG"==e.tagName?("none"==(t=domUtils.getComputedStyle(e,"float")||e.getAttribute("align"))&&(t="center"==domUtils.getComputedStyle(e.parentNode,"text-align")?"center":t),{left:1,right:1,center:1}[t]?t:"none"):"none"},queryCommandState:function(){var e,t=this.selection.getRange();return t.collapsed?-1:(e=t.getClosedNode())&&1==e.nodeType&&"IMG"==e.tagName?0:-1}},UE.commands.insertimage={execCommand:function(e,t){if((t=utils.isArray(t)?t:[t]).length){var i=this,n=i.selection.getRange(),o=n.getClosedNode();if(!0!==i.fireEvent("beforeinsertimage",t)){if(!o||!/img/i.test(o.tagName)||"edui-faked-video"==o.className&&-1==o.className.indexOf("edui-upload-video")||o.getAttribute("word_img")){var r,a=[],s="";if(r=t[0],1==t.length)u(r),s='<img src="'+r.src+'" '+(r._src?' _src="'+r._src+'" ':"")+("left"==r.floatStyle||"right"==r.floatStyle?' style="float:'+r.floatStyle+';"':"")+(r.width?'width="'+r.width+'" ':"")+(r.height?' height="'+r.height+'" ':"")+(r.title&&""!=r.title?' title="'+r.title+'"':"")+(r.border&&"0"!=r.border?' border="'+r.border+'"':"")+(r.alt&&""!=r.alt?' alt="'+r.alt+'"':"")+(r.hspace&&"0"!=r.hspace?' hspace = "'+r.hspace+'"':"")+(r.vspace&&"0"!=r.vspace?' vspace = "'+r.vspace+'"':"")+"/>","center"==r.floatStyle&&(s='<p style="text-align: center">'+s+"</p>"),a.push(s);else for(var l=0;r=t[l++];)u(r),s="<p "+("center"==r.floatStyle?'style="text-align: center" ':"")+'><img src="'+r.src+'" '+(r._src?' _src="'+r._src+'" ':"")+(r.width?'width="'+r.width+'" ':"")+(r._src?' _src="'+r._src+'" ':"")+(r.height?' height="'+r.height+'" ':"")+' style="'+(r.floatStyle&&"center"!=r.floatStyle?"float:"+r.floatStyle+";":"")+(r.border||"")+'" '+(r.title?' title="'+r.title+'"':"")+" /></p>",a.push(s);i.execCommand("insertHtml",a.join(""))}else{var d=t.shift(),c=d.floatStyle;delete d.floatStyle,domUtils.setAttributes(o,d),i.execCommand("imagefloat",c),t.length>0&&(n.setStartAfter(o).setCursor(!1,!0),i.execCommand("insertimage",t))}i.fireEvent("afterinsertimage",t)}}function u(e){utils.each("width,height,border,hspace,vspace".split(","),function(t){e[t]&&(e[t]=parseInt(e[t],10)||0)}),utils.each("src,_src".split(","),function(t){e[t]&&(e[t]=utils.unhtmlForUrl(e[t]))}),utils.each("title,alt".split(","),function(t){e[t]&&(e[t]=utils.unhtml(e[t]))})}}},UE.plugins.justify=function(){var e=domUtils.isBlockElm,t={left:1,right:1,center:1,justify:1};UE.commands.justify={execCommand:function(t,i){var n,o=this.selection.getRange();return o.collapsed&&(n=this.document.createTextNode("p"),o.insertNode(n)),function(t,i){var n=t.createBookmark(),o=function(e){return 1==e.nodeType?"br"!=e.tagName.toLowerCase()&&!domUtils.isBookmarkNode(e):!domUtils.isWhitespace(e)};t.enlarge(!0);for(var r,a=t.createBookmark(),s=domUtils.getNextDomNode(a.start,!1,o),l=t.cloneRange();s&&!(domUtils.getPosition(s,a.end)&domUtils.POSITION_FOLLOWING);)if(3!=s.nodeType&&e(s))s=domUtils.getNextDomNode(s,!0,o);else{for(l.setStartBefore(s);s&&s!==a.end&&!e(s);)r=s,s=domUtils.getNextDomNode(s,!1,null,function(t){return!e(t)});l.setEndAfter(r);var d=l.getCommonAncestor();if(!domUtils.isBody(d)&&e(d))domUtils.setStyles(d,utils.isString(i)?{"text-align":i}:i),s=d;else{var c=t.document.createElement("p");domUtils.setStyles(c,utils.isString(i)?{"text-align":i}:i);var u=l.extractContents();c.appendChild(u),l.insertNode(c),s=c}s=domUtils.getNextDomNode(s,!1,o)}t.moveToBookmark(a).moveToBookmark(n)}(o,i),n&&(o.setStartBefore(n).collapse(!0),domUtils.remove(n)),o.select(),!0},queryCommandValue:function(){var e=this.selection.getStart(),i=domUtils.getComputedStyle(e,"text-align");return t[i]?i:"left"},queryCommandState:function(){var e=this.selection.getStart();return e&&domUtils.findParentByTagName(e,["td","th","caption"],!0)?-1:0}}},UE.plugins.font=function(){var e={forecolor:"color",backcolor:"background-color",fontsize:"font-size",fontfamily:"font-family",underline:"text-decoration",strikethrough:"text-decoration",fontborder:"border"},t={underline:1,strikethrough:1,fontborder:1},i={forecolor:"color",backcolor:"background-color",fontsize:"font-size",fontfamily:"font-family"};function n(e,t,n){var o,r=e.collapsed,a=e.createBookmark();if(r)for(o=a.start.parentNode;dtd.$inline[o.tagName];)o=o.parentNode;else o=domUtils.getCommonAncestor(a.start,a.end);utils.each(domUtils.getElementsByTagName(o,"span"),function(e){if(e.parentNode&&!domUtils.isBookmarkNode(e))if(/\s*border\s*:\s*none;?\s*/i.test(e.style.cssText))/^\s*border\s*:\s*none;?\s*$/.test(e.style.cssText)?domUtils.remove(e,!0):domUtils.removeStyle(e,"border");else{if(/border/i.test(e.style.cssText)&&"SPAN"==e.parentNode.tagName&&/border/i.test(e.parentNode.style.cssText)&&(e.style.cssText=e.style.cssText.replace(/border[^:]*:[^;]+;?/gi,"")),"fontborder"!=t||"none"!=n)for(var i=e.nextSibling;i&&1==i.nodeType&&"SPAN"==i.tagName;)if(domUtils.isBookmarkNode(i)&&"fontborder"==t)e.appendChild(i),i=e.nextSibling;else{if(i.style.cssText==e.style.cssText&&(domUtils.moveChild(i,e),domUtils.remove(i)),e.nextSibling===i)break;i=e.nextSibling}if(function(e){for(var t;(t=e.parentNode)&&"SPAN"==t.tagName&&1==domUtils.getChildCount(t,function(e){return!domUtils.isBookmarkNode(e)&&!domUtils.isBr(e)});)t.style.cssText+=e.style.cssText,domUtils.remove(e,!0),e=t}(e),browser.ie&&browser.version>8){var o=domUtils.findParent(e,function(e){return"SPAN"==e.tagName&&/background-color/.test(e.style.cssText)});o&&!/background-color/.test(e.style.cssText)&&(e.style.backgroundColor=o.style.backgroundColor)}}}),e.moveToBookmark(a),function(e,t,n){if(i[t]&&(e.adjustmentBoundary(),!e.collapsed&&1==e.startContainer.nodeType)){var o=e.startContainer.childNodes[e.startOffset];if(o&&domUtils.isTagNode(o,"span")){var r=e.createBookmark();utils.each(domUtils.getElementsByTagName(o,"span"),function(e){e.parentNode&&!domUtils.isBookmarkNode(e)&&("backcolor"==t&&domUtils.getComputedStyle(e,"background-color").toLowerCase()===n||(domUtils.removeStyle(e,i[t]),0==e.style.cssText.replace(/^\s+$/,"").length&&domUtils.remove(e,!0)))}),e.moveToBookmark(r)}}}(e,t,n)}for(var o in this.setOpt({fontfamily:[{name:"songti",val:"宋体,SimSun"},{name:"yahei",val:"微软雅黑,Microsoft YaHei"},{name:"kaiti",val:"楷体,楷体_GB2312, SimKai"},{name:"heiti",val:"黑体, SimHei"},{name:"lishu",val:"隶书, SimLi"},{name:"andaleMono",val:"andale mono"},{name:"arial",val:"arial, helvetica,sans-serif"},{name:"arialBlack",val:"arial black,avant garde"},{name:"comicSansMs",val:"comic sans ms"},{name:"impact",val:"impact,chicago"},{name:"timesNewRoman",val:"times new roman"}],fontsize:[10,11,12,14,16,18,20,24,36]}),this.addInputRule(function(e){utils.each(e.getNodesByTagName("u s del font strike"),function(e){if("font"==e.tagName){var t=[];for(var i in e.attrs)switch(i){case"size":t.push("font-size:"+({1:"10",2:"12",3:"16",4:"18",5:"24",6:"32",7:"48"}[e.attrs[i]]||e.attrs[i])+"px");break;case"color":t.push("color:"+e.attrs[i]);break;case"face":t.push("font-family:"+e.attrs[i]);break;case"style":t.push(e.attrs[i])}e.attrs={style:t.join(";")}}else{var n="u"==e.tagName?"underline":"line-through";e.attrs={style:(e.getAttr("style")||"")+"text-decoration:"+n+";"}}e.tagName="span"})}),e)!function(e,i){UE.commands[e]={execCommand:function(o,r){r=r||(this.queryCommandState(o)?"none":"underline"==o?"underline":"fontborder"==o?"1px solid #000":"line-through");var a,s=this,l=this.selection.getRange();if("default"==r)l.collapsed&&(a=s.document.createTextNode("font"),l.insertNode(a).select()),s.execCommand("removeFormat","span,a",i),a&&(l.setStartBefore(a).collapse(!0),domUtils.remove(a)),n(l,o,r),l.select();else if(l.collapsed){var d=domUtils.findParentByTagName(l.startContainer,"span",!0);if(a=s.document.createTextNode("font"),!d||d.children.length||d[browser.ie?"innerText":"textContent"].replace(fillCharReg,"").length){if(l.insertNode(a),l.selectNode(a).select(),d=l.document.createElement("span"),t[e]){if(domUtils.findParentByTagName(a,"a",!0))return l.setStartBefore(a).setCursor(),void domUtils.remove(a);s.execCommand("removeFormat","span,a",i)}if(d.style.cssText=i+":"+r,a.parentNode.insertBefore(d,a),!browser.ie||browser.ie&&9==browser.version)for(var c=d.parentNode;!domUtils.isBlockElm(c);)"SPAN"==c.tagName&&(d.style.cssText=c.style.cssText+";"+d.style.cssText),c=c.parentNode;opera?setTimeout(function(){l.setStart(d,0).collapse(!0),n(l,o,r),l.select()}):(l.setStart(d,0).collapse(!0),n(l,o,r),l.select())}else l.insertNode(a),t[e]&&(l.selectNode(a).select(),s.execCommand("removeFormat","span,a",i,null),d=domUtils.findParentByTagName(a,"span",!0),l.setStartBefore(a)),d&&(d.style.cssText+=";"+i+":"+r),l.collapse(!0).select();domUtils.remove(a)}else t[e]&&s.queryCommandValue(e)&&s.execCommand("removeFormat","span,a",i),(l=s.selection.getRange()).applyInlineStyle("span",{style:i+":"+r}),n(l,o,r),l.select();return!0},queryCommandValue:function(e){var t=this.selection.getStart();if("underline"==e||"strikethrough"==e){for(var n,o=t;o&&!domUtils.isBlockElm(o)&&!domUtils.isBody(o);){if(1==o.nodeType&&"none"!=(n=domUtils.getComputedStyle(o,i)))return n;o=o.parentNode}return"none"}if("fontborder"==e){for(var r,a=t;a&&dtd.$inline[a.tagName];){if((r=domUtils.getComputedStyle(a,"border"))&&/1px/.test(r)&&/solid/.test(r))return r;a=a.parentNode}return""}if("FontSize"==e){var s=domUtils.getComputedStyle(t,i);return(a=/^([\d\.]+)(\w+)$/.exec(s))?Math.floor(a[1])+a[2]:s}return domUtils.getComputedStyle(t,i)},queryCommandState:function(e){if(!t[e])return 0;var i=this.queryCommandValue(e);return"fontborder"==e?/1px/.test(i)&&/solid/.test(i):"underline"==e?/underline/.test(i):/line\-through/.test(i)}}}(o,e[o])},UE.plugins.link=function(){function e(e){var t=e.startContainer,i=e.endContainer;(t=domUtils.findParentByTagName(t,"a",!0))&&e.setStartBefore(t),(i=domUtils.findParentByTagName(i,"a",!0))&&e.setEndAfter(i)}UE.commands.unlink={execCommand:function(){var t,i=this.selection.getRange();i.collapsed&&!domUtils.findParentByTagName(i.startContainer,"a",!0)||(t=i.createBookmark(),e(i),i.removeInlineStyle("a").moveToBookmark(t).select())},queryCommandState:function(){return!this.highlight&&this.queryCommandValue("link")?0:-1}},UE.commands.link={execCommand:function(t,i){var n;i._href&&(i._href=utils.unhtml(i._href,/[<">]/g)),i.href&&(i.href=utils.unhtml(i.href,/[<">]/g)),i.textValue&&(i.textValue=utils.unhtml(i.textValue,/[<">]/g)),function(t,i,n){var o=t.cloneRange(),r=n.queryCommandValue("link");e(t=t.adjustmentBoundary());var a=t.startContainer;if(1==a.nodeType&&r&&(a=a.childNodes[t.startOffset])&&1==a.nodeType&&"A"==a.tagName&&/^(?:https?|ftp|file)\s*:\s*\/\//.test(a[browser.ie?"innerText":"textContent"])&&(a[browser.ie?"innerText":"textContent"]=utils.html(i.textValue||i.href)),o.collapsed&&!r||(t.removeInlineStyle("a"),o=t.cloneRange()),o.collapsed){var s=t.document.createElement("a"),l="";i.textValue?(l=utils.html(i.textValue),delete i.textValue):l=utils.html(i.href),domUtils.setAttributes(s,i),(a=domUtils.findParentByTagName(o.startContainer,"a",!0))&&domUtils.isInNodeEndBoundary(o,a)&&t.setStartAfter(a).collapse(!0),s[browser.ie?"innerText":"textContent"]=l,t.insertNode(s).selectNode(s)}else t.applyInlineStyle("a",i)}(n=this.selection.getRange(),i,this),n.collapse().select(!0)},queryCommandValue:function(){var e,t=this.selection.getRange();if(!t.collapsed){t.shrinkBoundary();var i=3!=t.startContainer.nodeType&&t.startContainer.childNodes[t.startOffset]?t.startContainer.childNodes[t.startOffset]:t.startContainer,n=3==t.endContainer.nodeType||0==t.endOffset?t.endContainer:t.endContainer.childNodes[t.endOffset-1],o=t.getCommonAncestor();if(!(e=domUtils.findParentByTagName(o,"a",!0))&&1==o.nodeType)for(var r,a,s,l=o.getElementsByTagName("a"),d=0;s=l[d++];)if(r=domUtils.getPosition(s,i),a=domUtils.getPosition(s,n),(r&domUtils.POSITION_FOLLOWING||r&domUtils.POSITION_CONTAINS)&&(a&domUtils.POSITION_PRECEDING||a&domUtils.POSITION_CONTAINS)){e=s;break}return e}if((e=1==(e=t.startContainer).nodeType?e:e.parentNode)&&(e=domUtils.findParentByTagName(e,"a",!0))&&!domUtils.isInNodeEndBoundary(t,e))return e},queryCommandState:function(){var e=this.selection.getRange().getClosedNode();return e&&("edui-faked-video"==e.className||-1!=e.className.indexOf("edui-upload-video"))?-1:0}}},UE.plugins.insertframe=function(){var e=this;e.addListener("selectionchange",function(){e._iframe&&delete e._iframe})},UE.commands.scrawl={queryCommandState:function(){return browser.ie&&browser.version<=8?-1:0}},UE.plugins.removeformat=function(){this.setOpt({removeFormatTags:"b,big,code,del,dfn,em,font,i,ins,kbd,q,samp,small,span,strike,strong,sub,sup,tt,u,var",removeFormatAttributes:"class,style,lang,width,height,align,hspace,valign"}),this.commands.removeformat={execCommand:function(e,t,i,n,o){var r,a,s=new RegExp("^(?:"+(t||this.options.removeFormatTags).replace(/,/g,"|")+")$","i"),l=i?[]:(n||this.options.removeFormatAttributes).split(","),d=new dom.Range(this.document),c=function(e){return 1==e.nodeType};function u(e){if(3==e.nodeType||"span"!=e.tagName.toLowerCase())return 0;if(browser.ie){var t=e.attributes;if(t.length){for(var i=0,n=t.length;i<n;i++)if(t[i].specified)return 0;return 1}}return!e.attributes.length}(function(e){var t=e.createBookmark();if(e.collapsed&&e.enlarge(!0),!o){var n=domUtils.findParentByTagName(e.startContainer,"a",!0);n&&e.setStartBefore(n),(n=domUtils.findParentByTagName(e.endContainer,"a",!0))&&e.setEndAfter(n)}for(p=(r=e.createBookmark()).start;(a=p.parentNode)&&!domUtils.isBlockElm(a);)domUtils.breakParent(p,a),domUtils.clearEmptySibling(p);if(r.end){for(p=r.end;(a=p.parentNode)&&!domUtils.isBlockElm(a);)domUtils.breakParent(p,a),domUtils.clearEmptySibling(p);for(var d,m=domUtils.getNextDomNode(r.start,!1,c);m&&m!=r.end;)d=domUtils.getNextDomNode(m,!0,c),dtd.$empty[m.tagName.toLowerCase()]||domUtils.isBookmarkNode(m)||(s.test(m.tagName)?i?(domUtils.removeStyle(m,i),u(m)&&"text-decoration"!=i&&domUtils.remove(m,!0)):domUtils.remove(m,!0):dtd.$tableContent[m.tagName]||dtd.$list[m.tagName]||(domUtils.removeAttributes(m,l),u(m)&&domUtils.remove(m,!0))),m=d}var f=r.start.parentNode;!domUtils.isBlockElm(f)||dtd.$tableContent[f.tagName]||dtd.$list[f.tagName]||domUtils.removeAttributes(f,l),f=r.end.parentNode,r.end&&domUtils.isBlockElm(f)&&!dtd.$tableContent[f.tagName]&&!dtd.$list[f.tagName]&&domUtils.removeAttributes(f,l),e.moveToBookmark(r).moveToBookmark(t);for(var h,p=e.startContainer,g=e.collapsed;1==p.nodeType&&domUtils.isEmptyNode(p)&&dtd.$removeEmpty[p.tagName];)h=p.parentNode,e.setStartBefore(p),e.startContainer===e.endContainer&&e.endOffset--,domUtils.remove(p),p=h;if(!g)for(p=e.endContainer;1==p.nodeType&&domUtils.isEmptyNode(p)&&dtd.$removeEmpty[p.tagName];)h=p.parentNode,e.setEndBefore(p),domUtils.remove(p),p=h})(d=this.selection.getRange()),d.select()}}},UE.plugins.blockquote=function(){function e(e){return domUtils.filterNodeList(e.selection.getStartElementPath(),"blockquote")}this.commands.blockquote={execCommand:function(t,i){var n=this.selection.getRange(),o=e(this),r=dtd.blockquote,a=n.createBookmark();if(o){var s=n.startContainer,l=domUtils.isBlockElm(s)?s:domUtils.findParent(s,function(e){return domUtils.isBlockElm(e)}),d=n.endContainer,c=domUtils.isBlockElm(d)?d:domUtils.findParent(d,function(e){return domUtils.isBlockElm(e)});l=domUtils.findParentByTagName(l,"li",!0)||l,c=domUtils.findParentByTagName(c,"li",!0)||c,"LI"==l.tagName||"TD"==l.tagName||l===o||domUtils.isBody(l)?domUtils.remove(o,!0):domUtils.breakParent(l,o),l!==c&&(o=domUtils.findParentByTagName(c,"blockquote"))&&("LI"==c.tagName||"TD"==c.tagName||domUtils.isBody(c)?o.parentNode&&domUtils.remove(o,!0):domUtils.breakParent(c,o));for(var u,m=domUtils.getElementsByTagName(this.document,"blockquote"),f=0;u=m[f++];)u.childNodes.length?domUtils.getPosition(u,l)&domUtils.POSITION_FOLLOWING&&domUtils.getPosition(u,c)&domUtils.POSITION_PRECEDING&&domUtils.remove(u,!0):domUtils.remove(u)}else{for(var h=n.cloneRange(),p=1==h.startContainer.nodeType?h.startContainer:h.startContainer.parentNode,g=p,v=1;;){if(domUtils.isBody(p)){g!==p?n.collapsed?(h.selectNode(g),v=0):h.setStartBefore(g):h.setStart(p,0);break}if(!r[p.tagName]){n.collapsed?h.selectNode(g):h.setStartBefore(g);break}g=p,p=p.parentNode}if(v)for(g=p=p=1==h.endContainer.nodeType?h.endContainer:h.endContainer.parentNode;;){if(domUtils.isBody(p)){g!==p?h.setEndAfter(g):h.setEnd(p,p.childNodes.length);break}if(!r[p.tagName]){h.setEndAfter(g);break}g=p,p=p.parentNode}p=n.document.createElement("blockquote"),domUtils.setAttributes(p,i),p.appendChild(h.extractContents()),h.insertNode(p);var b,y=domUtils.getElementsByTagName(p,"blockquote");for(f=0;b=y[f++];)b.parentNode&&domUtils.remove(b,!0)}n.moveToBookmark(a).select()},queryCommandState:function(){return e(this)?1:0}}},UE.commands.touppercase=UE.commands.tolowercase={execCommand:function(e){var t=this.selection.getRange();if(t.collapsed)return t;for(var i=t.createBookmark(),n=i.end,o=function(e){return!domUtils.isBr(e)&&!domUtils.isWhitespace(e)},r=domUtils.getNextDomNode(i.start,!1,o);r&&domUtils.getPosition(r,n)&domUtils.POSITION_PRECEDING&&(3==r.nodeType&&(r.nodeValue=r.nodeValue["touppercase"==e?"toUpperCase":"toLowerCase"]()),(r=domUtils.getNextDomNode(r,!0,o))!==n););t.moveToBookmark(i).select()}},UE.commands.indent={execCommand:function(){var e=this.queryCommandState("indent")?"0em":this.options.indentValue||"2em";this.execCommand("Paragraph","p",{style:"text-indent:"+e})},queryCommandState:function(){var e=domUtils.filterNodeList(this.selection.getStartElementPath(),"p h1 h2 h3 h4 h5 h6");return e&&e.style.textIndent&&parseInt(e.style.textIndent)?1:0}},UE.commands.print={execCommand:function(){this.window.print()},notNeedUndo:1},UE.commands.preview={execCommand:function(){var e=window.open("","_blank","").document;e.open(),e.write('<!DOCTYPE html><html><head><meta charset="utf-8"/><script src="'+this.options.UEDITOR_HOME_URL+"ueditor.parse.js\"><\/script><script>setTimeout(function(){uParse('div',{rootPath: '"+this.options.UEDITOR_HOME_URL+"'})},300)<\/script></head><body><div>"+this.getContent(null,null,!0)+"</div></body></html>"),e.close()},notNeedUndo:1},UE.plugins.selectall=function(){this.commands.selectall={execCommand:function(){var e=this.body,t=this.selection.getRange();t.selectNodeContents(e),domUtils.isEmptyBlock(e)&&(browser.opera&&e.firstChild&&1==e.firstChild.nodeType&&t.setStartAtFirst(e.firstChild),t.collapse(!0)),t.select(!0)},notNeedUndo:1},this.addshortcutkey({selectAll:"ctrl+65"})},UE.plugins.paragraph=function(){var e=domUtils.isBlockElm,t=["TD","LI","PRE"];this.setOpt("paragraph",{p:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:""}),this.commands.paragraph={execCommand:function(i,n,o,r){var a=this.selection.getRange();if(a.collapsed){var s=this.document.createTextNode("p");if(a.insertNode(s),browser.ie){var l=s.previousSibling;l&&domUtils.isWhitespace(l)&&domUtils.remove(l),(l=s.nextSibling)&&domUtils.isWhitespace(l)&&domUtils.remove(l)}}if(a=function(i,n,o,r){var a,s=i.createBookmark(),l=function(e){return 1==e.nodeType?"br"!=e.tagName.toLowerCase()&&!domUtils.isBookmarkNode(e):!domUtils.isWhitespace(e)};i.enlarge(!0);for(var d,c=i.createBookmark(),u=domUtils.getNextDomNode(c.start,!1,l),m=i.cloneRange();u&&!(domUtils.getPosition(u,c.end)&domUtils.POSITION_FOLLOWING);)if(3!=u.nodeType&&e(u))u=domUtils.getNextDomNode(u,!0,l);else{for(m.setStartBefore(u);u&&u!==c.end&&!e(u);)d=u,u=domUtils.getNextDomNode(u,!1,null,function(t){return!e(t)});m.setEndAfter(d),a=i.document.createElement(n),o&&(domUtils.setAttributes(a,o),r&&"customstyle"==r&&o.style&&(a.style.cssText=o.style)),a.appendChild(m.extractContents()),domUtils.isEmptyNode(a)&&domUtils.fillChar(i.document,a),m.insertNode(a);var f=a.parentNode;e(f)&&!domUtils.isBody(a.parentNode)&&-1==utils.indexOf(t,f.tagName)&&(r&&"customstyle"==r||(f.getAttribute("dir")&&a.setAttribute("dir",f.getAttribute("dir")),f.style.cssText&&(a.style.cssText=f.style.cssText+";"+a.style.cssText),f.style.textAlign&&!a.style.textAlign&&(a.style.textAlign=f.style.textAlign),f.style.textIndent&&!a.style.textIndent&&(a.style.textIndent=f.style.textIndent),f.style.padding&&!a.style.padding&&(a.style.padding=f.style.padding)),o&&/h\d/i.test(f.tagName)&&!/h\d/i.test(a.tagName)?(domUtils.setAttributes(f,o),r&&"customstyle"==r&&o.style&&(f.style.cssText=o.style),domUtils.remove(a,!0),a=f):domUtils.remove(a.parentNode,!0)),u=-1!=utils.indexOf(t,f.tagName)?f:a,u=domUtils.getNextDomNode(u,!1,l)}return i.moveToBookmark(c).moveToBookmark(s)}(a,n,o,r),s&&(a.setStartBefore(s).collapse(!0),pN=s.parentNode,domUtils.remove(s),domUtils.isBlockElm(pN)&&domUtils.isEmptyNode(pN)&&domUtils.fillNode(this.document,pN)),browser.gecko&&a.collapsed&&1==a.startContainer.nodeType){var d=a.startContainer.childNodes[a.startOffset];d&&1==d.nodeType&&d.tagName.toLowerCase()==n&&a.setStart(d,0).collapse(!0)}return a.select(),!0},queryCommandValue:function(){var e=domUtils.filterNodeList(this.selection.getStartElementPath(),"p h1 h2 h3 h4 h5 h6");return e?e.tagName.toLowerCase():""}}},block=domUtils.isBlockElm,getObj=function(e){return domUtils.filterNodeList(e.selection.getStartElementPath(),function(e){return e&&1==e.nodeType&&e.getAttribute("dir")})},UE.commands.directionality={execCommand:function(e,t){var i=this.selection.getRange();if(i.collapsed){var n=this.document.createTextNode("d");i.insertNode(n)}return function(e,t,i){var n,o=function(e){return 1==e.nodeType?!domUtils.isBookmarkNode(e):!domUtils.isWhitespace(e)},r=getObj(t);if(r&&e.collapsed)return r.setAttribute("dir",i),e;n=e.createBookmark(),e.enlarge(!0);for(var a,s=e.createBookmark(),l=domUtils.getNextDomNode(s.start,!1,o),d=e.cloneRange();l&&!(domUtils.getPosition(l,s.end)&domUtils.POSITION_FOLLOWING);)if(3!=l.nodeType&&block(l))l=domUtils.getNextDomNode(l,!0,o);else{for(d.setStartBefore(l);l&&l!==s.end&&!block(l);)a=l,l=domUtils.getNextDomNode(l,!1,null,function(e){return!block(e)});d.setEndAfter(a);var c=d.getCommonAncestor();if(!domUtils.isBody(c)&&block(c))c.setAttribute("dir",i),l=c;else{var u=e.document.createElement("p");u.setAttribute("dir",i);var m=d.extractContents();u.appendChild(m),d.insertNode(u),l=u}l=domUtils.getNextDomNode(l,!1,o)}e.moveToBookmark(s).moveToBookmark(n)}(i,this,t),n&&(i.setStartBefore(n).collapse(!0),domUtils.remove(n)),i.select(),!0},queryCommandValue:function(){var e=getObj(this);return e?e.getAttribute("dir"):"ltr"}},UE.plugins.horizontal=function(){this.commands.horizontal={execCommand:function(e){var t=this;if(-1!==t.queryCommandState(e)){t.execCommand("insertHtml","<hr>");var i,n=t.selection.getRange(),o=n.startContainer;if(1==o.nodeType&&!o.childNodes[n.startOffset])(i=o.childNodes[n.startOffset-1])&&1==i.nodeType&&"HR"==i.tagName&&("p"==t.options.enterTag?(i=t.document.createElement("p"),n.insertNode(i),n.setStart(i,0).setCursor()):(i=t.document.createElement("br"),n.insertNode(i),n.setStartBefore(i).setCursor()));return!0}},queryCommandState:function(){return domUtils.filterNodeList(this.selection.getStartElementPath(),"table")?-1:0}},this.addListener("delkeydown",function(e,t){var i=this.selection.getRange();if(i.txtToElmBoundary(!0),domUtils.isStartInblock(i)){var n=i.startContainer.previousSibling;if(n&&domUtils.isTagNode(n,"hr"))return domUtils.remove(n),i.select(),domUtils.preventDefault(t),!0}})},UE.commands.time=UE.commands.date={execCommand:function(e,t){var i=new Date;this.execCommand("insertHtml","time"==e?function(e,t){var i=("0"+e.getHours()).slice(-2),n=("0"+e.getMinutes()).slice(-2),o=("0"+e.getSeconds()).slice(-2);return(t=t||"hh:ii:ss").replace(/hh/gi,i).replace(/ii/gi,n).replace(/ss/gi,o)}(i,t):function(e,t){var i=("000"+e.getFullYear()).slice(-4),n=i.slice(-2),o=("0"+(e.getMonth()+1)).slice(-2),r=("0"+e.getDate()).slice(-2);return(t=t||"yyyy-mm-dd").replace(/yyyy/gi,i).replace(/yy/gi,n).replace(/mm/gi,o).replace(/dd/gi,r)}(i,t))}},UE.plugins.rowspacing=function(){this.setOpt({rowspacingtop:["5","10","15","20","25"],rowspacingbottom:["5","10","15","20","25"]}),this.commands.rowspacing={execCommand:function(e,t,i){return this.execCommand("paragraph","p",{style:"margin-"+i+":"+t+"px"}),!0},queryCommandValue:function(e,t){var i=domUtils.filterNodeList(this.selection.getStartElementPath(),function(e){return domUtils.isBlockElm(e)});return i&&domUtils.getComputedStyle(i,"margin-"+t).replace(/[^\d]/g,"")||0}}},UE.plugins.lineheight=function(){this.setOpt({lineheight:["1","1.5","1.75","2","3","4","5"]}),this.commands.lineheight={execCommand:function(e,t){return this.execCommand("paragraph","p",{style:"line-height:"+("1"==t?"normal":t+"em")}),!0},queryCommandValue:function(){var e=domUtils.filterNodeList(this.selection.getStartElementPath(),function(e){return domUtils.isBlockElm(e)});if(e){var t=domUtils.getComputedStyle(e,"line-height");return"normal"==t?1:t.replace(/[^\d.]*/gi,"")}}}},UE.plugins.insertcode=function(){var e=this;e.ready(function(){utils.cssRule("pre","pre{margin:.5em 0;padding:.4em .6em;border-radius:8px;background:#f8f8f8;}",e.document)}),e.setOpt("insertcode",{as3:"ActionScript3",bash:"Bash/Shell",cpp:"C/C++",css:"Css",cf:"CodeFunction","c#":"C#",delphi:"Delphi",diff:"Diff",erlang:"Erlang",groovy:"Groovy",html:"Html",java:"Java",jfx:"JavaFx",js:"Javascript",pl:"Perl",php:"Php",plain:"Plain Text",ps:"PowerShell",python:"Python",ruby:"Ruby",scala:"Scala",sql:"Sql",vb:"Vb",xml:"Xml"}),e.commands.insertcode={execCommand:function(e,t){var i=this,n=i.selection.getRange(),o=domUtils.findParentByTagName(n.startContainer,"pre",!0);if(o)o.className="brush:"+t+";toolbar:false;";else{var r="";if(n.collapsed)r=browser.ie&&browser.ie11below?browser.version<=8?"&nbsp;":"":"<br/>";else{var a=n.extractContents(),s=i.document.createElement("div");s.appendChild(a),utils.each(UE.filterNode(UE.htmlparser(s.innerHTML.replace(/[\r\t]/g,"")),i.options.filterTxtRules).children,function(e){if(browser.ie&&browser.ie11below&&browser.version>8)"element"==e.type?"br"==e.tagName?r+="\n":dtd.$empty[e.tagName]||(utils.each(e.children,function(t){"element"==t.type?"br"==t.tagName?r+="\n":dtd.$empty[e.tagName]||(r+=t.innerText()):r+=t.data}),/\n$/.test(r)||(r+="\n")):r+=e.data+"\n",!e.nextSibling()&&/\n$/.test(r)&&(r=r.replace(/\n$/,""));else if(browser.ie&&browser.ie11below)"element"==e.type?"br"==e.tagName?r+="<br>":dtd.$empty[e.tagName]||(utils.each(e.children,function(t){"element"==t.type?"br"==t.tagName?r+="<br>":dtd.$empty[e.tagName]||(r+=t.innerText()):r+=t.data}),/br>$/.test(r)||(r+="<br>")):r+=e.data+"<br>",!e.nextSibling()&&/<br>$/.test(r)&&(r=r.replace(/<br>$/,""));else if(r+="element"==e.type?dtd.$empty[e.tagName]?"":e.innerText():e.data,!/br\/?\s*>$/.test(r)){if(!e.nextSibling())return;r+="<br>"}})}i.execCommand("inserthtml",'<pre id="coder" class="brush:'+t+';toolbar:false">'+r+"</pre>",!0),o=i.document.getElementById("coder"),domUtils.removeAttributes(o,"id");var l=o.previousSibling;l&&(3==l.nodeType&&1==l.nodeValue.length&&browser.ie&&6==browser.version||domUtils.isEmptyBlock(l))&&domUtils.remove(l);n=i.selection.getRange();domUtils.isEmptyBlock(o)?n.setStart(o,0).setCursor(!1,!0):n.selectNodeContents(o).select()}},queryCommandValue:function(){var e=this.selection.getStartElementPath(),t="";return utils.each(e,function(e){if("PRE"==e.nodeName){var i=e.className.match(/brush:([^;]+)/);return t=i&&i[1]?i[1]:"",!1}}),t}},e.addInputRule(function(e){utils.each(e.getNodesByTagName("pre"),function(e){var t=e.getNodesByTagName("br");if(t.length)browser.ie&&browser.ie11below&&browser.version>8&&utils.each(t,function(e){var t=UE.uNode.createText("\n");e.parentNode.insertBefore(t,e),e.parentNode.removeChild(e)});else if(!(browser.ie&&browser.ie11below&&browser.version>8)){var i=e.innerText().split(/\n/);e.innerHTML(""),utils.each(i,function(t){t.length&&e.appendChild(UE.uNode.createText(t)),e.appendChild(UE.uNode.createElement("br"))})}})}),e.addOutputRule(function(e){utils.each(e.getNodesByTagName("pre"),function(e){var t="";utils.each(e.children,function(e){"text"==e.type?t+=e.data.replace(/[ ]/g,"&nbsp;").replace(/\n$/,""):"br"==e.tagName?t+="\n":t+=dtd.$empty[e.tagName]?e.innerText():""}),e.innerText(t.replace(/(&nbsp;|\n)+$/,""))})}),e.notNeedCodeQuery={help:1,undo:1,redo:1,source:1,print:1,searchreplace:1,fullscreen:1,drafts:1,pagebreak:1,preview:1,insertparagraph:1,elementpath:1,insertcode:1,inserthtml:1,selectall:1};e.queryCommandState;e.queryCommandState=function(e){return!this.notNeedCodeQuery[e.toLowerCase()]&&this.selection&&this.queryCommandValue("insertcode")?-1:UE.Editor.prototype.queryCommandState.apply(this,arguments)},e.addListener("beforeenterkeydown",function(){var t=e.selection.getRange();if(i=domUtils.findParentByTagName(t.startContainer,"pre",!0)){if(e.fireEvent("saveScene"),t.collapsed||t.deleteContents(),!browser.ie||browser.ie9above){var i,n=e.document.createElement("br");for(t.insertNode(n).setStartAfter(n).collapse(!0),n.nextSibling||browser.ie&&!(browser.version>10)?t.setStartAfter(n):t.insertNode(n.cloneNode(!1)),i=n.previousSibling;i;)if(l=i,!(i=i.previousSibling)||"BR"==i.nodeName){i=l;break}if(i){for(var o="";i&&"BR"!=i.nodeName&&new RegExp("^[\\s"+domUtils.fillChar+"]*$").test(i.nodeValue);)o+=i.nodeValue,i=i.nextSibling;if("BR"!=i.nodeName)(d=i.nodeValue.match(new RegExp("^([\\s"+domUtils.fillChar+"]+)")))&&d[1]&&(o+=d[1]);o&&(o=e.document.createTextNode(o),t.insertNode(o).setStartAfter(o))}t.collapse(!0).select(!0)}else if(browser.version>8){var r=e.document.createTextNode("\n"),a=t.startContainer;if(0==t.startOffset){if(a.previousSibling){t.insertNode(r);var s=e.document.createTextNode(" ");t.setStartAfter(r).insertNode(s).setStart(s,0).collapse(!0).select(!0)}}else{t.insertNode(r).setStartAfter(r);s=e.document.createTextNode(" ");(a=t.startContainer.childNodes[t.startOffset])&&!/^\n/.test(a.nodeValue)&&t.setStartBefore(r),t.insertNode(s).setStart(s,0).collapse(!0).select(!0)}}else{var l;n=e.document.createElement("br");for(t.insertNode(n),t.insertNode(e.document.createTextNode(domUtils.fillChar)),t.setStartAfter(n),i=n.previousSibling;i;)if(l=i,!(i=i.previousSibling)||"BR"==i.nodeName){i=l;break}if(i){var d;for(o="";i&&"BR"!=i.nodeName&&new RegExp("^[ "+domUtils.fillChar+"]*$").test(i.nodeValue);)o+=i.nodeValue,i=i.nextSibling;if("BR"!=i.nodeName)(d=i.nodeValue.match(new RegExp("^([ "+domUtils.fillChar+"]+)")))&&d[1]&&(o+=d[1]);o=e.document.createTextNode(o),t.insertNode(o).setStartAfter(o)}t.collapse(!0).select()}return e.fireEvent("saveScene"),!0}}),e.addListener("tabkeydown",function(t,i){var n=e.selection.getRange(),o=domUtils.findParentByTagName(n.startContainer,"pre",!0);if(o){if(e.fireEvent("saveScene"),i.shiftKey);else if(n.collapsed){var r=e.document.createTextNode("    ");n.insertNode(r).setStartAfter(r).collapse(!0).select(!0)}else{for(var a=n.createBookmark(),s=a.start.previousSibling;s;){if(o.firstChild===s&&!domUtils.isBr(s)){o.insertBefore(e.document.createTextNode("    "),s);break}if(domUtils.isBr(s)){o.insertBefore(e.document.createTextNode("    "),s.nextSibling);break}s=s.previousSibling}var l=a.end;for(s=a.start.nextSibling,o.firstChild===a.start&&o.insertBefore(e.document.createTextNode("    "),s.nextSibling);s&&s!==l;){if(domUtils.isBr(s)&&s.nextSibling){if(s.nextSibling===l)break;o.insertBefore(e.document.createTextNode("    "),s.nextSibling)}s=s.nextSibling}n.moveToBookmark(a).select()}return e.fireEvent("saveScene"),!0}}),e.addListener("beforeinserthtml",function(e,t){var i=this,n=i.selection.getRange();if(domUtils.findParentByTagName(n.startContainer,"pre",!0)){n.collapsed||n.deleteContents();var o="";if(browser.ie&&browser.version>8){utils.each(UE.filterNode(UE.htmlparser(t),i.options.filterTxtRules).children,function(e){"element"==e.type?"br"==e.tagName?o+="\n":dtd.$empty[e.tagName]||(utils.each(e.children,function(t){"element"==t.type?"br"==t.tagName?o+="\n":dtd.$empty[e.tagName]||(o+=t.innerText()):o+=t.data}),/\n$/.test(o)||(o+="\n")):o+=e.data+"\n",!e.nextSibling()&&/\n$/.test(o)&&(o=o.replace(/\n$/,""))});var r=i.document.createTextNode(utils.html(o.replace(/&nbsp;/g," ")));n.insertNode(r).selectNode(r).select()}else{var a=i.document.createDocumentFragment();utils.each(UE.filterNode(UE.htmlparser(t),i.options.filterTxtRules).children,function(e){"element"==e.type?"br"==e.tagName?a.appendChild(i.document.createElement("br")):dtd.$empty[e.tagName]||(utils.each(e.children,function(t){"element"==t.type?"br"==t.tagName?a.appendChild(i.document.createElement("br")):dtd.$empty[e.tagName]||a.appendChild(i.document.createTextNode(utils.html(t.innerText().replace(/&nbsp;/g," ")))):a.appendChild(i.document.createTextNode(utils.html(t.data.replace(/&nbsp;/g," "))))}),"BR"!=a.lastChild.nodeName&&a.appendChild(i.document.createElement("br"))):a.appendChild(i.document.createTextNode(utils.html(e.data.replace(/&nbsp;/g," ")))),e.nextSibling()||"BR"!=a.lastChild.nodeName||a.removeChild(a.lastChild)}),n.insertNode(a).select()}return!0}}),e.addListener("keydown",function(e,t){if(40==(t.keyCode||t.which)){var i,n=this.selection.getRange(),o=n.startContainer;if(n.collapsed&&(i=domUtils.findParentByTagName(n.startContainer,"pre",!0))&&!i.nextSibling){for(var r=i.lastChild;r&&"BR"==r.nodeName;)r=r.previousSibling;(r===o||n.startContainer===i&&n.startOffset==i.childNodes.length)&&(this.execCommand("insertparagraph"),domUtils.preventDefault(t))}}}),e.addListener("delkeydown",function(t,i){var n=this.selection.getRange();n.txtToElmBoundary(!0);var o=n.startContainer;if(domUtils.isTagNode(o,"pre")&&n.collapsed&&domUtils.isStartInblock(n)){var r=e.document.createElement("p");return domUtils.fillNode(e.document,r),o.parentNode.insertBefore(r,o),domUtils.remove(o),n.setStart(r,0).setCursor(!1,!0),domUtils.preventDefault(i),!0}})},UE.commands.cleardoc={execCommand:function(e){var t=this,i=t.options.enterTag,n=t.selection.getRange();"br"==i?(t.body.innerHTML="<br/>",n.setStart(t.body,0).setCursor()):(t.body.innerHTML="",n.setStart(t.body.firstChild,0).setCursor(!1,!0)),setTimeout(function(){t.fireEvent("clearDoc")},0)}},UE.plugin.register("anchor",function(){return{bindEvents:{ready:function(){utils.cssRule("anchor",".anchorclass{background: url('"+this.options.themePath+this.options.theme+"/images/anchor.gif') no-repeat scroll left center transparent;cursor: auto;display: inline-block;height: 16px;width: 15px;}",this.document)}},outputRule:function(e){utils.each(e.getNodesByTagName("img"),function(e){var t;(t=e.getAttr("anchorname"))&&(e.tagName="a",e.setAttr({anchorname:"",name:t,class:""}))})},inputRule:function(e){utils.each(e.getNodesByTagName("a"),function(e){e.getAttr("name")&&!e.getAttr("href")&&(e.tagName="img",e.setAttr({anchorname:e.getAttr("name"),class:"anchorclass"}),e.setAttr("name"))})},commands:{anchor:{execCommand:function(e,t){var i=this.selection.getRange(),n=i.getClosedNode();if(n&&n.getAttribute("anchorname"))t?n.setAttribute("anchorname",t):(i.setStartBefore(n).setCursor(),domUtils.remove(n));else if(t){var o=this.document.createElement("img");i.collapse(!0),domUtils.setAttributes(o,{anchorname:t,class:"anchorclass"}),i.insertNode(o).setStartAfter(o).setCursor(!1,!0)}}}}}}),UE.plugins.wordcount=function(){var e,t=this;t.setOpt("wordCount",!0),t.addListener("contentchange",function(){t.fireEvent("wordcount")}),t.addListener("ready",function(){var t=this;domUtils.on(t.body,"keyup",function(i){(i.keyCode||i.which)in{16:1,18:1,20:1,37:1,38:1,39:1,40:1}||(clearTimeout(e),e=setTimeout(function(){t.fireEvent("wordcount")},200))})})},UE.plugins.pagebreak=function(){var e=this,t=["td"];function i(t){if(domUtils.isEmptyBlock(t)){for(var i,n=t.firstChild;n&&1==n.nodeType&&domUtils.isEmptyBlock(n);)i=n,n=n.firstChild;!i&&(i=t),domUtils.fillNode(e.document,i)}}function n(e){return e&&1==e.nodeType&&"HR"==e.tagName&&"pagebreak"==e.className}e.setOpt("pageBreakTag","_ueditor_page_break_tag_"),e.ready(function(){utils.cssRule("pagebreak",".pagebreak{display:block;clear:both !important;cursor:default !important;width: 100% !important;margin:0;}",e.document)}),e.addInputRule(function(t){t.traversal(function(t){if("text"==t.type&&t.data==e.options.pageBreakTag){var i=UE.uNode.createElement('<hr class="pagebreak" noshade="noshade" size="5" style="-webkit-user-select: none;">');t.parentNode.insertBefore(i,t),t.parentNode.removeChild(t)}})}),e.addOutputRule(function(t){utils.each(t.getNodesByTagName("hr"),function(t){if("pagebreak"==t.getAttr("class")){var i=UE.uNode.createText(e.options.pageBreakTag);t.parentNode.insertBefore(i,t),t.parentNode.removeChild(t)}})}),e.commands.pagebreak={execCommand:function(){var o=e.selection.getRange(),r=e.document.createElement("hr");domUtils.setAttributes(r,{class:"pagebreak",noshade:"noshade",size:"5"}),domUtils.unSelectable(r);var a=domUtils.findParentByTagName(o.startContainer,t,!0),s=[];if(a)switch(a.tagName){case"TD":if((u=a.parentNode).previousSibling)u.parentNode.insertBefore(r,u),s=domUtils.findParents(r);else{var l=domUtils.findParentByTagName(u,"table");l.parentNode.insertBefore(r,l),s=domUtils.findParents(r,!0)}r!==(u=s[1])&&domUtils.breakParent(r,u),e.fireEvent("afteradjusttable",e.document)}else{if(!o.collapsed){o.deleteContents();for(var d=o.startContainer;!domUtils.isBody(d)&&domUtils.isBlockElm(d)&&domUtils.isEmptyNode(d);)o.setStartBefore(d).collapse(!0),domUtils.remove(d),d=o.startContainer}o.insertNode(r);for(var c,u=r.parentNode;!domUtils.isBody(u);)domUtils.breakParent(r,u),(c=r.nextSibling)&&domUtils.isEmptyBlock(c)&&domUtils.remove(c),u=r.parentNode;c=r.nextSibling;var m=r.previousSibling;if(n(m)?domUtils.remove(m):m&&i(m),c)n(c)?domUtils.remove(c):i(c),o.setEndAfter(r).collapse(!1);else{var f=e.document.createElement("p");r.parentNode.appendChild(f),domUtils.fillNode(e.document,f),o.setStart(f,0).collapse(!0)}o.select(!0)}}}},UE.plugin.register("wordimage",function(){var e=this,t=[];return{commands:{wordimage:{execCommand:function(){for(var t,i=domUtils.getElementsByTagName(e.body,"img"),n=[],o=0;t=i[o++];){var r=t.getAttribute("word_img");r&&n.push(r)}return n},queryCommandState:function(){t=domUtils.getElementsByTagName(e.body,"img");for(var i,n=0;i=t[n++];)if(i.getAttribute("word_img"))return 1;return-1},notNeedUndo:!0}},inputRule:function(t){utils.each(t.getNodesByTagName("img"),function(t){var i=t.attrs,n=parseInt(i.width)<128||parseInt(i.height)<43,o=e.options,r=o.UEDITOR_HOME_URL+"themes/default/images/spacer.gif";i.src&&/^(?:(file:\/+))/.test(i.src)&&t.setAttr({width:i.width,height:i.height,alt:i.alt,word_img:i.src,src:r,style:"background:url("+(n?o.themePath+o.theme+"/images/word.gif":o.langPath+o.lang+"/images/localimage.png")+") no-repeat center center;border:1px solid #ddd"})})}}}),UE.plugins.dragdrop=function(){var e=this;e.ready(function(){domUtils.on(this.body,"dragend",function(){var t=e.selection.getRange(),i=t.getClosedNode()||e.selection.getStart();if(i&&"IMG"==i.tagName){for(var n,o=i.previousSibling;(n=i.nextSibling)&&1==n.nodeType&&"SPAN"==n.tagName&&!n.firstChild;)domUtils.remove(n);(!o||1!=o.nodeType||domUtils.isEmptyBlock(o))&&o||n&&(!n||domUtils.isEmptyBlock(n))||(o&&"P"==o.tagName&&!domUtils.isEmptyBlock(o)?(o.appendChild(i),domUtils.moveChild(n,o),domUtils.remove(n)):n&&"P"==n.tagName&&!domUtils.isEmptyBlock(n)&&n.insertBefore(i,n.firstChild),o&&"P"==o.tagName&&domUtils.isEmptyBlock(o)&&domUtils.remove(o),n&&"P"==n.tagName&&domUtils.isEmptyBlock(n)&&domUtils.remove(n),t.selectNode(i).select(),e.fireEvent("saveScene"))}})}),e.addListener("keyup",function(t,i){if(13==(i.keyCode||i.which)){var n,o=e.selection.getRange();(n=domUtils.findParentByTagName(o.startContainer,"p",!0))&&"center"==domUtils.getComputedStyle(n,"text-align")&&domUtils.removeStyle(n,"text-align")}})},UE.plugins.undo=function(){var e,t=this,i=t.options.maxUndoCount||20,n=t.options.maxInputCount||20,o=new RegExp(domUtils.fillChar+"|</hr>","gi"),r={ol:1,ul:1,table:1,tbody:1,tr:1,body:1},a=t.options.autoClearEmptyNode;function s(e,t){if(e.length!=t.length)return 0;for(var i=0,n=e.length;i<n;i++)if(e[i]!=t[i])return 0;return 1}t.undoManger=new function(){this.list=[],this.index=0,this.hasUndo=!1,this.hasRedo=!1,this.undo=function(){if(this.hasUndo){if(!this.list[this.index-1]&&1==this.list.length)return void this.reset();for(;this.list[this.index].content==this.list[this.index-1].content;)if(this.index--,0==this.index)return this.restore(0);this.restore(--this.index)}},this.redo=function(){if(this.hasRedo){for(;this.list[this.index].content==this.list[this.index+1].content;)if(this.index++,this.index==this.list.length-1)return this.restore(this.index);this.restore(++this.index)}},this.restore=function(){var e=this.editor,t=this.list[this.index],i=UE.htmlparser(t.content.replace(o,""));e.options.autoClearEmptyNode=!1,e.filterInputRule(i),e.options.autoClearEmptyNode=a,e.document.body.innerHTML=i.toHtml(),e.fireEvent("afterscencerestore"),browser.ie&&utils.each(domUtils.getElementsByTagName(e.document,"td th caption p"),function(t){domUtils.isEmptyNode(t)&&domUtils.fillNode(e.document,t)});try{var n=new dom.Range(e.document).moveToAddress(t.address);n.select(r[n.startContainer.nodeName.toLowerCase()])}catch(e){}this.update(),this.clearKey(),e.fireEvent("reset",!0)},this.getScene=function(){var e=this.editor,t=e.selection.getRange().createAddress(!1,!0);e.fireEvent("beforegetscene");var i=UE.htmlparser(e.body.innerHTML);e.options.autoClearEmptyNode=!1,e.filterOutputRule(i),e.options.autoClearEmptyNode=a;var n=i.toHtml();return e.fireEvent("aftergetscene"),{address:t,content:n}},this.save=function(n,o){clearTimeout(e);var r,a,l=this.getScene(o),d=this.list[this.index];d&&d.content!=l.content&&t.trigger("contentchange"),d&&d.content==l.content&&(n||(r=d.address,a=l.address,r.collapsed==a.collapsed&&s(r.startAddress,a.startAddress)&&s(r.endAddress,a.endAddress)))||(this.list=this.list.slice(0,this.index+1),this.list.push(l),this.list.length>i&&this.list.shift(),this.index=this.list.length-1,this.clearKey(),this.update())},this.update=function(){this.hasRedo=!!this.list[this.index+1],this.hasUndo=!!this.list[this.index-1]},this.reset=function(){this.list=[],this.index=0,this.hasUndo=!1,this.hasRedo=!1,this.clearKey()},this.clearKey=function(){d=0,null}},t.undoManger.editor=t,t.addListener("saveScene",function(){var e=Array.prototype.splice.call(arguments,1);this.undoManger.save.apply(this.undoManger,e)}),t.addListener("reset",function(e,t){t||this.undoManger.reset()}),t.commands.redo=t.commands.undo={execCommand:function(e){this.undoManger[e]()},queryCommandState:function(e){return this.undoManger["has"+("undo"==e.toLowerCase()?"Undo":"Redo")]?0:-1},notNeedUndo:1};var l={16:1,17:1,18:1,37:1,38:1,39:1,40:1},d=0,c=!1;t.addListener("ready",function(){domUtils.on(this.body,"compositionstart",function(){c=!0}),domUtils.on(this.body,"compositionend",function(){c=!1})}),t.addshortcutkey({Undo:"ctrl+90",Redo:"ctrl+89"});var u=!0;t.addListener("keydown",function(t,i){var o=this,r=i.keyCode||i.which;if(!(l[r]||i.ctrlKey||i.metaKey||i.shiftKey||i.altKey)){if(c)return;if(!o.selection.getRange().collapsed)return o.undoManger.save(!1,!0),void(u=!1);function a(e){e.undoManger.save(!1,!0),e.fireEvent("selectionchange")}0==o.undoManger.list.length&&o.undoManger.save(!0),clearTimeout(e),e=setTimeout(function(){if(c)var e=setInterval(function(){c||(a(o),clearInterval(e))},300);else a(o)},200),r,++d>=n&&a(o)}}),t.addListener("keyup",function(e,t){var i=t.keyCode||t.which;if(!(l[i]||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)){if(c)return;u||(this.undoManger.save(!1,!0),u=!0)}}),t.stopCmdUndo=function(){t.__hasEnterExecCommand=!0},t.startCmdUndo=function(){t.__hasEnterExecCommand=!1}},UE.plugin.register("copy",function(){return{bindEvents:{ready:function(){}},commands:{copy:{execCommand:function(e){}}}}}),UE.plugins.paste=function(){function e(e){var t=this.document;if(!t.getElementById("baidu_pastebin")){var i=this.selection.getRange(),n=i.createBookmark(),o=t.createElement("div");o.id="baidu_pastebin",browser.webkit&&o.appendChild(t.createTextNode(domUtils.fillChar+domUtils.fillChar)),t.body.appendChild(o),n.start.style.display="",o.style.cssText="position:absolute;width:1px;height:1px;overflow:hidden;left:-1000px;white-space:nowrap;top:"+domUtils.getXY(n.start).y+"px",i.selectNodeContents(o).select(!0),setTimeout(function(){if(browser.webkit)for(var r,a=0,s=t.querySelectorAll("#baidu_pastebin");r=s[a++];){if(!domUtils.isEmptyNode(r)){o=r;break}domUtils.remove(r)}try{o.parentNode.removeChild(o)}catch(e){}i.moveToBookmark(n).select(!0),e(o)},0)}}var t,i,n,o=this;function r(e){return e.replace(/<(\/?)([\w\-]+)([^>]*)>/gi,function(e,t,i,n){return{img:1}[i=i.toLowerCase()]?e:(n=n.replace(/([\w\-]*?)\s*=\s*(("([^"]*)")|('([^']*)')|([^\s>]+))/gi,function(e,t,i){return{src:1,href:1,name:1}[t.toLowerCase()]?t+"="+i+" ":""}),{span:1,div:1}[i]?"":"<"+t+i+" "+utils.trim(n)+">")})}function a(e){var a;if(e.firstChild){for(var s,l=domUtils.getElementsByTagName(e,"span"),d=0;s=l[d++];)"_baidu_cut_start"!=s.id&&"_baidu_cut_end"!=s.id||domUtils.remove(s);if(browser.webkit){var c,u=e.querySelectorAll("div br");for(d=0;c=u[d++];){var m=c.parentNode;"DIV"==m.tagName&&1==m.childNodes.length&&(m.innerHTML="",domUtils.remove(m))}var f,h=e.querySelectorAll("#baidu_pastebin");for(d=0;f=h[d++];){var p=o.document.createElement("p");for(f.parentNode.insertBefore(p,f);f.firstChild;)p.appendChild(f.firstChild);domUtils.remove(f)}var g=e.querySelectorAll("meta");for(d=0;b=g[d++];)domUtils.remove(b);u=e.querySelectorAll("br");for(d=0;b=u[d++];)/^apple-/i.test(b.className)&&domUtils.remove(b)}if(browser.gecko){var v=e.querySelectorAll("[_moz_dirty]");for(d=0;b=v[d++];)b.removeAttribute("_moz_dirty")}if(!browser.ie){var b,y=e.querySelectorAll("span.Apple-style-span");for(d=0;b=y[d++];)domUtils.remove(b,!0)}a=e.innerHTML,a=UE.filterWord(a);var C=UE.htmlparser(a);if(o.options.filterRules&&UE.filterNode(C,o.options.filterRules),o.filterInputRule(C),browser.webkit){var N=C.lastChild();N&&"element"==N.type&&"br"==N.tagName&&C.removeChild(N),utils.each(o.body.querySelectorAll("div"),function(e){domUtils.isEmptyBlock(e)&&domUtils.remove(e,!0)})}if(a={html:C.toHtml()},o.fireEvent("beforepaste",a,C),!a.html)return;C=UE.htmlparser(a.html,!0),1===o.queryCommandState("pasteplain")?o.execCommand("insertHtml",UE.filterNode(C,o.options.filterTxtRules).toHtml(),!0):(UE.filterNode(C,o.options.filterTxtRules),t=C.toHtml(),i=a.html,n=o.selection.getRange().createAddress(!0),o.execCommand("insertHtml",!0===o.getOpt("retainOnlyLabelPasted")?r(i):i,!0)),o.fireEvent("afterpaste",a)}}o.setOpt({retainOnlyLabelPasted:!1}),o.addListener("pasteTransfer",function(e,a){if(n&&t&&i&&t!=i){var s=o.selection.getRange();if(s.moveToAddress(n,!0),!s.collapsed){for(;!domUtils.isBody(s.startContainer);){var l=s.startContainer;if(1==l.nodeType){if(!(l=l.childNodes[s.startOffset])){s.setStartBefore(s.startContainer);continue}var d=l.previousSibling;d&&3==d.nodeType&&new RegExp("^[\n\r\t "+domUtils.fillChar+"]*$").test(d.nodeValue)&&s.setStartBefore(d)}if(0!=s.startOffset)break;s.setStartBefore(s.startContainer)}for(;!domUtils.isBody(s.endContainer);){var c=s.endContainer;if(1==c.nodeType){if(!(c=c.childNodes[s.endOffset])){s.setEndAfter(s.endContainer);continue}var u=c.nextSibling;u&&3==u.nodeType&&new RegExp("^[\n\r\t"+domUtils.fillChar+"]*$").test(u.nodeValue)&&s.setEndAfter(u)}if(s.endOffset!=s.endContainer[3==s.endContainer.nodeType?"nodeValue":"childNodes"].length)break;s.setEndAfter(s.endContainer)}}s.deleteContents(),s.select(!0),o.__hasEnterExecCommand=!0;var m=i;2===a?m=r(m):a&&(m=t),o.execCommand("inserthtml",m,!0),o.__hasEnterExecCommand=!1;for(var f=o.selection.getRange();!domUtils.isBody(f.startContainer)&&!f.startOffset&&f.startContainer[3==f.startContainer.nodeType?"nodeValue":"childNodes"].length;)f.setStartBefore(f.startContainer);var h=f.createAddress(!0);n.endAddress=h.startAddress}}),o.addListener("ready",function(){domUtils.on(o.body,"cut",function(){!o.selection.getRange().collapsed&&o.undoManger&&o.undoManger.save()}),domUtils.on(o.body,browser.ie||browser.opera?"keydown":"paste",function(t){(!browser.ie&&!browser.opera||(t.ctrlKey||t.metaKey)&&"86"==t.keyCode)&&e.call(o,function(e){a(e)})})}),o.commands.paste={execCommand:function(t){browser.ie?(e.call(o,function(e){a(e)}),o.document.execCommand("paste")):alert(o.getLang("pastemsg"))}}},UE.plugins.pasteplain=function(){this.setOpt({pasteplain:!1,filterTxtRules:function(){function e(e){e.tagName="p",e.setStyle()}function t(e){e.parentNode.removeChild(e,!0)}return{"-":"script style object iframe embed input select",p:{$:{}},br:{$:{}},div:function(e){for(var t,i=UE.uNode.createElement("p");t=e.firstChild();)"text"!=t.type&&UE.dom.dtd.$block[t.tagName]?i.firstChild()?(e.parentNode.insertBefore(i,e),i=UE.uNode.createElement("p")):e.parentNode.insertBefore(t,e):i.appendChild(t);i.firstChild()&&e.parentNode.insertBefore(i,e),e.parentNode.removeChild(e)},ol:t,ul:t,dl:t,dt:t,dd:t,li:t,caption:e,th:e,tr:e,h1:e,h2:e,h3:e,h4:e,h5:e,h6:e,td:function(e){!!e.innerText()&&e.parentNode.insertAfter(UE.uNode.createText(" &nbsp; &nbsp;"),e),e.parentNode.removeChild(e,e.innerText())}}}()});var e=this.options.pasteplain;this.commands.pasteplain={queryCommandState:function(){return e?1:0},execCommand:function(){e=0|!e},notNeedUndo:1}},UE.plugins.list=function(){var e=this,t={TD:1,PRE:1,BLOCKQUOTE:1},i={cn:"cn-1-",cn1:"cn-2-",cn2:"cn-3-",num:"num-1-",num1:"num-2-",num2:"num-3-",dash:"dash",dot:"dot"};function n(e){var t=[];for(var i in e)t.push(i);return t}e.setOpt({autoTransWordToList:!1,insertorderedlist:{num:"",num1:"",num2:"",cn:"",cn1:"",cn2:"",decimal:"","lower-alpha":"","lower-roman":"","upper-alpha":"","upper-roman":""},insertunorderedlist:{circle:"",disc:"",square:"",dash:"",dot:""},listDefaultPaddingLeft:"30",listiconpath:"http://bs.baidu.com/listicon/",maxListLevel:-1,disablePInList:!1});var o={OL:n(e.options.insertorderedlist),UL:n(e.options.insertunorderedlist)},r=e.options.listiconpath;for(var a in i)e.options.insertorderedlist.hasOwnProperty(a)||e.options.insertunorderedlist.hasOwnProperty(a)||delete i[a];function s(e){var t=e.className;return domUtils.hasClass(e,/custom_/)?t.match(/custom_(\w+)/)[1]:domUtils.getStyle(e,"list-style-type")}function l(e,t){utils.each(domUtils.getElementsByTagName(e,"ol ul"),function(n){if(domUtils.inDoc(n,e)){var r=n.parentNode;if(r.tagName==n.tagName){var a=s(n)||("OL"==n.tagName?"decimal":"disc");if(a==(s(r)||("OL"==r.tagName?"decimal":"disc"))){var l=utils.indexOf(o[n.tagName],a);l=l+1==o[n.tagName].length?0:l+1,c(n,o[n.tagName][l])}}var u=0,m=2;domUtils.hasClass(n,/custom_/)?/[ou]l/i.test(r.tagName)&&domUtils.hasClass(r,/custom_/)||(m=1):/[ou]l/i.test(r.tagName)&&domUtils.hasClass(r,/custom_/)&&(m=3);var f=domUtils.getStyle(n,"list-style-type");f&&(n.style.cssText="list-style-type:"+f),n.className=utils.trim(n.className.replace(/list-paddingleft-\w+/,""))+" list-paddingleft-"+m,utils.each(domUtils.getElementsByTagName(n,"li"),function(e){if(e.style.cssText&&(e.style.cssText=""),e.firstChild){if(e.parentNode===n){if(u++,domUtils.hasClass(n,/custom_/)){var t=1,o=s(n);if("OL"==n.tagName){if(o)switch(o){case"cn":case"cn1":case"cn2":u>10&&(u%10==0||u>10&&u<20)?t=2:u>20&&(t=3);break;case"num2":u>9&&(t=2)}e.className="list-"+i[o]+u+" list-"+o+"-paddingleft-"+t}else e.className="list-"+i[o]+" list-"+o+"-paddingleft"}else e.className=e.className.replace(/list-[\w\-]+/gi,"");var r=e.getAttribute("class");null===r||r.replace(/\s/g,"")||domUtils.removeAttributes(e,"class")}}else domUtils.remove(e)}),!t&&d(n,n.tagName.toLowerCase(),s(n)||domUtils.getStyle(n,"list-style-type"),!0)}})}function d(e,t,i,n){var o=e.nextSibling;o&&1==o.nodeType&&o.tagName.toLowerCase()==t&&(s(o)||domUtils.getStyle(o,"list-style-type")||("ol"==t?"decimal":"disc"))==i&&(domUtils.moveChild(o,e),0==o.childNodes.length&&domUtils.remove(o)),o&&domUtils.isFillChar(o)&&domUtils.remove(o);var r=e.previousSibling;r&&1==r.nodeType&&r.tagName.toLowerCase()==t&&(s(r)||domUtils.getStyle(r,"list-style-type")||("ol"==t?"decimal":"disc"))==i&&domUtils.moveChild(e,r),r&&domUtils.isFillChar(r)&&domUtils.remove(r),!n&&domUtils.isEmptyBlock(e)&&domUtils.remove(e),s(e)&&l(e.ownerDocument,!0)}function c(e,t){i[t]&&(e.className="custom_"+t);try{domUtils.setStyle(e,"list-style-type",t)}catch(e){}}function u(e){var t=e.previousSibling;t&&domUtils.isEmptyBlock(t)&&domUtils.remove(t),(t=e.nextSibling)&&domUtils.isEmptyBlock(t)&&domUtils.remove(t)}function m(e){for(;e&&!domUtils.isBody(e);){if("TABLE"==e.nodeName)return null;if("LI"==e.nodeName)return e;e=e.parentNode}}e.ready(function(){var t=[];for(var n in i){if("dash"==n||"dot"==n)t.push("li.list-"+i[n]+"{background-image:url("+r+i[n]+".gif)}"),t.push("ul.custom_"+n+"{list-style:none;}ul.custom_"+n+" li{background-position:0 3px;background-repeat:no-repeat}");else{for(var o=0;o<99;o++)t.push("li.list-"+i[n]+o+"{background-image:url("+r+"list-"+i[n]+o+".gif)}");t.push("ol.custom_"+n+"{list-style:none;}ol.custom_"+n+" li{background-position:0 3px;background-repeat:no-repeat}")}switch(n){case"cn":t.push("li.list-"+n+"-paddingleft-1{padding-left:25px}"),t.push("li.list-"+n+"-paddingleft-2{padding-left:40px}"),t.push("li.list-"+n+"-paddingleft-3{padding-left:55px}");break;case"cn1":t.push("li.list-"+n+"-paddingleft-1{padding-left:30px}"),t.push("li.list-"+n+"-paddingleft-2{padding-left:40px}"),t.push("li.list-"+n+"-paddingleft-3{padding-left:55px}");break;case"cn2":t.push("li.list-"+n+"-paddingleft-1{padding-left:40px}"),t.push("li.list-"+n+"-paddingleft-2{padding-left:55px}"),t.push("li.list-"+n+"-paddingleft-3{padding-left:68px}");break;case"num":case"num1":t.push("li.list-"+n+"-paddingleft-1{padding-left:25px}");break;case"num2":t.push("li.list-"+n+"-paddingleft-1{padding-left:35px}"),t.push("li.list-"+n+"-paddingleft-2{padding-left:40px}");break;case"dash":t.push("li.list-"+n+"-paddingleft{padding-left:35px}");break;case"dot":t.push("li.list-"+n+"-paddingleft{padding-left:20px}")}}t.push(".list-paddingleft-1{padding-left:0}"),t.push(".list-paddingleft-2{padding-left:"+e.options.listDefaultPaddingLeft+"px}"),t.push(".list-paddingleft-3{padding-left:"+2*e.options.listDefaultPaddingLeft+"px}"),utils.cssRule("list","ol,ul{margin:0;pading:0;"+(browser.ie?"":"width:95%")+"}li{clear:both;}"+t.join("\n"),e.document)}),e.ready(function(){domUtils.on(e.body,"cut",function(){setTimeout(function(){var t,i=e.selection.getRange();if(!i.collapsed&&(t=domUtils.findParentByTagName(i.startContainer,"li",!0))&&!t.nextSibling&&domUtils.isEmptyBlock(t)){var n,o=t.parentNode;if(n=o.previousSibling)domUtils.remove(o),i.setStartAtLast(n).collapse(!0),i.select(!0);else if(n=o.nextSibling)domUtils.remove(o),i.setStartAtFirst(n).collapse(!0),i.select(!0);else{var r=e.document.createElement("p");domUtils.fillNode(e.document,r),o.parentNode.insertBefore(r,o),domUtils.remove(o),i.setStart(r,0).collapse(!0),i.select(!0)}}})})}),e.addListener("beforepaste",function(e,t){var n,r=this.selection.getRange(),a=UE.htmlparser(t.html,!0);if(n=domUtils.findParentByTagName(r.startContainer,"li",!0)){var l=n.parentNode,d="OL"==l.tagName?"ul":"ol";utils.each(a.getNodesByTagName(d),function(t){if(t.tagName=l.tagName,t.setAttr(),t.parentNode===a)e=s(l)||("OL"==l.tagName?"decimal":"disc");else{var n=t.parentNode.getAttr("class");(e=n&&/custom_/.test(n)?n.match(/custom_(\w+)/)[1]:t.parentNode.getStyle("list-style-type"))||(e="OL"==l.tagName?"decimal":"disc")}var r=utils.indexOf(o[l.tagName],e);t.parentNode!==a&&(r=r+1==o[l.tagName].length?0:r+1);var d=o[l.tagName][r];i[d]?t.setAttr("class","custom_"+d):t.setStyle("list-style-type",d)})}t.html=a.toHtml()}),!0===e.getOpt("disablePInList")&&e.addOutputRule(function(e){utils.each(e.getNodesByTagName("li"),function(e){var t=[],i=0;utils.each(e.children,function(n){if("p"==n.tagName){for(var o;o=n.children.pop();)t.splice(i,0,o),o.parentNode=e,lastNode=o;if(!(o=t[t.length-1])||"element"!=o.type||"br"!=o.tagName){var r=UE.uNode.createElement("br");r.parentNode=e,t.push(r)}i=t.length}}),t.length&&(e.children=t)})}),e.addInputRule(function(t){if(utils.each(t.getNodesByTagName("li"),function(e){for(var t,i=UE.uNode.createElement("p"),n=0;t=e.children[n];)"text"==t.type||dtd.p[t.tagName]?i.appendChild(t):i.firstChild()?(e.insertBefore(i,t),i=UE.uNode.createElement("p"),n+=2):n++;(i.firstChild()&&!i.parentNode||!e.firstChild())&&e.appendChild(i),i.firstChild()||i.innerHTML(browser.ie?"&nbsp;":"<br/>");var o=e.firstChild(),r=o.lastChild();r&&"text"==r.type&&/^\s*$/.test(r.data)&&o.removeChild(r)}),e.options.autoTransWordToList){var n={num1:/^\d+\)/,decimal:/^\d+\./,"lower-alpha":/^[a-z]+\)/,"upper-alpha":/^[A-Z]+\./,cn:/^[\u4E00\u4E8C\u4E09\u56DB\u516d\u4e94\u4e03\u516b\u4e5d]+[\u3001]/,cn2:/^\([\u4E00\u4E8C\u4E09\u56DB\u516d\u4e94\u4e03\u516b\u4e5d]+\)/},o={square:"n"};function r(e,t){var i=t.firstChild();if(i&&"element"==i.type&&"span"==i.tagName&&/Wingdings|Symbol/.test(i.getStyle("font-family"))){for(var r in o)if(o[r]==i.data)return r;return"disc"}for(var r in n)if(n[r].test(e))return r}utils.each(t.getNodesByTagName("p"),function(t){if("MsoListParagraph"==t.getAttr("class")){t.setStyle("margin",""),t.setStyle("margin-left",""),t.setAttr("class","");var o,a=t,s=t;if("li"!=t.parentNode.tagName&&(o=r(t.innerText(),t))){var l=UE.uNode.createElement(e.options.insertorderedlist.hasOwnProperty(o)?"ol":"ul");for(i[o]?l.setAttr("class","custom_"+o):l.setStyle("list-style-type",o);t&&"li"!=t.parentNode.tagName&&r(t.innerText(),t);)(a=t.nextSibling())||t.parentNode.insertBefore(l,t),c(l,t,o),t=a;!l.parentNode&&t&&t.parentNode&&t.parentNode.insertBefore(l,t)}var d=s.firstChild();d&&"element"==d.type&&"span"==d.tagName&&/^\s*(&nbsp;)+\s*$/.test(d.innerText())&&d.parentNode.removeChild(d)}function c(e,t,i){if("ol"==e.tagName)if(browser.ie){var o=t.firstChild();"element"==o.type&&"span"==o.tagName&&n[i].test(o.innerText())&&t.removeChild(o)}else t.innerHTML(t.innerHTML().replace(n[i],""));else t.removeChild(t.firstChild());var r=UE.uNode.createElement("li");r.appendChild(t),e.appendChild(r)}})}}),e.addListener("contentchange",function(){l(e.document)}),e.addListener("keydown",function(t,i){function n(){i.preventDefault?i.preventDefault():i.returnValue=!1,e.fireEvent("contentchange"),e.undoManger&&e.undoManger.save()}function o(e,t){for(;e&&!domUtils.isBody(e);){if(t(e))return null;if(1==e.nodeType&&/[ou]l/i.test(e.tagName))return e;e=e.parentNode}return null}var r=i.keyCode||i.which;if(13==r&&!i.shiftKey){var a=e.selection.getRange(),s=domUtils.findParent(a.startContainer,function(e){return domUtils.isBlockElm(e)},!0),l=domUtils.findParentByTagName(a.startContainer,"li",!0);if(s&&"PRE"!=s.tagName&&!l){var d=s.innerHTML.replace(new RegExp(domUtils.fillChar,"g"),"");/^\s*1\s*\.[^\d]/.test(d)&&(s.innerHTML=d.replace(/^\s*1\s*\./,""),a.setStartAtLast(s).collapse(!0).select(),e.__hasEnterExecCommand=!0,e.execCommand("insertorderedlist"),e.__hasEnterExecCommand=!1)}var c=e.selection.getRange(),m=o(c.startContainer,function(e){return"TABLE"==e.tagName}),f=c.collapsed?m:o(c.endContainer,function(e){return"TABLE"==e.tagName});if(m&&f&&m===f){if(!c.collapsed){if(m=domUtils.findParentByTagName(c.startContainer,"li",!0),f=domUtils.findParentByTagName(c.endContainer,"li",!0),!m||!f||m!==f){var h=c.cloneRange(),p=h.collapse(!1).createBookmark();return c.deleteContents(),h.moveToBookmark(p),u(l=domUtils.findParentByTagName(h.startContainer,"li",!0)),h.select(),void n()}if(c.deleteContents(),(l=domUtils.findParentByTagName(c.startContainer,"li",!0))&&domUtils.isEmptyBlock(l))return C=l.previousSibling,next=l.nextSibling,v=e.document.createElement("p"),domUtils.fillNode(e.document,v),N=l.parentNode,C&&next?(c.setStart(next,0).collapse(!0).select(!0),domUtils.remove(l)):((C||next)&&C?l.parentNode.parentNode.insertBefore(v,N.nextSibling):N.parentNode.insertBefore(v,N),domUtils.remove(l),N.firstChild||domUtils.remove(N),c.setStart(v,0).setCursor()),void n()}if(l=domUtils.findParentByTagName(c.startContainer,"li",!0)){if(domUtils.isEmptyBlock(l)){if(p=c.createBookmark(),l!==(N=l.parentNode).lastChild?(domUtils.breakParent(l,N),u(l)):(N.parentNode.insertBefore(l,N.nextSibling),domUtils.isEmptyNode(N)&&domUtils.remove(N)),!dtd.$list[l.parentNode.tagName])if(domUtils.isBlockElm(l.firstChild))domUtils.remove(l,!0);else{for(v=e.document.createElement("p"),l.parentNode.insertBefore(v,l);l.firstChild;)v.appendChild(l.firstChild);domUtils.remove(l)}c.moveToBookmark(p).select()}else{var g=l.firstChild;if(!g||!domUtils.isBlockElm(g)){var v=e.document.createElement("p");for(!l.firstChild&&domUtils.fillNode(e.document,v);l.firstChild;)v.appendChild(l.firstChild);l.appendChild(v),g=v}var b=e.document.createElement("span");c.insertNode(b),domUtils.breakParent(b,l);var y=b.nextSibling;(g=y.firstChild)||(v=e.document.createElement("p"),domUtils.fillNode(e.document,v),y.appendChild(v),g=v),domUtils.isEmptyNode(g)&&(g.innerHTML="",domUtils.fillNode(e.document,g)),c.setStart(g,0).collapse(!0).shrinkBoundary().select(),domUtils.remove(b);var C=y.previousSibling;C&&domUtils.isEmptyBlock(C)&&(C.innerHTML="<p></p>",domUtils.fillNode(e.document,C.firstChild))}n()}}}if(8==r&&(c=e.selection.getRange()).collapsed&&domUtils.isStartInblock(c)&&(h=c.cloneRange().trimBoundary(),(l=domUtils.findParentByTagName(c.startContainer,"li",!0))&&domUtils.isStartInblock(h))){if((m=domUtils.findParentByTagName(c.startContainer,"p",!0))&&m!==l.firstChild){var N=domUtils.findParentByTagName(m,["ol","ul"]);return domUtils.breakParent(m,N),u(m),e.fireEvent("contentchange"),c.setStart(m,0).setCursor(!1,!0),e.fireEvent("saveScene"),void domUtils.preventDefault(i)}if(l&&(C=l.previousSibling)){if(46==r&&l.childNodes.length)return;if(dtd.$list[C.tagName]&&(C=C.lastChild),e.undoManger&&e.undoManger.save(),g=l.firstChild,domUtils.isBlockElm(g))if(domUtils.isEmptyNode(g))for(C.appendChild(g),c.setStart(g,0).setCursor(!1,!0);l.firstChild;)C.appendChild(l.firstChild);else b=e.document.createElement("span"),c.insertNode(b),domUtils.isEmptyBlock(C)&&(C.innerHTML=""),domUtils.moveChild(l,C),c.setStartBefore(b).collapse(!0).select(!0),domUtils.remove(b);else if(domUtils.isEmptyNode(l)){v=e.document.createElement("p");C.appendChild(v),c.setStart(v,0).setCursor()}else for(c.setEnd(C,C.childNodes.length).collapse().select(!0);l.firstChild;)C.appendChild(l.firstChild);return domUtils.remove(l),e.fireEvent("contentchange"),e.fireEvent("saveScene"),void domUtils.preventDefault(i)}if(l&&!l.previousSibling){N=l.parentNode,p=c.createBookmark();if(domUtils.isTagNode(N.parentNode,"ol ul"))N.parentNode.insertBefore(l,N),domUtils.isEmptyNode(N)&&domUtils.remove(N);else{for(;l.firstChild;)N.parentNode.insertBefore(l.firstChild,N);domUtils.remove(l),domUtils.isEmptyNode(N)&&domUtils.remove(N)}return c.moveToBookmark(p).setCursor(!1,!0),e.fireEvent("contentchange"),e.fireEvent("saveScene"),void domUtils.preventDefault(i)}}}),e.addListener("keyup",function(t,i){if(8==(i.keyCode||i.which)){var n,o=e.selection.getRange();(n=domUtils.findParentByTagName(o.startContainer,["ol","ul"],!0))&&d(n,n.tagName.toLowerCase(),s(n)||domUtils.getComputedStyle(n,"list-style-type"),!0)}}),e.addListener("tabkeydown",function(){var t=e.selection.getRange();function i(t){if(-1!=e.options.maxListLevel){for(var i=t.parentNode,n=0;/[ou]l/i.test(i.tagName);)n++,i=i.parentNode;if(n>=e.options.maxListLevel)return!0}}var n=domUtils.findParentByTagName(t.startContainer,"li",!0);if(n){var r;if(!t.collapsed){e.fireEvent("saveScene"),r=t.createBookmark();for(var a,l,u=0,m=domUtils.findParents(n);l=m[u++];)if(domUtils.isTagNode(l,"ol ul")){a=l;break}var f=n;if(r.end)for(;f&&!(domUtils.getPosition(f,r.end)&domUtils.POSITION_FOLLOWING);)if(i(f))f=domUtils.getNextDomNode(f,!1,null,function(e){return e!==a});else{v=f.parentNode,b=e.document.createElement(v.tagName);var h=(p=utils.indexOf(o[b.tagName],s(v)||domUtils.getComputedStyle(v,"list-style-type")))+1==o[b.tagName].length?0:p+1;for(c(b,g=o[b.tagName][h]),v.insertBefore(b,f);f&&!(domUtils.getPosition(f,r.end)&domUtils.POSITION_FOLLOWING);){if(n=f.nextSibling,b.appendChild(f),!n||domUtils.isTagNode(n,"ol ul")){if(n)for(;(n=n.firstChild)&&"LI"!=n.tagName;);else n=domUtils.getNextDomNode(f,!1,null,function(e){return e!==a});break}f=n}d(b,b.tagName.toLowerCase(),g),f=n}return e.fireEvent("contentchange"),t.moveToBookmark(r).select(),!0}if(i(n))return!0;var p,g,v=n.parentNode,b=e.document.createElement(v.tagName);if(p=(p=utils.indexOf(o[b.tagName],s(v)||domUtils.getComputedStyle(v,"list-style-type")))+1==o[b.tagName].length?0:p+1,c(b,g=o[b.tagName][p]),domUtils.isStartInblock(t))return e.fireEvent("saveScene"),r=t.createBookmark(),v.insertBefore(b,n),b.appendChild(n),d(b,b.tagName.toLowerCase(),g),e.fireEvent("contentchange"),t.moveToBookmark(r).select(!0),!0}}),e.commands.insertorderedlist=e.commands.insertunorderedlist={execCommand:function(e,i){i||(i="insertorderedlist"==e.toLowerCase()?"decimal":"disc");var n=this,o=this.selection.getRange(),r=function(e){return 1==e.nodeType?"br"!=e.tagName.toLowerCase():!domUtils.isWhitespace(e)},a="insertorderedlist"==e.toLowerCase()?"ol":"ul",l=n.document.createDocumentFragment();o.adjustmentBoundary().shrinkBoundary();var u,f,h,p,g=o.createBookmark(!0),v=m(n.document.getElementById(g.start)),b=0,y=m(n.document.getElementById(g.end)),C=0;if(v||y){if(v&&(u=v.parentNode),g.end||(y=v),y&&(f=y.parentNode),u===f){for(;v!==y;){if(p=v,v=v.nextSibling,!domUtils.isBlockElm(p.firstChild)){for(var N=n.document.createElement("p");p.firstChild;)N.appendChild(p.firstChild);p.appendChild(N)}l.appendChild(p)}if(p=n.document.createElement("span"),u.insertBefore(p,y),!domUtils.isBlockElm(y.firstChild)){for(N=n.document.createElement("p");y.firstChild;)N.appendChild(y.firstChild);y.appendChild(N)}l.appendChild(y),domUtils.breakParent(p,u),domUtils.isEmptyNode(p.previousSibling)&&domUtils.remove(p.previousSibling),domUtils.isEmptyNode(p.nextSibling)&&domUtils.remove(p.nextSibling);var x=s(u)||domUtils.getComputedStyle(u,"list-style-type")||("insertorderedlist"==e.toLowerCase()?"decimal":"disc");if(u.tagName.toLowerCase()==a&&x==i){for(var w=0,U=n.document.createDocumentFragment();D=l.firstChild;)if(domUtils.isTagNode(D,"ol ul"))U.appendChild(D);else for(;D.firstChild;)U.appendChild(D.firstChild),domUtils.remove(D);p.parentNode.insertBefore(U,p)}else c(h=n.document.createElement(a),i),h.appendChild(l),p.parentNode.insertBefore(h,p);return domUtils.remove(p),h&&d(h,a,i),void o.moveToBookmark(g).select()}if(v){for(;v;){if(p=v.nextSibling,domUtils.isTagNode(v,"ol ul"))l.appendChild(v);else{for(var E=n.document.createDocumentFragment(),T=0;v.firstChild;)domUtils.isBlockElm(v.firstChild)&&(T=1),E.appendChild(v.firstChild);if(T)l.appendChild(E);else{var S=n.document.createElement("p");S.appendChild(E),l.appendChild(S)}domUtils.remove(v)}v=p}u.parentNode.insertBefore(l,u.nextSibling),domUtils.isEmptyNode(u)?(o.setStartBefore(u),domUtils.remove(u)):o.setStartAfter(u),b=1}if(y&&domUtils.inDoc(f,n.document)){for(v=f.firstChild;v&&v!==y;){if(p=v.nextSibling,domUtils.isTagNode(v,"ol ul"))l.appendChild(v);else{for(E=n.document.createDocumentFragment(),T=0;v.firstChild;)domUtils.isBlockElm(v.firstChild)&&(T=1),E.appendChild(v.firstChild);T?l.appendChild(E):((S=n.document.createElement("p")).appendChild(E),l.appendChild(S)),domUtils.remove(v)}v=p}var k=domUtils.createElement(n.document,"div",{tmpDiv:1});domUtils.moveChild(y,k),l.appendChild(k),domUtils.remove(y),f.parentNode.insertBefore(l,f),o.setEndBefore(f),domUtils.isEmptyNode(f)&&domUtils.remove(f),C=1}}b||o.setStartBefore(n.document.getElementById(g.start)),g.end&&!C&&o.setEndAfter(n.document.getElementById(g.end)),o.enlarge(!0,function(e){return t[e.tagName]}),l=n.document.createDocumentFragment();for(var _=o.createBookmark(),B=domUtils.getNextDomNode(_.start,!1,r),I=o.cloneRange(),A=domUtils.isBlockElm;B&&B!==_.end&&domUtils.getPosition(B,_.end)&domUtils.POSITION_PRECEDING;)if(3==B.nodeType||dtd.li[B.tagName]){if(1==B.nodeType&&dtd.$list[B.tagName]){for(;B.firstChild;)l.appendChild(B.firstChild);L=domUtils.getNextDomNode(B,!1,r),domUtils.remove(B),B=L;continue}for(L=B,I.setStartBefore(B);B&&B!==_.end&&(!A(B)||domUtils.isBookmarkNode(B));)L=B,B=domUtils.getNextDomNode(B,!1,null,function(e){return!t[e.tagName]});B&&A(B)&&(p=domUtils.getNextDomNode(L,!1,r))&&domUtils.isBookmarkNode(p)&&(B=domUtils.getNextDomNode(p,!1,r),L=p),I.setEndAfter(L),B=domUtils.getNextDomNode(L,!1,r);var R=o.document.createElement("li");if(R.appendChild(I.extractContents()),domUtils.isEmptyNode(R)){for(var L=o.document.createElement("p");R.firstChild;)L.appendChild(R.firstChild);R.appendChild(L)}l.appendChild(R)}else B=domUtils.getNextDomNode(B,!0,r);o.moveToBookmark(_).collapse(!0),c(h=n.document.createElement(a),i),h.appendChild(l),o.insertNode(h),d(h,a,i);w=0;for(var D,O=domUtils.getElementsByTagName(h,"div");D=O[w++];)D.getAttribute("tmpDiv")&&domUtils.remove(D,!0);o.moveToBookmark(g).select()},queryCommandState:function(e){for(var t,i="insertorderedlist"==e.toLowerCase()?"ol":"ul",n=this.selection.getStartElementPath(),o=0;t=n[o++];){if("TABLE"==t.nodeName)return 0;if(i==t.nodeName.toLowerCase())return 1}return 0},queryCommandValue:function(e){for(var t,i,n="insertorderedlist"==e.toLowerCase()?"ol":"ul",o=this.selection.getStartElementPath(),r=0;i=o[r++];){if("TABLE"==i.nodeName){t=null;break}if(n==i.nodeName.toLowerCase()){t=i;break}}return t?s(t)||domUtils.getComputedStyle(t,"list-style-type"):null}}},sourceEditors={textarea:function(e,t){var i=t.ownerDocument.createElement("textarea");return i.style.cssText="position:absolute;resize:none;width:100%;height:100%;border:0;padding:0;margin:0;overflow-y:auto;",browser.ie&&browser.version<8&&(i.style.width=t.offsetWidth+"px",i.style.height=t.offsetHeight+"px",t.onresize=function(){i.style.width=t.offsetWidth+"px",i.style.height=t.offsetHeight+"px"}),t.appendChild(i),{setContent:function(e){i.value=e},getContent:function(){return i.value},select:function(){var e;browser.ie?((e=i.createTextRange()).collapse(!0),e.select()):(i.setSelectionRange(0,0),i.focus())},dispose:function(){t.removeChild(i),t.onresize=null,i=null,t=null}}},codemirror:function(e,t){var i=window.CodeMirror(t,{mode:"text/html",tabMode:"indent",lineNumbers:!0,lineWrapping:!0}),n=i.getWrapperElement();return n.style.cssText='position:absolute;left:0;top:0;width:100%;height:100%;font-family:consolas,"Courier new",monospace;font-size:13px;',i.getScrollerElement().style.cssText="position:absolute;left:0;top:0;width:100%;height:100%;",i.refresh(),{getCodeMirror:function(){return i},setContent:function(e){i.setValue(e)},getContent:function(){return i.getValue()},select:function(){i.focus()},dispose:function(){t.removeChild(n),n=null,i=null}}}},UE.plugins.source=function(){var e,t,i,n,o,r=this,a=this.options,s=!1;a.sourceEditor=browser.ie?"textarea":a.sourceEditor||"codemirror",r.setOpt({sourceEditorFirst:!1}),r.commands.source={execCommand:function(){if(s=!s){o=r.selection.getRange().createAddress(!1,!0),r.undoManger&&r.undoManger.save(!0),browser.gecko&&(r.body.contentEditable=!1),i=r.iframe.style.cssText,r.iframe.style.cssText+="position:absolute;left:-32768px;top:-32768px;",r.fireEvent("beforegetcontent");var l=UE.htmlparser(r.body.innerHTML);r.filterOutputRule(l),l.traversal(function(e){if("element"==e.type)switch(e.tagName){case"td":case"th":case"caption":e.children&&1==e.children.length&&"br"==e.firstChild().tagName&&e.removeChild(e.firstChild());break;case"pre":e.innerText(e.innerText().replace(/&nbsp;/g," "))}}),r.fireEvent("aftergetcontent");var d=l.toHtml(!0);f=r.iframe.parentNode,(e=sourceEditors["codemirror"==a.sourceEditor&&window.CodeMirror?"codemirror":"textarea"](r,f)).setContent(d),t=r.setContent,r.setContent=function(t){var i=UE.htmlparser(t);r.filterInputRule(i),t=i.toHtml(),e.setContent(t)},n=r.getContent,r.getContent=function(){return e.getContent()||""}}else{r.iframe.style.cssText=i;var c=e.getContent()||"";c=c.replace(new RegExp("[\\r\\t\\n ]*</?(\\w+)\\s*(?:[^>]*)>","g"),function(e,t){return t&&!dtd.$inlineWithA[t.toLowerCase()]?e.replace(/(^[\n\r\t ]*)|([\n\r\t ]*$)/g,""):e.replace(/(^[\n\r\t]*)|([\n\r\t]*$)/g,"")}),r.setContent=t,r.setContent(c),e.dispose(),e=null,r.getContent=n;var u=r.body.firstChild;if(u||(r.body.innerHTML="",u=r.body.firstChild),r.undoManger&&r.undoManger.save(!0),browser.gecko){var m=document.createElement("input");m.style.cssText="position:absolute;left:0;top:-32768px",document.body.appendChild(m),r.body.contentEditable=!1,setTimeout(function(){domUtils.setViewportOffset(m,{left:-32768,top:0}),m.focus(),setTimeout(function(){r.body.contentEditable=!0,r.selection.getRange().moveToAddress(o).select(!0),domUtils.remove(m)})})}else try{r.selection.getRange().moveToAddress(o).select(!0)}catch(e){}}var f;this.fireEvent("sourcemodechanged",s)},queryCommandState:function(){return 0|s},notNeedUndo:1};var l=r.queryCommandState;r.queryCommandState=function(e){return e=e.toLowerCase(),s?e in{source:1,fullscreen:1}?1:-1:l.apply(this,arguments)},"codemirror"==a.sourceEditor&&r.addListener("ready",function(){utils.loadFile(document,{src:a.codeMirrorJsUrl||a.UEDITOR_HOME_URL+"third-party/codemirror/codemirror.js",tag:"script",type:"text/javascript",defer:"defer"},function(){a.sourceEditorFirst&&setTimeout(function(){r.execCommand("source")},0)}),utils.loadFile(document,{tag:"link",rel:"stylesheet",type:"text/css",href:a.codeMirrorCssUrl||a.UEDITOR_HOME_URL+"third-party/codemirror/codemirror.css"})})},UE.plugins.enterkey=function(){var e,t=this,i=t.options.enterTag;t.addListener("keyup",function(i,n){if(13==(n.keyCode||n.which)){var o,r=t.selection.getRange(),a=r.startContainer;if(browser.ie)t.fireEvent("saveScene",!0,!0);else{if(/h\d/i.test(e)){if(browser.gecko)domUtils.findParentByTagName(a,["h1","h2","h3","h4","h5","h6","blockquote","caption","table"],!0)||(t.document.execCommand("formatBlock",!1,"<p>"),o=1);else if(1==a.nodeType){var s,l=t.document.createTextNode("");if(r.insertNode(l),s=domUtils.findParentByTagName(l,"div",!0)){for(var d=t.document.createElement("p");s.firstChild;)d.appendChild(s.firstChild);s.parentNode.insertBefore(d,s),domUtils.remove(s),r.setStartBefore(l).setCursor(),o=1}domUtils.remove(l)}t.undoManger&&o&&t.undoManger.save()}browser.opera&&r.select()}}}),t.addListener("keydown",function(n,o){if(13==(o.keyCode||o.which)){if(t.fireEvent("beforeenterkeydown"))return void domUtils.preventDefault(o);t.fireEvent("saveScene",!0,!0),e="";var r=t.selection.getRange();if(!r.collapsed){var a=r.startContainer,s=r.endContainer,l=domUtils.findParentByTagName(a,"td",!0),d=domUtils.findParentByTagName(s,"td",!0);if(l&&d&&l!==d||!l&&d||l&&!d)return void(o.preventDefault?o.preventDefault():o.returnValue=!1)}if("p"==i)browser.ie||((a=domUtils.findParentByTagName(r.startContainer,["ol","ul","p","h1","h2","h3","h4","h5","h6","blockquote","caption"],!0))||browser.opera?(e=a.tagName,"p"==a.tagName.toLowerCase()&&browser.gecko&&domUtils.removeDirtyAttr(a)):(t.document.execCommand("formatBlock",!1,"<p>"),browser.gecko&&(r=t.selection.getRange(),(a=domUtils.findParentByTagName(r.startContainer,"p",!0))&&domUtils.removeDirtyAttr(a))));else if(o.preventDefault?o.preventDefault():o.returnValue=!1,r.collapsed)c=r.document.createElement("br"),r.insertNode(c),c.parentNode.lastChild===c?(c.parentNode.insertBefore(c.cloneNode(!0),c),r.setStartBefore(c)):r.setStartAfter(c),r.setCursor();else if(r.deleteContents(),1==(a=r.startContainer).nodeType&&(a=a.childNodes[r.startOffset])){for(;1==a.nodeType;){if(dtd.$empty[a.tagName])return r.setStartBefore(a).setCursor(),t.undoManger&&t.undoManger.save(),!1;if(!a.firstChild){var c=r.document.createElement("br");return a.appendChild(c),r.setStart(a,0).setCursor(),t.undoManger&&t.undoManger.save(),!1}a=a.firstChild}a===r.startContainer.childNodes[r.startOffset]?(c=r.document.createElement("br"),r.insertNode(c).setCursor()):r.setStart(a,0).setCursor()}else c=r.document.createElement("br"),r.insertNode(c).setStartAfter(c).setCursor()}})},UE.plugins.keystrokes=function(){var e=this,t=!0;e.addListener("keydown",function(i,n){var o=n.keyCode||n.which,r=e.selection.getRange();if(!r.collapsed&&!(n.ctrlKey||n.shiftKey||n.altKey||n.metaKey)&&(o>=65&&o<=90||o>=48&&o<=57||o>=96&&o<=111||{13:1,8:1,46:1}[o])){var a=r.startContainer;if(domUtils.isFillChar(a)&&r.setStartBefore(a),a=r.endContainer,domUtils.isFillChar(a)&&r.setEndAfter(a),r.txtToElmBoundary(),r.endContainer&&1==r.endContainer.nodeType&&(a=r.endContainer.childNodes[r.endOffset])&&domUtils.isBr(a)&&r.setEndAfter(a),0==r.startOffset&&(a=r.startContainer,domUtils.isBoundaryNode(a,"firstChild")&&(a=r.endContainer,r.endOffset==(3==a.nodeType?a.nodeValue.length:a.childNodes.length)&&domUtils.isBoundaryNode(a,"lastChild"))))return e.fireEvent("saveScene"),e.body.innerHTML="",r.setStart(e.body.firstChild,0).setCursor(!1,!0),void e._selectionChange()}if(o==keymap.Backspace){if(r=e.selection.getRange(),t=r.collapsed,e.fireEvent("delkeydown",n))return;var s,l;if(r.collapsed&&r.inFillChar()&&(s=r.startContainer,domUtils.isFillChar(s)?(r.setStartBefore(s).shrinkBoundary(!0).collapse(!0),domUtils.remove(s)):(s.nodeValue=s.nodeValue.replace(new RegExp("^"+domUtils.fillChar),""),r.startOffset--,r.collapse(!0).select(!0))),s=r.getClosedNode())return e.fireEvent("saveScene"),r.setStartBefore(s),domUtils.remove(s),r.setCursor(),e.fireEvent("saveScene"),void domUtils.preventDefault(n);if(!browser.ie&&(s=domUtils.findParentByTagName(r.startContainer,"table",!0),l=domUtils.findParentByTagName(r.endContainer,"table",!0),s&&!l||!s&&l||s!==l))return void n.preventDefault()}if(o==keymap.Tab){var d={ol:1,ul:1,table:1};if(e.fireEvent("tabkeydown",n))return void domUtils.preventDefault(n);var c=e.selection.getRange();e.fireEvent("saveScene");for(var u=0,m="",f=e.options.tabSize||4,h=e.options.tabNode||"&nbsp;";u<f;u++)m+=h;var p=e.document.createElement("span");if(p.innerHTML=m+domUtils.fillChar,c.collapsed)c.insertNode(p.cloneNode(!0).firstChild).setCursor(!0);else{var g=function(e){return domUtils.isBlockElm(e)&&!d[e.tagName.toLowerCase()]};if(s=domUtils.findParent(c.startContainer,g,!0),l=domUtils.findParent(c.endContainer,g,!0),s&&l&&s===l)c.deleteContents(),c.insertNode(p.cloneNode(!0).firstChild).setCursor(!0);else{var v=c.createBookmark();c.enlarge(!0);for(var b=c.createBookmark(),y=domUtils.getNextDomNode(b.start,!1,g);y&&!(domUtils.getPosition(y,b.end)&domUtils.POSITION_FOLLOWING);)y.insertBefore(p.cloneNode(!0).firstChild,y.firstChild),y=domUtils.getNextDomNode(y,!1,g);c.moveToBookmark(b).moveToBookmark(v).select()}}domUtils.preventDefault(n)}if(browser.gecko&&46==o&&(c=e.selection.getRange()).collapsed&&(s=c.startContainer,domUtils.isEmptyBlock(s))){for(var C=s.parentNode;1==domUtils.getChildCount(C)&&!domUtils.isBody(C);)s=C,C=C.parentNode;s===C.lastChild&&n.preventDefault()}else;}),e.addListener("keyup",function(e,i){var n;if((i.keyCode||i.which)==keymap.Backspace){if(this.fireEvent("delkeyup"))return;if((n=this.selection.getRange()).collapsed){if((a=domUtils.findParentByTagName(n.startContainer,["h1","h2","h3","h4","h5","h6"],!0))&&domUtils.isEmptyBlock(a)){var o=a.previousSibling;if(o&&"TABLE"!=o.nodeName)return domUtils.remove(a),void n.setStartAtLast(o).setCursor(!1,!0);var r=a.nextSibling;if(r&&"TABLE"!=r.nodeName)return domUtils.remove(a),void n.setStartAtFirst(r).setCursor(!1,!0)}if(domUtils.isBody(n.startContainer)){var a=domUtils.createElement(this.document,"p",{innerHTML:browser.ie?domUtils.fillChar:"<br/>"});n.insertNode(a).setStart(a,0).setCursor(!1,!0)}}if(!t&&(3==n.startContainer.nodeType||1==n.startContainer.nodeType&&domUtils.isEmptyBlock(n.startContainer)))if(browser.ie){var s=n.document.createElement("span");n.insertNode(s).setStartBefore(s).collapse(!0),n.select(),domUtils.remove(s)}else n.select()}})},UE.plugins.fiximgclick=function(){var e,t=!1;function n(){this.editor=null,this.resizer=null,this.cover=null,this.doc=document,this.prePos={x:0,y:0},this.startPos={x:0,y:0}}return e=[[0,0,-1,-1],[0,0,0,-1],[0,0,1,-1],[0,0,-1,0],[0,0,1,0],[0,0,-1,1],[0,0,0,1],[0,0,1,1]],n.prototype={init:function(e){var t=this;t.editor=e,t.startPos=this.prePos={x:0,y:0},t.dragId=-1;var n=[],o=t.cover=document.createElement("div"),r=t.resizer=document.createElement("div");for(o.id=t.editor.ui.id+"_imagescale_cover",o.style.cssText="position:absolute;display:none;z-index:"+t.editor.options.zIndex+";filter:alpha(opacity=0); opacity:0;background:#CCC;",domUtils.on(o,"mousedown click",function(){t.hide()}),i=0;i<8;i++)n.push('<span class="edui-editor-imagescale-hand'+i+'"></span>');r.id=t.editor.ui.id+"_imagescale",r.className="edui-editor-imagescale",r.innerHTML=n.join(""),r.style.cssText+=";display:none;border:1px solid #3b77ff;z-index:"+t.editor.options.zIndex+";",t.editor.ui.getDom().appendChild(o),t.editor.ui.getDom().appendChild(r),t.initStyle(),t.initEvents()},initStyle:function(){utils.cssRule("imagescale",".edui-editor-imagescale{display:none;position:absolute;border:1px solid #38B2CE;cursor:hand;-webkit-box-sizing: content-box;-moz-box-sizing: content-box;box-sizing: content-box;}.edui-editor-imagescale span{position:absolute;width:6px;height:6px;overflow:hidden;font-size:0px;display:block;background-color:#3C9DD0;}.edui-editor-imagescale .edui-editor-imagescale-hand0{cursor:nw-resize;top:0;margin-top:-4px;left:0;margin-left:-4px;}.edui-editor-imagescale .edui-editor-imagescale-hand1{cursor:n-resize;top:0;margin-top:-4px;left:50%;margin-left:-4px;}.edui-editor-imagescale .edui-editor-imagescale-hand2{cursor:ne-resize;top:0;margin-top:-4px;left:100%;margin-left:-3px;}.edui-editor-imagescale .edui-editor-imagescale-hand3{cursor:w-resize;top:50%;margin-top:-4px;left:0;margin-left:-4px;}.edui-editor-imagescale .edui-editor-imagescale-hand4{cursor:e-resize;top:50%;margin-top:-4px;left:100%;margin-left:-3px;}.edui-editor-imagescale .edui-editor-imagescale-hand5{cursor:sw-resize;top:100%;margin-top:-3px;left:0;margin-left:-4px;}.edui-editor-imagescale .edui-editor-imagescale-hand6{cursor:s-resize;top:100%;margin-top:-3px;left:50%;margin-left:-4px;}.edui-editor-imagescale .edui-editor-imagescale-hand7{cursor:se-resize;top:100%;margin-top:-3px;left:100%;margin-left:-3px;}")},initEvents:function(){this.startPos.x=this.startPos.y=0,this.isDraging=!1},_eventHandler:function(e){var i=this;switch(e.type){case"mousedown":var n;-1!=(n=e.target||e.srcElement).className.indexOf("edui-editor-imagescale-hand")&&-1==i.dragId&&(i.dragId=n.className.slice(-1),i.startPos.x=i.prePos.x=e.clientX,i.startPos.y=i.prePos.y=e.clientY,domUtils.on(i.doc,"mousemove",i.proxy(i._eventHandler,i)));break;case"mousemove":-1!=i.dragId&&(i.updateContainerStyle(i.dragId,{x:e.clientX-i.prePos.x,y:e.clientY-i.prePos.y}),i.prePos.x=e.clientX,i.prePos.y=e.clientY,t=!0,i.updateTargetElement());break;case"mouseup":-1!=i.dragId&&(i.updateContainerStyle(i.dragId,{x:e.clientX-i.prePos.x,y:e.clientY-i.prePos.y}),i.updateTargetElement(),i.target.parentNode&&i.attachTo(i.target),i.dragId=-1),domUtils.un(i.doc,"mousemove",i.proxy(i._eventHandler,i)),t&&(t=!1,i.editor.fireEvent("contentchange"))}},updateTargetElement:function(){var e=this;domUtils.setStyles(e.target,{width:e.resizer.style.width,height:e.resizer.style.height}),e.target.width=parseInt(e.resizer.style.width),e.target.height=parseInt(e.resizer.style.height),e.attachTo(e.target)},updateContainerStyle:function(t,i){var n,o=this,r=o.resizer;0!=e[t][0]&&(n=parseInt(r.style.left)+i.x,r.style.left=o._validScaledProp("left",n)+"px"),0!=e[t][1]&&(n=parseInt(r.style.top)+i.y,r.style.top=o._validScaledProp("top",n)+"px"),0!=e[t][2]&&(n=r.clientWidth+e[t][2]*i.x,r.style.width=o._validScaledProp("width",n)+"px"),0!=e[t][3]&&(n=r.clientHeight+e[t][3]*i.y,r.style.height=o._validScaledProp("height",n)+"px")},_validScaledProp:function(e,t){var i=this.resizer,n=document;switch(t=isNaN(t)?0:t,e){case"left":return t<0?0:t+i.clientWidth>n.clientWidth?n.clientWidth-i.clientWidth:t;case"top":return t<0?0:t+i.clientHeight>n.clientHeight?n.clientHeight-i.clientHeight:t;case"width":return t<=0?1:t+i.offsetLeft>n.clientWidth?n.clientWidth-i.offsetLeft:t;case"height":return t<=0?1:t+i.offsetTop>n.clientHeight?n.clientHeight-i.offsetTop:t}},hideCover:function(){this.cover.style.display="none"},showCover:function(){var e=this,t=domUtils.getXY(e.editor.ui.getDom()),i=domUtils.getXY(e.editor.iframe);domUtils.setStyles(e.cover,{width:e.editor.iframe.offsetWidth+"px",height:e.editor.iframe.offsetHeight+"px",top:i.y-t.y+"px",left:i.x-t.x+"px",position:"absolute",display:""})},show:function(e){var t=this;t.resizer.style.display="block",e&&t.attachTo(e),domUtils.on(this.resizer,"mousedown",t.proxy(t._eventHandler,t)),domUtils.on(t.doc,"mouseup",t.proxy(t._eventHandler,t)),t.showCover(),t.editor.fireEvent("afterscaleshow",t),t.editor.fireEvent("saveScene")},hide:function(){var e=this;e.hideCover(),e.resizer.style.display="none",domUtils.un(e.resizer,"mousedown",e.proxy(e._eventHandler,e)),domUtils.un(e.doc,"mouseup",e.proxy(e._eventHandler,e)),e.editor.fireEvent("afterscalehide",e)},proxy:function(e,t){return function(i){return e.apply(t||this,arguments)}},attachTo:function(e){var t=this.target=e,i=this.resizer,n=domUtils.getXY(t),o=domUtils.getXY(this.editor.iframe),r=domUtils.getXY(i.parentNode);domUtils.setStyles(i,{width:t.width+"px",height:t.height+"px",left:o.x+n.x-this.editor.document.body.scrollLeft-r.x-parseInt(i.style.borderLeftWidth)+"px",top:o.y+n.y-this.editor.document.documentElement.scrollTop-r.y-parseInt(i.style.borderTopWidth)+"px"})}},function(){var e,t=this;t.setOpt("imageScaleEnabled",!0),!browser.ie&&t.options.imageScaleEnabled&&t.addListener("click",function(i,o){var r=t.selection.getRange().getClosedNode();if(r&&"IMG"==r.tagName&&"false"!=t.body.contentEditable){if(-1!=r.className.indexOf("edui-faked-music")||r.getAttribute("anchorname")||domUtils.hasClass(r,"loadingclass")||domUtils.hasClass(r,"loaderrorclass"))return;if(!e){(e=new n).init(t),t.ui.getDom().appendChild(e.resizer);var a,s=function(i){e.hide(),e.target&&t.selection.getRange().selectNode(e.target).select()},l=function(e){var t=e.target||e.srcElement;!t||void 0!==t.className&&-1!=t.className.indexOf("edui-editor-imagescale")||s()};t.addListener("afterscaleshow",function(e){t.addListener("beforekeydown",s),t.addListener("beforemousedown",l),domUtils.on(document,"keydown",s),domUtils.on(document,"mousedown",l),t.selection.getNative().removeAllRanges()}),t.addListener("afterscalehide",function(i){t.removeListener("beforekeydown",s),t.removeListener("beforemousedown",l),domUtils.un(document,"keydown",s),domUtils.un(document,"mousedown",l);var n=e.target;n.parentNode&&t.selection.getRange().selectNode(n).select()}),domUtils.on(e.resizer,"mousedown",function(i){t.selection.getNative().removeAllRanges();var n=i.target||i.srcElement;n&&-1==n.className.indexOf("edui-editor-imagescale-hand")&&(a=setTimeout(function(){e.hide(),e.target&&t.selection.getRange().selectNode(n).select()},200))}),domUtils.on(e.resizer,"mouseup",function(e){var t=e.target||e.srcElement;t&&-1==t.className.indexOf("edui-editor-imagescale-hand")&&clearTimeout(a)})}e.show(r)}else e&&"none"!=e.resizer.style.display&&e.hide()}),browser.webkit&&t.addListener("click",function(e,i){"IMG"==i.target.tagName&&"false"!=t.body.contentEditable&&new dom.Range(t.document).selectNode(i.target).select()})}}(),UE.plugin.register("autolink",function(){return browser.ie?{}:{bindEvents:{reset:function(){0},keydown:function(e,t){var i=this,n=t.keyCode||t.which;if(32==n||13==n){for(var o,r,a=i.selection.getNative(),s=a.getRangeAt(0).cloneRange(),l=s.startContainer;1==l.nodeType&&s.startOffset>0&&(l=s.startContainer.childNodes[s.startOffset-1]);)s.setStart(l,1==l.nodeType?l.childNodes.length:l.nodeValue.length),s.collapse(!0),l=s.startContainer;do{if(0==s.startOffset){for(l=s.startContainer.previousSibling;l&&1==l.nodeType;)l=l.lastChild;if(!l||domUtils.isFillChar(l))break;o=l.nodeValue.length}else l=s.startContainer,o=s.startOffset;s.setStart(l,o-1),r=s.toString().charCodeAt(0)}while(160!=r&&32!=r);if(s.toString().replace(new RegExp(domUtils.fillChar,"g"),"").match(/(?:https?:\/\/|ssh:\/\/|ftp:\/\/|file:\/|www\.)/i)){for(;s.toString().length&&!/^(?:https?:\/\/|ssh:\/\/|ftp:\/\/|file:\/|www\.)/i.test(s.toString());)try{s.setStart(s.startContainer,s.startOffset+1)}catch(e){for(l=s.startContainer;!(next=l.nextSibling);){if(domUtils.isBody(l))return;l=l.parentNode}s.setStart(next,0)}if(domUtils.findParentByTagName(s.startContainer,"a",!0))return;var d,c=i.document.createElement("a"),u=i.document.createTextNode(" ");i.undoManger&&i.undoManger.save(),c.appendChild(s.extractContents()),c.href=c.innerHTML=c.innerHTML.replace(/<[^>]+>/g,""),d=c.getAttribute("href").replace(new RegExp(domUtils.fillChar,"g"),""),d=/^(?:https?:\/\/)/gi.test(d)?d:"http://"+d,c.setAttribute("_src",utils.html(d)),c.href=utils.html(d),s.insertNode(c),c.parentNode.insertBefore(u,c.nextSibling),s.setStart(u,0),s.collapse(!0),a.removeAllRanges(),a.addRange(s),i.undoManger&&i.undoManger.save()}}}}}},function(){var e={37:1,38:1,39:1,40:1,13:1,32:1};browser.ie&&this.addListener("keyup",function(t,i){var n=i.keyCode;if(e[n]){var o=this.selection.getRange(),r=o.startContainer;if(13==n){for(;r&&!domUtils.isBody(r)&&!domUtils.isBlockElm(r);)r=r.parentNode;var a;if(r&&!domUtils.isBody(r)&&"P"==r.nodeName)if((a=r.previousSibling)&&1==a.nodeType)(a=function(e){if(3==e.nodeType)return null;if("A"==e.nodeName)return e;for(var t=e.lastChild;t;){if("A"==t.nodeName)return t;if(3==t.nodeType){if(domUtils.isWhitespace(t)){t=t.previousSibling;continue}return null}t=t.lastChild}}(a))&&!a.getAttribute("_href")&&domUtils.remove(a,!0)}else if(32==n)3==r.nodeType&&/^\s$/.test(r.nodeValue)&&(r=r.previousSibling)&&"A"==r.nodeName&&!r.getAttribute("_href")&&domUtils.remove(r,!0);else if((r=domUtils.findParentByTagName(r,"a",!0))&&!r.getAttribute("_href")){var s=o.createBookmark();domUtils.remove(r,!0),o.moveToBookmark(s).select(!0)}}})}),UE.plugins.autoheight=function(){var e=this;if(e.autoHeightEnabled=!1!==e.options.autoHeightEnabled,e.autoHeightEnabled){var t,i,n,o,r=0,a=e.options;e.addListener("fullscreenchanged",function(e,t){o=t}),e.addListener("destroy",function(){e.removeListener("contentchange afterinserthtml keyup mouseup",s)}),e.enableAutoHeight=function(){var e=this;if(e.autoHeightEnabled){var i=e.document;e.autoHeightEnabled=!0,t=i.body.style.overflowY,i.body.style.overflowY="hidden",e.addListener("contentchange afterinserthtml keyup mouseup",s),setTimeout(function(){s.call(e)},browser.gecko?100:0),e.fireEvent("autoheightchanged",e.autoHeightEnabled)}},e.disableAutoHeight=function(){e.body.style.overflowY=t||"",e.removeListener("contentchange",s),e.removeListener("keyup",s),e.removeListener("mouseup",s),e.autoHeightEnabled=!1,e.fireEvent("autoheightchanged",e.autoHeightEnabled)},e.on("setHeight",function(){e.disableAutoHeight()}),e.addListener("ready",function(){var t,i;e.enableAutoHeight(),domUtils.on(browser.ie?e.body:e.document,browser.webkit?"dragover":"drop",function(){clearTimeout(t),t=setTimeout(function(){s.call(e)},100)}),window.onscroll=function(){null===i?i=this.scrollY:0==this.scrollY&&0!=i&&(e.window.scrollTo(0,0),i=null)}})}function s(){var e=this;clearTimeout(n),o||(!e.queryCommandState||e.queryCommandState&&1!=e.queryCommandState("source"))&&(n=setTimeout(function(){for(var t=e.body.lastChild;t&&1!=t.nodeType;)t=t.previousSibling;t&&1==t.nodeType&&(t.style.clear="both",(i=Math.max(domUtils.getXY(t).y+t.offsetHeight+25,Math.max(a.minFrameHeight,a.initialFrameHeight)))!=r&&(i!==parseInt(e.iframe.parentNode.style.height)&&(e.iframe.parentNode.style.height=i+"px"),e.body.style.height=i+"px",r=i),domUtils.removeStyle(t,"clear"))},50))}},UE.plugins.autofloat=function(){var e=this,t=e.getLang();e.setOpt({topOffset:0});var i=!1!==e.options.autoFloatEnabled,n=e.options.topOffset;if(i){var o,r,a,s,l=UE.ui.uiUtils,d=browser.ie&&browser.version<=6,c=browser.quirks,u=document.createElement("div"),m=!0,f=utils.defer(function(){p()},browser.ie?200:100,!0);e.addListener("destroy",function(){domUtils.un(window,["scroll","resize"],p),e.removeListener("keydown",f)}),e.addListener("ready",function(){if(UE.ui||(alert(t.autofloatMsg),0)){if(!e.ui)return;s=l.getClientRect,r=e.ui.getDom("toolbarbox"),a=s(r).top,o=r.style.cssText,u.style.height=r.offsetHeight+"px",d&&((i=document.body.style).backgroundImage='url("about:blank")',i.backgroundAttachment="fixed"),domUtils.on(window,["scroll","resize"],p),e.addListener("keydown",f),e.addListener("beforefullscreenchange",function(e,t){t&&h()}),e.addListener("fullscreenchanged",function(e,t){t||p()}),e.addListener("sourcemodechanged",function(e,t){setTimeout(function(){p()},0)}),e.addListener("clearDoc",function(){setTimeout(function(){p()},0)})}var i})}function h(){m=!0,u.parentNode&&u.parentNode.removeChild(u),r.style.cssText=o}function p(){var t,i,o,l=s(e.container),f=e.options.toolbarTopOffset||0;l.top<0&&l.bottom-r.offsetHeight>f?(t=domUtils.getXY(r),i=domUtils.getComputedStyle(r,"position"),o=domUtils.getComputedStyle(r,"left"),r.style.width=r.offsetWidth+"px",r.style.zIndex=1*e.options.zIndex+1,r.parentNode.insertBefore(u,r),d||c&&browser.ie?("absolute"!=r.style.position&&(r.style.position="absolute"),r.style.top=(document.body.scrollTop||document.documentElement.scrollTop)-a+n+"px"):(browser.ie7Compat&&m&&(m=!1,r.style.left=domUtils.getXY(r).x-document.documentElement.getBoundingClientRect().left+2+"px"),"fixed"!=r.style.position&&(r.style.position="fixed",r.style.top="40px",("absolute"==i||"relative"==i)&&parseFloat(o)&&(r.style.left=t.x+"px")))):h()}},UE.plugins.video=function(){var e=this;function t(t,i,n,o,r,a,s){var l;switch(t=utils.unhtmlForUrl(t),r=utils.unhtml(r),a=utils.unhtml(a),s){case"image":l="<img "+(o?'id="'+o+'"':"")+' width="'+i+'" height="'+n+'" _url="'+t+'" class="'+a.replace(/\bvideo-js\b/,"")+'" src="'+e.options.UEDITOR_HOME_URL+'themes/default/images/spacer.gif" style="background:url('+e.options.UEDITOR_HOME_URL+"themes/default/images/videologo.gif) no-repeat center center; border:1px solid gray;"+(r?"float:"+r+";":"")+'" />';break;case"embed":case"video":var d=t.substr(t.lastIndexOf(".")+1);"ogv"==d&&(d="ogg"),l="<video"+(o?' id="'+o+'"':"")+' class="'+a+' video-js" '+(r?' style="float:'+r+'"':"")+' controls preload="none" width="'+i+'" height="'+n+'" src="'+t+'"><source src="'+t+'" type="video/'+d+'" /></video>'}return l}function i(e,i){utils.each(e.getNodesByTagName(i?"img":"embed video"),function(e){var n=e.getAttr("class");if(n&&-1!=n.indexOf("edui-faked-video")){var o=t(i?e.getAttr("_url"):e.getAttr("src"),e.getAttr("width"),e.getAttr("height"),null,e.getStyle("float")||"",n,i?"video":"image");e.parentNode.replaceChild(UE.uNode.createElement(o),e)}if(n&&-1!=n.indexOf("edui-upload-video")){o=t(i?e.getAttr("_url"):e.getAttr("src"),e.getAttr("width"),e.getAttr("height"),null,e.getStyle("float")||"",n,i?"video":"image");e.parentNode.replaceChild(UE.uNode.createElement(o),e)}})}e.addOutputRule(function(e){i(e,!0)}),e.addInputRule(function(e){i(e)}),e.commands.insertvideo={execCommand:function(i,n,o){for(var r,a,s=[],l=0,d=(n=utils.isArray(n)?n:[n]).length;l<d;l++)a=n[l],r="upload"==o?"edui-upload-video video-js vjs-default-skin":"edui-faked-video",s.push(t(a.url,a.width||420,a.height||280,"tmpVedio"+l,null,r,"video"));e.execCommand("inserthtml",s.join(""),!0),e.execCommand("source"),e.execCommand("source");this.selection.getRange()},queryCommandState:function(){var t=e.selection.getRange().getClosedNode();return t&&("edui-faked-video"==t.className||-1!=t.className.indexOf("edui-upload-video"))?1:0}}},function(){var e=UE.UETable=function(e){this.table=e,this.indexTable=[],this.selectedTds=[],this.cellsRange={},this.update(e)};e.removeSelectedClass=function(e){utils.each(e,function(e){domUtils.removeClasses(e,"selectTdClass")})},e.addSelectedClass=function(e){utils.each(e,function(e){domUtils.addClass(e,"selectTdClass")})},e.isEmptyBlock=function(e){var t=new RegExp(domUtils.fillChar,"g");if(e[browser.ie?"innerText":"textContent"].replace(/^\s*$/,"").replace(t,"").length>0)return 0;for(var i in dtd.$isNotEmpty)if(dtd.$isNotEmpty.hasOwnProperty(i)&&e.getElementsByTagName(i).length)return 0;return 1},e.getWidth=function(e){return e?parseInt(domUtils.getComputedStyle(e,"width"),10):0},e.getTableCellAlignState=function(e){!utils.isArray(e)&&(e=[e]);var t={},i=["align","valign"],n=null,o=!0;return utils.each(e,function(e){return utils.each(i,function(i){if(n=e.getAttribute(i),!t[i]&&n)t[i]=n;else if(!t[i]||n!==t[i])return o=!1,!1}),o}),o?t:null},e.getTableItemsByRange=function(e){var t=e.selection.getStart();t&&t.id&&0===t.id.indexOf("_baidu_bookmark_start_")&&t.nextSibling&&(t=t.nextSibling);var i=t&&domUtils.findParentByTagName(t,["td","th"],!0),n=i&&i.parentNode,o=t&&domUtils.findParentByTagName(t,"caption",!0);return{cell:i,tr:n,table:o?o.parentNode:n&&n.parentNode.parentNode,caption:o}},e.getUETableBySelected=function(t){var i=e.getTableItemsByRange(t).table;return i&&i.ueTable&&i.ueTable.selectedTds.length?i.ueTable:null},e.getDefaultValue=function(e,t){var i,n,o,r,a={thin:"0px",medium:"1px",thick:"2px"};if(t)return s=t.getElementsByTagName("td")[0],r=domUtils.getComputedStyle(t,"border-left-width"),i=parseInt(a[r]||r,10),r=domUtils.getComputedStyle(s,"padding-left"),n=parseInt(a[r]||r,10),r=domUtils.getComputedStyle(s,"border-left-width"),{tableBorder:i,tdPadding:n,tdBorder:o=parseInt(a[r]||r,10)};(t=e.document.createElement("table")).insertRow(0).insertCell(0).innerHTML="xxx",e.body.appendChild(t);var s=t.getElementsByTagName("td")[0];return r=domUtils.getComputedStyle(t,"border-left-width"),i=parseInt(a[r]||r,10),r=domUtils.getComputedStyle(s,"padding-left"),n=parseInt(a[r]||r,10),r=domUtils.getComputedStyle(s,"border-left-width"),o=parseInt(a[r]||r,10),domUtils.remove(t),{tableBorder:i,tdPadding:n,tdBorder:o}},e.getUETable=function(t){var i=t.tagName.toLowerCase();return(t="td"==i||"th"==i||"caption"==i?domUtils.findParentByTagName(t,"table",!0):t).ueTable||(t.ueTable=new e(t)),t.ueTable},e.cloneCell=function(e,t,i){if(!e||utils.isString(e))return this.table.ownerDocument.createElement(e||"td");var n=domUtils.hasClass(e,"selectTdClass");n&&domUtils.removeClasses(e,"selectTdClass");var o=e.cloneNode(!0);return t&&(o.rowSpan=o.colSpan=1),!i&&domUtils.removeAttributes(o,"width height"),!i&&domUtils.removeAttributes(o,"style"),o.style.borderLeftStyle="",o.style.borderTopStyle="",o.style.borderLeftColor=e.style.borderRightColor,o.style.borderLeftWidth=e.style.borderRightWidth,o.style.borderTopColor=e.style.borderBottomColor,o.style.borderTopWidth=e.style.borderBottomWidth,n&&domUtils.addClass(e,"selectTdClass"),o},e.prototype={getMaxRows:function(){for(var e,t=this.table.rows,i=1,n=0;e=t[n];n++){for(var o,r=1,a=0;o=e.cells[a++];)r=Math.max(o.rowSpan||1,r);i=Math.max(r+n,i)}return i},getMaxCols:function(){for(var e,t=this.table.rows,i=0,n={},o=0;e=t[o];o++){for(var r,a=0,s=0;r=e.cells[s++];)if(a+=r.colSpan||1,r.rowSpan&&r.rowSpan>1)for(var l=1;l<r.rowSpan;l++)n["row_"+(o+l)]?n["row_"+(o+l)]++:n["row_"+(o+l)]=r.colSpan||1;a+=n["row_"+o]||0,i=Math.max(a,i)}return i},getCellColIndex:function(e){},getHSideCell:function(e,t){try{var i,n,o=this.getCellInfo(e),r=this.selectedTds.length,a=this.cellsRange;return!t&&(r?!a.beginColIndex:!o.colIndex)||t&&(r?a.endColIndex==this.colsNum-1:o.colIndex==this.colsNum-1)?null:(i=r?a.beginRowIndex:o.rowIndex,n=t?r?a.endColIndex+1:o.colIndex+1:r?a.beginColIndex-1:o.colIndex<1?0:o.colIndex-1,this.getCell(this.indexTable[i][n].rowIndex,this.indexTable[i][n].cellIndex))}catch(e){}},getTabNextCell:function(e,t){var i,n=this.getCellInfo(e),o=t||n.rowIndex,r=n.colIndex+1+(n.colSpan-1);try{i=this.getCell(this.indexTable[o][r].rowIndex,this.indexTable[o][r].cellIndex)}catch(e){try{o=1*o+1,r=0,i=this.getCell(this.indexTable[o][r].rowIndex,this.indexTable[o][r].cellIndex)}catch(e){}}return i},getVSideCell:function(e,t,i){try{var n,o,r=this.getCellInfo(e),a=this.selectedTds.length&&!i,s=this.cellsRange;return!t&&0==r.rowIndex||t&&(a?s.endRowIndex==this.rowsNum-1:r.rowIndex+r.rowSpan>this.rowsNum-1)?null:(n=t?a?s.endRowIndex+1:r.rowIndex+r.rowSpan:a?s.beginRowIndex-1:r.rowIndex-1,o=a?s.beginColIndex:r.colIndex,this.getCell(this.indexTable[n][o].rowIndex,this.indexTable[n][o].cellIndex))}catch(e){}},getSameEndPosCells:function(e,t){try{for(var i="x"===t.toLowerCase(),n=domUtils.getXY(e)[i?"x":"y"]+e["offset"+(i?"Width":"Height")],o=this.table.rows,r=null,a=[],s=0;s<this.rowsNum;s++){r=o[s].cells;for(var l,d=0;l=r[d++];){var c=domUtils.getXY(l)[i?"x":"y"]+l["offset"+(i?"Width":"Height")];if(c>n&&i)break;if((e==l||n==c)&&(1==l[i?"colSpan":"rowSpan"]&&a.push(l),i))break}}return a}catch(e){}},setCellContent:function(e,t){e.innerHTML=t||(browser.ie?domUtils.fillChar:"<br />")},cloneCell:e.cloneCell,getSameStartPosXCells:function(e){try{for(var t,i=domUtils.getXY(e).x+e.offsetWidth,n=this.table.rows,o=[],r=0;r<this.rowsNum;r++){t=n[r].cells;for(var a,s=0;a=t[s++];){var l=domUtils.getXY(a).x;if(l>i)break;if(l==i&&1==a.colSpan){o.push(a);break}}}return o}catch(e){}},update:function(e){this.table=e||this.table,this.selectedTds=[],this.cellsRange={},this.indexTable=[];for(var t=this.table.rows,i=this.getMaxRows(),n=i-t.length,o=this.getMaxCols();n--;)this.table.insertRow(t.length);this.rowsNum=i,this.colsNum=o;for(var r=0,a=t.length;r<a;r++)this.indexTable[r]=new Array(o);for(var s,l=0;s=t[l];l++)for(var d,c=0,u=s.cells;d=u[c];c++){d.rowSpan>i&&(d.rowSpan=i);for(var m=c,f=d.rowSpan||1,h=d.colSpan||1;this.indexTable[l][m];)m++;for(var p=0;p<f;p++)for(var g=0;g<h;g++)this.indexTable[l+p][m+g]={rowIndex:l,cellIndex:c,colIndex:m,rowSpan:f,colSpan:h}}for(p=0;p<i;p++)for(g=0;g<o;g++)void 0===this.indexTable[p][g]&&(d=(d=(s=t[p]).cells[s.cells.length-1])?d.cloneNode(!0):this.table.ownerDocument.createElement("td"),this.setCellContent(d),1!==d.colSpan&&(d.colSpan=1),1!==d.rowSpan&&(d.rowSpan=1),s.appendChild(d),this.indexTable[p][g]={rowIndex:p,cellIndex:d.cellIndex,colIndex:g,rowSpan:1,colSpan:1});var v=domUtils.getElementsByTagName(this.table,"td"),b=[];if(utils.each(v,function(e){domUtils.hasClass(e,"selectTdClass")&&b.push(e)}),b.length){var y=b[0],C=b[b.length-1],N=this.getCellInfo(y),x=this.getCellInfo(C);this.selectedTds=b,this.cellsRange={beginRowIndex:N.rowIndex,beginColIndex:N.colIndex,endRowIndex:x.rowIndex+x.rowSpan-1,endColIndex:x.colIndex+x.colSpan-1}}if(!domUtils.hasClass(this.table.rows[0],"firstRow")){domUtils.addClass(this.table.rows[0],"firstRow");for(r=1;r<this.table.rows.length;r++)domUtils.removeClasses(this.table.rows[r],"firstRow")}},getCellInfo:function(e){if(e)for(var t=e.cellIndex,i=e.parentNode.rowIndex,n=this.indexTable[i],o=this.colsNum,r=t;r<o;r++){var a=n[r];if(a.rowIndex===i&&a.cellIndex===t)return a}},getCell:function(e,t){return e<this.rowsNum&&this.table.rows[e].cells[t]||null},deleteCell:function(e,t){t="number"==typeof t?t:e.parentNode.rowIndex,this.table.rows[t].deleteCell(e.cellIndex)},getCellsRange:function(e,t){try{var i=this,n=i.getCellInfo(e);if(e===t)return{beginRowIndex:n.rowIndex,beginColIndex:n.colIndex,endRowIndex:n.rowIndex+n.rowSpan-1,endColIndex:n.colIndex+n.colSpan-1};var o=i.getCellInfo(t);return function e(t,n,o,r){var a,s,l,d=t,c=n,u=o,m=r;if(t>0)for(s=n;s<r;s++)(l=(a=i.indexTable[t][s]).rowIndex)<t&&(d=Math.min(l,d));if(r<i.colsNum)for(l=t;l<o;l++)(s=(a=i.indexTable[l][r]).colIndex+a.colSpan-1)>r&&(m=Math.max(s,m));if(o<i.rowsNum)for(s=n;s<r;s++)(l=(a=i.indexTable[o][s]).rowIndex+a.rowSpan-1)>o&&(u=Math.max(l,u));if(n>0)for(l=t;l<o;l++)(s=(a=i.indexTable[l][n]).colIndex)<n&&(c=Math.min(a.colIndex,c));return d!=t||c!=n||u!=o||m!=r?e(d,c,u,m):{beginRowIndex:t,beginColIndex:n,endRowIndex:o,endColIndex:r}}(Math.min(n.rowIndex,o.rowIndex),Math.min(n.colIndex,o.colIndex),Math.max(n.rowIndex+n.rowSpan-1,o.rowIndex+o.rowSpan-1),Math.max(n.colIndex+n.colSpan-1,o.colIndex+o.colSpan-1))}catch(e){}},getCells:function(e){this.clearSelected();for(var t,i,n,o=e.beginRowIndex,r=e.beginColIndex,a=e.endRowIndex,s=e.endColIndex,l={},d=[],c=o;c<=a;c++)for(var u=r;u<=s;u++){var m=(i=(t=this.indexTable[c][u]).rowIndex)+"|"+(n=t.colIndex);if(!l[m]){if(l[m]=1,i<c||n<u||i+t.rowSpan-1>a||n+t.colSpan-1>s)return null;d.push(this.getCell(i,t.cellIndex))}}return d},clearSelected:function(){e.removeSelectedClass(this.selectedTds),this.selectedTds=[],this.cellsRange={}},setSelected:function(t){var i=this.getCells(t);e.addSelectedClass(i),this.selectedTds=i,this.cellsRange=t},isFullRow:function(){var e=this.cellsRange;return e.endColIndex-e.beginColIndex+1==this.colsNum},isFullCol:function(){var e=this.cellsRange,t=this.table.getElementsByTagName("th"),i=e.endRowIndex-e.beginRowIndex+1;return t.length?i==this.rowsNum||i==this.rowsNum-1:i==this.rowsNum},getNextCell:function(e,t,i){try{var n,o,r=this.getCellInfo(e),a=this.selectedTds.length&&!i,s=this.cellsRange;return!t&&0==r.rowIndex||t&&(a?s.endRowIndex==this.rowsNum-1:r.rowIndex+r.rowSpan>this.rowsNum-1)?null:(n=t?a?s.endRowIndex+1:r.rowIndex+r.rowSpan:a?s.beginRowIndex-1:r.rowIndex-1,o=a?s.beginColIndex:r.colIndex,this.getCell(this.indexTable[n][o].rowIndex,this.indexTable[n][o].cellIndex))}catch(e){}},getPreviewCell:function(e,t){try{var i,n,o=this.getCellInfo(e),r=this.selectedTds.length,a=this.cellsRange;return!t&&(r?!a.beginColIndex:!o.colIndex)||t&&(r?a.endColIndex==this.colsNum-1:o.rowIndex>this.colsNum-1)?null:(i=t?r?a.beginRowIndex:o.rowIndex<1?0:o.rowIndex-1:r?a.beginRowIndex:o.rowIndex,n=t?r?a.endColIndex+1:o.colIndex:r?a.beginColIndex-1:o.colIndex<1?0:o.colIndex-1,this.getCell(this.indexTable[i][n].rowIndex,this.indexTable[i][n].cellIndex))}catch(e){}},moveContent:function(t,i){if(!e.isEmptyBlock(i))if(e.isEmptyBlock(t))t.innerHTML=i.innerHTML;else{var n=t.lastChild;for(3!=n.nodeType&&dtd.$block[n.tagName]||t.appendChild(t.ownerDocument.createElement("br"));n=i.firstChild;)t.appendChild(n)}},mergeRight:function(e){var t=this.getCellInfo(e),i=t.colIndex+t.colSpan,n=this.indexTable[t.rowIndex][i],o=this.getCell(n.rowIndex,n.cellIndex);e.colSpan=t.colSpan+n.colSpan,e.removeAttribute("width"),this.moveContent(e,o),this.deleteCell(o,n.rowIndex),this.update()},mergeDown:function(e){var t=this.getCellInfo(e),i=t.rowIndex+t.rowSpan,n=this.indexTable[i][t.colIndex],o=this.getCell(n.rowIndex,n.cellIndex);e.rowSpan=t.rowSpan+n.rowSpan,e.removeAttribute("height"),this.moveContent(e,o),this.deleteCell(o,n.rowIndex),this.update()},mergeRange:function(){var e=this.cellsRange,t=this.getCell(e.beginRowIndex,this.indexTable[e.beginRowIndex][e.beginColIndex].cellIndex);if("TH"==t.tagName&&e.endRowIndex!==e.beginRowIndex){var i=this.indexTable,n=this.getCellInfo(t);t=this.getCell(1,i[1][n.colIndex].cellIndex),e=this.getCellsRange(t,this.getCell(i[this.rowsNum-1][n.colIndex].rowIndex,i[this.rowsNum-1][n.colIndex].cellIndex))}for(var o,r=this.getCells(e),a=0;o=r[a++];)o!==t&&(this.moveContent(t,o),this.deleteCell(o));if(t.rowSpan=e.endRowIndex-e.beginRowIndex+1,t.rowSpan>1&&t.removeAttribute("height"),t.colSpan=e.endColIndex-e.beginColIndex+1,t.colSpan>1&&t.removeAttribute("width"),t.rowSpan==this.rowsNum&&1!=t.colSpan&&(t.colSpan=1),t.colSpan==this.colsNum&&1!=t.rowSpan){var s=t.parentNode.rowIndex;if(this.table.deleteRow){a=s+1;for(var l=s+1,d=t.rowSpan;a<d;a++)this.table.deleteRow(l)}else for(a=0,d=t.rowSpan-1;a<d;a++){var c=this.table.rows[s+1];c.parentNode.removeChild(c)}t.rowSpan=1}this.update()},insertRow:function(e,t){var i,n=this.colsNum,o=this.table.insertRow(e),r="string"==typeof t&&"TH"==t.toUpperCase();function a(e,t,i){if(0==e){var n=(i.nextSibling||i.previousSibling).cells[e];"TH"==n.tagName&&((n=t.ownerDocument.createElement("th")).appendChild(t.firstChild),i.insertBefore(n,t),domUtils.remove(t))}else if("TH"==t.tagName){var o=t.ownerDocument.createElement("td");o.appendChild(t.firstChild),i.insertBefore(o,t),domUtils.remove(t)}}if(0==e||e==this.rowsNum)for(var s=0;s<n;s++)i=this.cloneCell(t,!0),this.setCellContent(i),i.getAttribute("vAlign")&&i.setAttribute("vAlign",i.getAttribute("vAlign")),o.appendChild(i),r||a(s,i,o);else{var l=this.indexTable[e];for(s=0;s<n;s++){var d=l[s];d.rowIndex<e?(i=this.getCell(d.rowIndex,d.cellIndex)).rowSpan=d.rowSpan+1:(i=this.cloneCell(t,!0),this.setCellContent(i),o.appendChild(i)),r||a(s,i,o)}}return this.update(),o},deleteRow:function(e){for(var t=this.table.rows[e],i=this.indexTable[e],n=this.colsNum,o=0,r=0;r<n;){var a=i[r],s=this.getCell(a.rowIndex,a.cellIndex);if(s.rowSpan>1&&a.rowIndex==e){var l=s.cloneNode(!0);l.rowSpan=s.rowSpan-1,l.innerHTML="",s.rowSpan=1;var d,c=e+1,u=this.table.rows[c],m=this.getPreviewMergedCellsNum(c,r)-o;m<r?(d=r-m-1,domUtils.insertAfter(u.cells[d],l)):u.cells.length&&u.insertBefore(l,u.cells[0]),o+=1}r+=s.colSpan||1}var f=[],h={};for(r=0;r<n;r++){var p=i[r].rowIndex,g=i[r].cellIndex,v=p+"_"+g;h[v]||(h[v]=1,s=this.getCell(p,g),f.push(s))}var b=[];utils.each(f,function(e){1==e.rowSpan?e.parentNode.removeChild(e):b.push(e)}),utils.each(b,function(e){e.rowSpan--}),t.parentNode.removeChild(t),this.update()},insertCol:function(e,t,i){var n,o,r,a=this.rowsNum,s=0,l=parseInt((this.table.offsetWidth-20*(this.colsNum+1)-(this.colsNum+1))/(this.colsNum+1),10),d="string"==typeof t&&"TH"==t.toUpperCase();function c(e,t,i){if(0==e){var n=t.nextSibling||t.previousSibling;"TH"==n.tagName&&((n=t.ownerDocument.createElement("th")).appendChild(t.firstChild),i.insertBefore(n,t),domUtils.remove(t))}else if("TH"==t.tagName){var o=t.ownerDocument.createElement("td");o.appendChild(t.firstChild),i.insertBefore(o,t),domUtils.remove(t)}}if(0==e||e==this.colsNum)for(;s<a;s++)r=(n=this.table.rows[s]).cells[0==e?e:n.cells.length],o=this.cloneCell(t,!0),this.setCellContent(o),o.setAttribute("vAlign",o.getAttribute("vAlign")),r&&o.setAttribute("width",r.getAttribute("width")),e?domUtils.insertAfter(n.cells[n.cells.length-1],o):n.insertBefore(o,n.cells[0]),d||c(s,o,n);else for(;s<a;s++){var u=this.indexTable[s][e];u.colIndex<e?(o=this.getCell(u.rowIndex,u.cellIndex)).colSpan=u.colSpan+1:(r=(n=this.table.rows[s]).cells[u.cellIndex],o=this.cloneCell(t,!0),this.setCellContent(o),o.setAttribute("vAlign",o.getAttribute("vAlign")),r&&o.setAttribute("width",r.getAttribute("width")),r?n.insertBefore(o,r):n.appendChild(o)),d||c(s,o,n)}this.update(),this.updateWidth(l,i||{tdPadding:10,tdBorder:1})},updateWidth:function(t,i){var n=this.table,o=e.getWidth(n)-2*i.tdPadding-i.tdBorder+t;if(o<n.ownerDocument.body.offsetWidth)n.setAttribute("width",o);else{var r=domUtils.getElementsByTagName(this.table,"td th");utils.each(r,function(e){e.setAttribute("width",t)})}},deleteCol:function(e){for(var t=this.indexTable,i=this.table.rows,n=this.table.getAttribute("width"),o=0,r=this.rowsNum,a={},s=0;s<r;){var l=t[s][e],d=l.rowIndex+"_"+l.colIndex;if(!a[d]){a[d]=1;var c=this.getCell(l.rowIndex,l.cellIndex);o||(o=c&&parseInt(c.offsetWidth/c.colSpan,10).toFixed(0)),c.colSpan>1?c.colSpan--:i[s].deleteCell(l.cellIndex),s+=l.rowSpan||1}}this.table.setAttribute("width",n-o),this.update()},splitToCells:function(e){var t=this,i=this.splitToRows(e);utils.each(i,function(e){t.splitToCols(e)})},splitToRows:function(e){var t=this.getCellInfo(e),i=t.rowIndex,n=t.colIndex,o=[];e.rowSpan=1,o.push(e);for(var r=i,a=i+t.rowSpan;r<a;r++)if(r!=i){var s=this.table.rows[r].insertCell(n-this.getPreviewMergedCellsNum(r,n));s.colSpan=t.colSpan,this.setCellContent(s),s.setAttribute("vAlign",e.getAttribute("vAlign")),s.setAttribute("align",e.getAttribute("align")),e.style.cssText&&(s.style.cssText=e.style.cssText),o.push(s)}return this.update(),o},getPreviewMergedCellsNum:function(e,t){for(var i=this.indexTable[e],n=0,o=0;o<t;){var r=i[o].colSpan;n+=r-(i[o].rowIndex==e?1:0),o+=r}return n},splitToCols:function(e){var t=(e.offsetWidth/e.colSpan-22).toFixed(0),i=this.getCellInfo(e),n=i.rowIndex,o=i.colIndex,r=[];e.colSpan=1,e.setAttribute("width",t),r.push(e);for(var a=o,s=o+i.colSpan;a<s;a++)if(a!=o){var l=this.table.rows[n],d=l.insertCell(this.indexTable[n][a].cellIndex+1);if(d.rowSpan=i.rowSpan,this.setCellContent(d),d.setAttribute("vAlign",e.getAttribute("vAlign")),d.setAttribute("align",e.getAttribute("align")),d.setAttribute("width",t),e.style.cssText&&(d.style.cssText=e.style.cssText),"TH"==e.tagName){var c=e.ownerDocument.createElement("th");c.appendChild(d.firstChild),c.setAttribute("vAlign",e.getAttribute("vAlign")),c.rowSpan=d.rowSpan,l.insertBefore(c,d),domUtils.remove(d)}r.push(d)}return this.update(),r},isLastCell:function(e,t,i){t=t||this.rowsNum,i=i||this.colsNum;var n=this.getCellInfo(e);return n.rowIndex+n.rowSpan==t&&n.colIndex+n.colSpan==i},getLastCell:function(e){e=e||this.table.getElementsByTagName("td");this.getCellInfo(e[0]);var t,i=this,n=e[0],o=n.parentNode,r=0,a=0;return utils.each(e,function(e){e.parentNode==o&&(a+=e.colSpan||1),r+=e.rowSpan*e.colSpan||1}),t=r/a,utils.each(e,function(e){if(i.isLastCell(e,t,a))return n=e,!1}),n},selectRow:function(e){var t=this.indexTable[e],i=this.getCell(t[0].rowIndex,t[0].cellIndex),n=this.getCell(t[this.colsNum-1].rowIndex,t[this.colsNum-1].cellIndex),o=this.getCellsRange(i,n);this.setSelected(o)},selectTable:function(){var e=this.table.getElementsByTagName("td"),t=this.getCellsRange(e[0],e[e.length-1]);this.setSelected(t)},setBackground:function(e,t){if("string"==typeof t)utils.each(e,function(e){e.style.backgroundColor=t});else if("object"==typeof t){t=utils.extend({repeat:!0,colorList:["#ddd","#fff"]},t);for(var i,n=this.getCellInfo(e[0]).rowIndex,o=0,r=t.colorList,a=0;i=e[a++];){var s=this.getCellInfo(i);i.style.backgroundColor=(l=r,d=n+o==s.rowIndex?o:++o,c=t.repeat,l[d]?l[d]:c?l[d%l.length]:"")}}var l,d,c},removeBackground:function(e){utils.each(e,function(e){e.style.backgroundColor=""})}}}(),function(){var e=UE.UETable,t=function(t){return e.getTableItemsByRange(t)},i=function(t){return e.getUETableBySelected(t)},n=function(t,i){return e.getDefaultValue(t,i)},o=function(t){return e.getUETable(t)};function r(e,t){var i=domUtils.getElementsByTagName(e,"td th");utils.each(i,function(e){e.removeAttribute("width")}),e.setAttribute("width",function(e,t,i){var n=e.body;return n.offsetWidth-(t?2*parseInt(domUtils.getComputedStyle(n,"margin-left"),10):0)-2*i.tableBorder-(e.options.offsetWidth||0)}(t,!0,n(t,e)));var o=[];setTimeout(function(){utils.each(i,function(e){1==e.colSpan&&o.push(e.offsetWidth)}),utils.each(i,function(e,t){1==e.colSpan&&e.setAttribute("width",o[t]+"")})},0)}function a(e){var i=t(e).cell;if(i){var n=o(i);return n.selectedTds.length?n.selectedTds:[i]}return[]}UE.commands.inserttable={queryCommandState:function(){return t(this).table?-1:0},execCommand:function(e,t){t||(t=utils.extend({},{numCols:this.options.defaultCols,numRows:this.options.defaultRows,tdvalign:this.options.tdvalign}));var i=this.selection.getRange().startContainer,o=domUtils.findParent(i,function(e){return domUtils.isBlockElm(e)},!0)||this.body,r=n(this),a=o.offsetWidth,s=Math.floor(a/t.numCols-2*r.tdPadding-r.tdBorder);!t.tdvalign&&(t.tdvalign=this.options.tdvalign),this.execCommand("inserthtml",function(e,t){for(var i=[],n=e.numRows,o=e.numCols,r=0;r<n;r++){i.push("<tr"+(0==r?' class="firstRow"':"")+">");for(var a=0;a<o;a++)i.push('<td width="'+t+'"  vAlign="'+e.tdvalign+'" >'+(browser.ie&&browser.version<11?domUtils.fillChar:"<br/>")+"</td>");i.push("</tr>")}return"<table><tbody>"+i.join("")+"</tbody></table>"}(t,s))}},UE.commands.insertparagraphbeforetable={queryCommandState:function(){return t(this).cell?0:-1},execCommand:function(){var e=t(this).table;if(e){var i=this.document.createElement("p");i.innerHTML=browser.ie?"&nbsp;":"<br />",e.parentNode.insertBefore(i,e),this.selection.getRange().setStart(i,0).setCursor()}}},UE.commands.deletetable={queryCommandState:function(){var e=this.selection.getRange();return domUtils.findParentByTagName(e.startContainer,"table",!0)?0:-1},execCommand:function(e,t){var i=this.selection.getRange();if(t=t||domUtils.findParentByTagName(i.startContainer,"table",!0)){var n=t.nextSibling;n||(n=domUtils.createElement(this.document,"p",{innerHTML:browser.ie?domUtils.fillChar:"<br/>"}),t.parentNode.insertBefore(n,t)),domUtils.remove(t),i=this.selection.getRange(),3==n.nodeType?i.setStartBefore(n):i.setStart(n,0),i.setCursor(!1,!0),this.fireEvent("tablehasdeleted")}}},UE.commands.cellalign={queryCommandState:function(){return a(this).length?0:-1},execCommand:function(e,t){var i=a(this);if(i.length)for(var n,o=0;n=i[o++];)n.setAttribute("align",t)}},UE.commands.cellvalign={queryCommandState:function(){return a(this).length?0:-1},execCommand:function(e,t){var i=a(this);if(i.length)for(var n,o=0;n=i[o++];)n.setAttribute("vAlign",t)}},UE.commands.insertcaption={queryCommandState:function(){var e=t(this).table;return e&&0==e.getElementsByTagName("caption").length?1:-1},execCommand:function(){var e=t(this).table;if(e){var i=this.document.createElement("caption");i.innerHTML=browser.ie?domUtils.fillChar:"<br/>",e.insertBefore(i,e.firstChild),this.selection.getRange().setStart(i,0).setCursor()}}},UE.commands.deletecaption={queryCommandState:function(){var e=this.selection.getRange(),t=domUtils.findParentByTagName(e.startContainer,"table");return t?0==t.getElementsByTagName("caption").length?-1:1:-1},execCommand:function(){var e=this.selection.getRange(),t=domUtils.findParentByTagName(e.startContainer,"table");t&&(domUtils.remove(t.getElementsByTagName("caption")[0]),this.selection.getRange().setStart(t.rows[0].cells[0],0).setCursor())}},UE.commands.inserttitle={queryCommandState:function(){var e=t(this).table;if(e){var i=e.rows[0];return"th"!=i.cells[i.cells.length-1].tagName.toLowerCase()?0:-1}return-1},execCommand:function(){var e=t(this).table;e&&o(e).insertRow(0,"th");var i=e.getElementsByTagName("th")[0];this.selection.getRange().setStart(i,0).setCursor(!1,!0)}},UE.commands.deletetitle={queryCommandState:function(){var e=t(this).table;if(e){var i=e.rows[0];return"th"==i.cells[i.cells.length-1].tagName.toLowerCase()?0:-1}return-1},execCommand:function(){var e=t(this).table;e&&domUtils.remove(e.rows[0]);var i=e.getElementsByTagName("td")[0];this.selection.getRange().setStart(i,0).setCursor(!1,!0)}},UE.commands.inserttitlecol={queryCommandState:function(){var e=t(this).table;return e?e.rows[e.rows.length-1].getElementsByTagName("th").length?-1:0:-1},execCommand:function(e){var i=t(this).table;i&&o(i).insertCol(0,"th"),r(i,this);var n=i.getElementsByTagName("th")[0];this.selection.getRange().setStart(n,0).setCursor(!1,!0)}},UE.commands.deletetitlecol={queryCommandState:function(){var e=t(this).table;return e&&e.rows[e.rows.length-1].getElementsByTagName("th").length?0:-1},execCommand:function(){var e=t(this).table;if(e)for(var i=0;i<e.rows.length;i++)domUtils.remove(e.rows[i].children[0]);r(e,this);var n=e.getElementsByTagName("td")[0];this.selection.getRange().setStart(n,0).setCursor(!1,!0)}},UE.commands.mergeright={queryCommandState:function(e){var i=t(this),n=i.table,r=i.cell;if(!n||!r)return-1;var a=o(n);if(a.selectedTds.length)return-1;var s=a.getCellInfo(r),l=s.colIndex+s.colSpan;if(l>=a.colsNum)return-1;var d=a.indexTable[s.rowIndex][l],c=n.rows[d.rowIndex].cells[d.cellIndex];return c&&r.tagName==c.tagName&&d.rowIndex==s.rowIndex&&d.rowSpan==s.rowSpan?0:-1},execCommand:function(e){var i=this.selection.getRange(),n=i.createBookmark(!0),r=t(this).cell;o(r).mergeRight(r),i.moveToBookmark(n).select()}},UE.commands.mergedown={queryCommandState:function(e){var i=t(this),n=i.table,r=i.cell;if(!n||!r)return-1;var a=o(n);if(a.selectedTds.length)return-1;var s=a.getCellInfo(r),l=s.rowIndex+s.rowSpan;if(l>=a.rowsNum)return-1;var d=a.indexTable[l][s.colIndex],c=n.rows[d.rowIndex].cells[d.cellIndex];return c&&r.tagName==c.tagName&&d.colIndex==s.colIndex&&d.colSpan==s.colSpan?0:-1},execCommand:function(){var e=this.selection.getRange(),i=e.createBookmark(!0),n=t(this).cell;o(n).mergeDown(n),e.moveToBookmark(i).select()}},UE.commands.mergecells={queryCommandState:function(){return i(this)?0:-1},execCommand:function(){var e=i(this);if(e&&e.selectedTds.length){var t=e.selectedTds[0];e.mergeRange();var n=this.selection.getRange();domUtils.isEmptyBlock(t)?n.setStart(t,0).collapse(!0):n.selectNodeContents(t),n.select()}}},UE.commands.insertrow={queryCommandState:function(){var e=t(this),i=e.cell;return i&&("TD"==i.tagName||"TH"==i.tagName&&e.tr!==e.table.rows[0])&&o(e.table).rowsNum<this.options.maxRowNum?0:-1},execCommand:function(){var e=this.selection.getRange(),i=e.createBookmark(!0),n=t(this),r=n.cell,a=n.table,s=o(a),l=s.getCellInfo(r);if(s.selectedTds.length)for(var d=s.cellsRange,c=0,u=d.endRowIndex-d.beginRowIndex+1;c<u;c++)s.insertRow(d.beginRowIndex,r);else s.insertRow(l.rowIndex,r);e.moveToBookmark(i).select(),"enabled"===a.getAttribute("interlaced")&&this.fireEvent("interlacetable",a)}},UE.commands.insertrownext={queryCommandState:function(){var e=t(this),i=e.cell;return i&&"TD"==i.tagName&&o(e.table).rowsNum<this.options.maxRowNum?0:-1},execCommand:function(){var e=this.selection.getRange(),i=e.createBookmark(!0),n=t(this),r=n.cell,a=n.table,s=o(a),l=s.getCellInfo(r);if(s.selectedTds.length)for(var d=s.cellsRange,c=0,u=d.endRowIndex-d.beginRowIndex+1;c<u;c++)s.insertRow(d.endRowIndex+1,r);else s.insertRow(l.rowIndex+l.rowSpan,r);e.moveToBookmark(i).select(),"enabled"===a.getAttribute("interlaced")&&this.fireEvent("interlacetable",a)}},UE.commands.deleterow={queryCommandState:function(){return t(this).cell?0:-1},execCommand:function(){var e=t(this).cell,i=o(e),n=i.cellsRange,r=i.getCellInfo(e),a=i.getVSideCell(e),s=i.getVSideCell(e,!0),l=this.selection.getRange();if(utils.isEmptyObject(n))i.deleteRow(r.rowIndex);else for(var d=n.beginRowIndex;d<n.endRowIndex+1;d++)i.deleteRow(n.beginRowIndex);var c=i.table;if(c.getElementsByTagName("td").length)if(1==r.rowSpan||r.rowSpan==n.endRowIndex-n.beginRowIndex+1)(s||a)&&l.selectNodeContents(s||a).setCursor(!1,!0);else{var u=i.getCell(r.rowIndex,i.indexTable[r.rowIndex][r.colIndex].cellIndex);u&&l.selectNodeContents(u).setCursor(!1,!0)}else{var m=c.nextSibling;domUtils.remove(c),m&&l.setStart(m,0).setCursor(!1,!0)}"enabled"===c.getAttribute("interlaced")&&this.fireEvent("interlacetable",c)}},UE.commands.insertcol={queryCommandState:function(e){var i=t(this),n=i.cell;return n&&("TD"==n.tagName||"TH"==n.tagName&&n!==i.tr.cells[0])&&o(i.table).colsNum<this.options.maxColNum?0:-1},execCommand:function(e){var i=this.selection.getRange(),n=i.createBookmark(!0);if(-1!=this.queryCommandState(e)){var r=t(this).cell,a=o(r),s=a.getCellInfo(r);if(a.selectedTds.length)for(var l=a.cellsRange,d=0,c=l.endColIndex-l.beginColIndex+1;d<c;d++)a.insertCol(l.beginColIndex,r);else a.insertCol(s.colIndex,r);i.moveToBookmark(n).select(!0)}}},UE.commands.insertcolnext={queryCommandState:function(){var e=t(this);return e.cell&&o(e.table).colsNum<this.options.maxColNum?0:-1},execCommand:function(){var e=this.selection.getRange(),i=e.createBookmark(!0),n=t(this).cell,r=o(n),a=r.getCellInfo(n);if(r.selectedTds.length)for(var s=r.cellsRange,l=0,d=s.endColIndex-s.beginColIndex+1;l<d;l++)r.insertCol(s.endColIndex+1,n);else r.insertCol(a.colIndex+a.colSpan,n);e.moveToBookmark(i).select()}},UE.commands.deletecol={queryCommandState:function(){return t(this).cell?0:-1},execCommand:function(){var e=t(this).cell,i=o(e),n=i.cellsRange,r=i.getCellInfo(e),a=i.getHSideCell(e),s=i.getHSideCell(e,!0);if(utils.isEmptyObject(n))i.deleteCol(r.colIndex);else for(var l=n.beginColIndex;l<n.endColIndex+1;l++)i.deleteCol(n.beginColIndex);var d=i.table,c=this.selection.getRange();if(d.getElementsByTagName("td").length)domUtils.inDoc(e,this.document)?c.setStart(e,0).setCursor(!1,!0):s&&domUtils.inDoc(s,this.document)?c.selectNodeContents(s).setCursor(!1,!0):a&&domUtils.inDoc(a,this.document)&&c.selectNodeContents(a).setCursor(!0,!0);else{var u=d.nextSibling;domUtils.remove(d),u&&c.setStart(u,0).setCursor(!1,!0)}}},UE.commands.splittocells={queryCommandState:function(){var e=t(this),i=e.cell;return i?o(e.table).selectedTds.length>0?-1:i&&(i.colSpan>1||i.rowSpan>1)?0:-1:-1},execCommand:function(){var e=this.selection.getRange(),i=e.createBookmark(!0),n=t(this).cell;o(n).splitToCells(n),e.moveToBookmark(i).select()}},UE.commands.splittorows={queryCommandState:function(){var e=t(this),i=e.cell;return i?o(e.table).selectedTds.length>0?-1:i&&i.rowSpan>1?0:-1:-1},execCommand:function(){var e=this.selection.getRange(),i=e.createBookmark(!0),n=t(this).cell;o(n).splitToRows(n),e.moveToBookmark(i).select()}},UE.commands.splittocols={queryCommandState:function(){var e=t(this),i=e.cell;return i?o(e.table).selectedTds.length>0?-1:i&&i.colSpan>1?0:-1:-1},execCommand:function(){var e=this.selection.getRange(),i=e.createBookmark(!0),n=t(this).cell;o(n).splitToCols(n),e.moveToBookmark(i).select()}},UE.commands.adaptbytext=UE.commands.adaptbywindow={queryCommandState:function(){return t(this).table?0:-1},execCommand:function(e){var i=t(this).table;if(i)if("adaptbywindow"==e)r(i,this);else{var n=domUtils.getElementsByTagName(i,"td th");utils.each(n,function(e){e.removeAttribute("width")}),i.removeAttribute("width")}}},UE.commands.averagedistributecol={queryCommandState:function(){var e=i(this);return e&&(e.isFullRow()||e.isFullCol())?0:-1},execCommand:function(e){var t=this,o=i(t);o&&o.selectedTds.length&&function(e){utils.each(domUtils.getElementsByTagName(o.table,"th"),function(e){e.setAttribute("width","")});var t=o.isFullRow()?domUtils.getElementsByTagName(o.table,"td"):o.selectedTds;utils.each(t,function(t){1==t.colSpan&&t.setAttribute("width",e)})}(function(){var e=o.table,i=0,r=0,a=n(t,e);if(o.isFullRow())i=e.offsetWidth,r=o.colsNum;else for(var s,l=o.cellsRange.beginColIndex,d=o.cellsRange.endColIndex,c=l;c<=d;)i+=(s=o.selectedTds[c]).offsetWidth,c+=s.colSpan,r+=1;return Math.ceil(i/r)-2*a.tdBorder-2*a.tdPadding}())}},UE.commands.averagedistributerow={queryCommandState:function(){var e=i(this);return e?e.selectedTds&&/th/gi.test(e.selectedTds[0].tagName)?-1:e.isFullRow()||e.isFullCol()?0:-1:-1},execCommand:function(e){var t,o,r=this,a=i(r);a&&a.selectedTds.length&&(t=function(){var e,t=0,i=a.table,o=n(r,i),s=parseInt(domUtils.getComputedStyle(i.getElementsByTagName("td")[0],"padding-top"));if(a.isFullCol()){var l,d,c=domUtils.getElementsByTagName(i,"caption"),u=domUtils.getElementsByTagName(i,"th");c.length>0&&(l=c[0].offsetHeight),u.length>0&&(d=u[0].offsetHeight),t=i.offsetHeight-(l||0)-(d||0),e=0==u.length?a.rowsNum:a.rowsNum-1}else{for(var m=a.cellsRange.beginRowIndex,f=a.cellsRange.endRowIndex,h=0,p=domUtils.getElementsByTagName(i,"tr"),g=m;g<=f;g++)t+=p[g].offsetHeight,h+=1;e=h}return browser.ie&&browser.version<9?Math.ceil(t/e):Math.ceil(t/e)-2*o.tdBorder-2*s}(),o=a.isFullCol()?domUtils.getElementsByTagName(a.table,"td"):a.selectedTds,utils.each(o,function(e){1==e.rowSpan&&e.setAttribute("height",t)}))}},UE.commands.cellalignment={queryCommandState:function(){return t(this).table?0:-1},execCommand:function(e,t){var n=i(this);if(n)utils.each(n.selectedTds,function(e){domUtils.setAttributes(e,t)});else{var o=this.selection.getStart(),r=o&&domUtils.findParentByTagName(o,["td","th","caption"],!0);/caption/gi.test(r.tagName)?(r.style.textAlign=t.align,r.style.verticalAlign=t.vAlign):domUtils.setAttributes(r,t),this.selection.getRange().setCursor(!0)}},queryCommandValue:function(e){var i=t(this).cell;if(i||(i=a(this)[0]),i){var n=UE.UETable.getUETable(i).selectedTds;return!n.length&&(n=i),UE.UETable.getTableCellAlignState(n)}return null}},UE.commands.tablealignment={queryCommandState:function(){return browser.ie&&browser.version<8?-1:t(this).table?0:-1},execCommand:function(e,t){var i=this.selection.getStart(),n=i&&domUtils.findParentByTagName(i,["table"],!0);n&&n.setAttribute("align",t)}},UE.commands.edittable={queryCommandState:function(){return t(this).table?0:-1},execCommand:function(e,t){var i=this.selection.getRange(),n=domUtils.findParentByTagName(i.startContainer,"table");if(n){var o=domUtils.getElementsByTagName(n,"td").concat(domUtils.getElementsByTagName(n,"th"),domUtils.getElementsByTagName(n,"caption"));utils.each(o,function(e){e.style.borderColor=t})}}},UE.commands.edittd={queryCommandState:function(){return t(this).table?0:-1},execCommand:function(e,t){var n=i(this);if(n)utils.each(n.selectedTds,function(e){e.style.backgroundColor=t});else{var o=this.selection.getStart(),r=o&&domUtils.findParentByTagName(o,["td","th","caption"],!0);r&&(r.style.backgroundColor=t)}}},UE.commands.settablebackground={queryCommandState:function(){return a(this).length>1?0:-1},execCommand:function(e,t){var i;i=a(this),o(i[0]).setBackground(i,t)}},UE.commands.cleartablebackground={queryCommandState:function(){var e=a(this);if(!e.length)return-1;for(var t,i=0;t=e[i++];)if(""!==t.style.backgroundColor)return 0;return-1},execCommand:function(){var e=a(this);o(e[0]).removeBackground(e)}},UE.commands.interlacetable=UE.commands.uninterlacetable={queryCommandState:function(e){var i=t(this).table;if(!i)return-1;var n=i.getAttribute("interlaced");return"interlacetable"==e?"enabled"===n?-1:0:n&&"disabled"!==n?0:-1},execCommand:function(e,i){var n=t(this).table;"interlacetable"==e?(n.setAttribute("interlaced","enabled"),this.fireEvent("interlacetable",n,i)):(n.setAttribute("interlaced","disabled"),this.fireEvent("uninterlacetable",n))}},UE.commands.setbordervisible={queryCommandState:function(e){return t(this).table?0:-1},execCommand:function(){var e=t(this).table;utils.each(domUtils.getElementsByTagName(e,"td"),function(e){e.style.borderWidth="1px",e.style.borderStyle="solid"})}}}(),UE.plugins.table=function(){var e=this,t=null,i=5,n=!1,o=5,r=10,a=0,s=null,l=360,d=UE.UETable,c=function(e){return d.getUETable(e)},u=function(e){return d.getUETableBySelected(e)},m=function(e,t){return d.getDefaultValue(e,t)},f=function(e){return d.removeSelectedClass(e)};e.ready(function(){var e=this,t=e.selection.getText;e.selection.getText=function(){var i=u(e);if(i){var n="";return utils.each(i.selectedTds,function(e){n+=e[browser.ie?"innerText":"textContent"]}),n}return t.call(e.selection)}});var h=null,p=null,g="",v=!1,b=null,y=!1,C=null,N=null,x=!1;e.setOpt({maxColNum:20,maxRowNum:100,defaultCols:5,defaultRows:5,tdvalign:"top",cursorpath:e.options.UEDITOR_HOME_URL+"themes/default/images/cursor_",tableDragable:!1,classList:["ue-table-interlace-color-single","ue-table-interlace-color-double"]}),e.getUETable=c;var w={deletetable:1,inserttable:1,cellvalign:1,insertcaption:1,deletecaption:1,inserttitle:1,deletetitle:1,mergeright:1,mergedown:1,mergecells:1,insertrow:1,insertrownext:1,deleterow:1,insertcol:1,insertcolnext:1,deletecol:1,splittocells:1,splittorows:1,splittocols:1,adaptbytext:1,adaptbywindow:1,adaptbycustomer:1,insertparagraph:1,insertparagraphbeforetable:1,averagedistributecol:1,averagedistributerow:1};function U(e,t){E(e,"width",!0),E(e,"height",!0)}function E(e,t,i){e.style[t]&&(i&&e.setAttribute(t,parseInt(e.style[t],10)),e.style[t]="")}function T(e){return"TD"==e.tagName||"TH"==e.tagName?e:(t=domUtils.findParentByTagName(e,"td",!0)||domUtils.findParentByTagName(e,"th",!0))?t:null;var t}function S(e){var t=new RegExp(domUtils.fillChar,"g");if(e[browser.ie?"innerText":"textContent"].replace(/^\s*$/,"").replace(t,"").length>0)return 0;for(var i in dtd.$isNotEmpty)if(e.getElementsByTagName(i).length)return 0;return 1}function k(t){return t.pageX||t.pageY?{x:t.pageX,y:t.pageY}:{x:t.clientX+e.document.body.scrollLeft-e.document.body.clientLeft,y:t.clientY+e.document.body.scrollTop-e.document.body.clientTop}}function _(t){if(!q())try{var o,l=T(t.target||t.srcElement);if(n&&(e.body.style.webkitUserSelect="none",(Math.abs(s.x-t.clientX)>r||Math.abs(s.y-t.clientY)>r)&&(P(),n=!1,a=0,M(t))),g&&N)return a=0,e.body.style.webkitUserSelect="none",e.selection.getNative()[browser.ie9below?"empty":"removeAllRanges"](),o=k(t),A(e,!0,g,o,l),void("h"==g?C.style.left=function(t,n){var o=c(t);if(o){var r=o.getSameEndPosCells(t,"x")[0],a=o.getSameStartPosXCells(t)[0],s=k(n).x,l=(r?domUtils.getXY(r).x:domUtils.getXY(o.table).x)+20,d=a?domUtils.getXY(a).x+a.offsetWidth-20:e.body.offsetWidth+5||parseInt(domUtils.getComputedStyle(e.body,"width"),10);return d-=i,s<(l+=i)?l:s>d?d:s}}(N,t)+"px":"v"==g&&(C.style.top=function(e,t){try{var i=domUtils.getXY(e).y,n=k(t).y;return n<i?i:n}catch(e){}}(N,t)+"px"));if(l){if(!0===e.fireEvent("excludetable",l))return;var d=R(l,o=k(t)),u=domUtils.findParentByTagName(l,"table",!0);if(I(u,l,t,!0)){if(!0===e.fireEvent("excludetable",u))return;e.body.style.cursor="url("+e.options.cursorpath+"h.png),pointer"}else if(I(u,l,t)){if(!0===e.fireEvent("excludetable",u))return;e.body.style.cursor="url("+e.options.cursorpath+"v.png),pointer"}else{e.body.style.cursor="text";/\d/.test(d)&&(d=d.replace(/\d/,""),l=c(l).getPreviewCell(l,"v"==d)),A(e,!!l&&!!d,l?d:"",o,l)}}else B(!1,u,e)}catch(e){}}function B(e,t,i){if(e)!function(e,t){var i,n=domUtils.getXY(e),o=e.ownerDocument;if(b&&b.parentNode)return b;(b=o.createElement("div")).contentEditable=!1,b.innerHTML="",b.style.cssText="width:15px;height:15px;background-image:url("+t.options.UEDITOR_HOME_URL+"dialogs/table/dragicon.png);position: absolute;cursor:move;top:"+(n.y-15)+"px;left:"+n.x+"px;",domUtils.unSelectable(b),b.onmouseover=function(e){y=!0},b.onmouseout=function(e){y=!1},domUtils.on(b,"click",function(n,o){var r;r=this,clearTimeout(i),i=setTimeout(function(){t.fireEvent("tableClicked",e,r)},300)}),domUtils.on(b,"dblclick",function(n,o){!function(n){clearTimeout(i);var o=c(e),r=e.rows[0].cells[0],a=o.getLastCell(),s=o.getCellsRange(r,a);t.selection.getRange().setStart(r,0).setCursor(!1,!0),o.setSelected(s)}()}),domUtils.on(b,"dragstart",function(e,t){domUtils.preventDefault(t)}),o.body.appendChild(b)}(t,i);else{if(y)return;setTimeout(function(){!y&&b&&b.parentNode&&b.parentNode.removeChild(b)},2e3)}}function I(e,t,i,n){var o=k(i),r=R(t,o);if(n){var a=e.getElementsByTagName("caption")[0],s=a?a.offsetHeight:0;return"v1"==r&&o.y-domUtils.getXY(e).y-s<8}return"h1"==r&&o.x-domUtils.getXY(e).x<8}function A(e,t,i,n,o){try{e.body.style.cursor="h"==i?"col-resize":"v"==i?"row-resize":"text",browser.ie&&(!i||x||u(e)?X(e):(j(e,e.document),Y(i,o))),v=t}catch(e){}}function R(e,t){var i=domUtils.getXY(e);return i?i.x+e.offsetWidth-t.x<o?"h":t.x-i.x<o?"h1":i.y+e.offsetHeight-t.y<o?"v":t.y-i.y<o?"v1":"":""}function L(t,i){if(!q())if(s={x:i.clientX,y:i.clientY},2==i.button){var n=u(e),o=!1;if(n){var r=G(e,i);utils.each(n.selectedTds,function(e){e===r&&(o=!0)}),o?(r=n.selectedTds[0],setTimeout(function(){e.selection.getRange().setStart(r,0).setCursor(!1,!0)},0)):(f(domUtils.getElementsByTagName(e.body,"th td")),n.clearSelected())}}else O(i)}function D(t){a=0;var i,n=T((t=t||e.window.event).target||t.srcElement);if(n&&(i=R(n,k(t)))){if(X(e),"h1"==i)if(i="h",I(domUtils.findParentByTagName(n,"table"),n,t))e.execCommand("adaptbywindow");else if(n=c(n).getPreviewCell(n))e.selection.getRange().selectNodeContents(n).setCursor(!0,!0);if("h"==i){var o=c(n),r=V(n,o.table,!0);r=function(e,t){for(var i=[],n=null,o=0,r=e.length;o<r;o++)(n=e[o][t])&&i.push(n);return i}(r,"left"),o.width=o.offsetWidth;var s=[],l=[];utils.each(r,function(e){s.push(e.offsetWidth)}),utils.each(r,function(e){e.removeAttribute("width")}),window.setTimeout(function(){var e=!0;utils.each(r,function(t,i){var n=t.offsetWidth;if(n>s[i])return e=!1,!1;l.push(n)});var t=e?l:s;utils.each(r,function(e,i){e.width=t[i]-W()})},0)}}}function O(i){if(f(domUtils.getElementsByTagName(e.body,"td th")),utils.each(e.document.getElementsByTagName("table"),function(e){e.ueTable=null}),h=G(e,i)){var o=domUtils.findParentByTagName(h,"table",!0);ut=c(o),ut&&ut.clearSelected(),v?function(e){browser.ie&&(e=function(e){var t=["pageX","pageY","clientX","clientY","srcElement","target"],i={};if(e)for(var n,o,r=0;n=t[r];r++)(o=e[n])&&(i[n]=o);return i}(e));P(),n=!0,t=setTimeout(function(){M(e)},l)}(i):(e.document.body.style.webkitUserSelect="",x=!0,e.addListener("mouseover",F))}}function P(){t&&clearTimeout(t),t=null}function M(t){if(n=!1,h=t.target||t.srcElement){var i=R(h,k(t));/\d/.test(i)&&(i=i.replace(/\d/,""),h=c(h).getPreviewCell(h,"v"==i)),X(e),j(e,e.document),e.fireEvent("saveScene"),Y(i,h),x=!0,g=i,N=h}}function H(e,t){if(!q()){if(P(),n=!1,v&&(a=++a%3,s={x:t.clientX,y:t.clientY},setTimeout(function(){a>0&&a--},l),2===a))return a=0,void D(t);if(2!=t.button){var o=this,r=o.selection.getRange(),d=domUtils.findParentByTagName(r.startContainer,"table",!0),u=domUtils.findParentByTagName(r.endContainer,"table",!0);if((d||u)&&(d===u?(d=domUtils.findParentByTagName(r.startContainer,["td","th","caption"],!0))!==(u=domUtils.findParentByTagName(r.endContainer,["td","th","caption"],!0))&&o.selection.clearRange():o.selection.clearRange()),x=!1,o.document.body.style.webkitUserSelect="",g&&N&&(o.selection.getNative()[browser.ie9below?"empty":"removeAllRanges"](),a=0,C=o.document.getElementById("ue_tableDragLine"))){var m=domUtils.getXY(N),f=domUtils.getXY(C);switch(g){case"h":!function(e,t){var n=c(e);if(n){var o=n.table,r=V(e,o);if(o.style.width="",o.removeAttribute("width"),t=function(e,t,n){if((e-=W())<0)return 0;var o=(e-=z(t))<0?"left":"right";return e=Math.abs(e),utils.each(n,function(t){var n=t[o];n&&(e=Math.min(e,z(n)-i))}),e=e<0?0:e,"left"===o?-e:e}(t,e,r),e.nextSibling){utils.each(r,function(e){e.left.width=+e.left.width+t,e.right&&(e.right.width=+e.right.width-t)})}else utils.each(r,function(e){e.left.width-=-t})}}(N,f.x-m.x);break;case"v":!function(e,t){if(Math.abs(t)<10)return;var i=c(e);if(i)for(var n,o=i.getSameEndPosCells(e,"y"),r=o[0]?o[0].offsetHeight:0,a=0;n=o[a++];)$(n,t,r)}(N,f.y-m.y-N.offsetHeight)}return g="",N=null,X(o),void o.fireEvent("saveScene")}if(h){var p=c(h),b=p?p.selectedTds[0]:null;if(b)r=new dom.Range(o.document),domUtils.isEmptyBlock(b)?r.setStart(b,0).setCursor(!1,!0):r.selectNodeContents(b).shrinkBoundary().setCursor(!1,!0);else if(!(r=o.selection.getRange().shrinkBoundary()).collapsed){d=domUtils.findParentByTagName(r.startContainer,["td","th"],!0),u=domUtils.findParentByTagName(r.endContainer,["td","th"],!0);(d&&!u||!d&&u||d&&u&&d!==u)&&r.setCursor(!1,!0)}h=null,o.removeListener("mouseover",F)}else{var y=domUtils.findParentByTagName(t.target||t.srcElement,"td",!0);if(y||(y=domUtils.findParentByTagName(t.target||t.srcElement,"th",!0)),y&&("TD"==y.tagName||"TH"==y.tagName)){if(!0===o.fireEvent("excludetable",y))return;(r=new dom.Range(o.document)).setStart(y,0).setCursor(!1,!0)}}o._selectionChange(250,t)}}}function F(e,t){if(!q()){var i=t.target||t.srcElement;if(p=domUtils.findParentByTagName(i,"td",!0)||domUtils.findParentByTagName(i,"th",!0),h&&p&&("TD"==h.tagName&&"TD"==p.tagName||"TH"==h.tagName&&"TH"==p.tagName)&&domUtils.findParentByTagName(h,"table")==domUtils.findParentByTagName(p,"table")){var n=c(p);if(h!=p){this.document.body.style.webkitUserSelect="none",this.selection.getNative()[browser.ie9below?"empty":"removeAllRanges"]();var o=n.getCellsRange(h,p);n.setSelected(o)}else this.document.body.style.webkitUserSelect="",n.clearSelected()}t.preventDefault?t.preventDefault():t.returnValue=!1}}function $(e,t,i){var n=parseInt(domUtils.getComputedStyle(e,"line-height"),10),o=i+t;t=o<n?n:o,e.style.height&&(e.style.height=""),1==e.rowSpan?e.setAttribute("height",t):e.removeAttribute&&e.removeAttribute("height")}function q(){return"false"===e.body.contentEditable}function V(e,t,i){if(t||(t=domUtils.findParentByTagName(e,"table")),!t)return null;domUtils.getNodeIndex(e);for(var n=e,o=t.rows,r=0;n;)1===n.nodeType&&(r+=n.colSpan||1),n=n.previousSibling;n=null;var a=[];return utils.each(o,function(e){var t=e.cells,n=0;utils.each(t,function(e){return(n+=e.colSpan||1)===r?(a.push({left:e,right:e.nextSibling||null}),!1):n>r?(i&&a.push({left:e}),!1):void 0})}),a}function z(e){var t=0;t=e.offsetWidth-W();e.nextSibling||(t-=function(e){if(tab=domUtils.findParentByTagName(e,"table",!1),void 0===tab.offsetVal){var t=e.previousSibling;tab.offsetVal=t&&e.offsetWidth-t.offsetWidth===d.borderWidth?d.borderWidth:0}return tab.offsetVal}(e)),t=t<0?0:t;try{e.width=t}catch(e){}return t}function W(){if(void 0===d.tabcellSpace){var t=e.document.createElement("table"),i=e.document.createElement("tbody"),n=e.document.createElement("tr"),o=e.document.createElement("td"),r=null;o.style.cssText="border: 0;",o.width=1,n.appendChild(o),n.appendChild(r=o.cloneNode(!1)),i.appendChild(n),t.appendChild(i),t.style.cssText="visibility: hidden;",e.body.appendChild(t),d.paddingSpace=o.offsetWidth-1;var a=t.offsetWidth;o.style.cssText="",r.style.cssText="",d.borderWidth=(t.offsetWidth-a)/3,d.tabcellSpace=d.paddingSpace+d.borderWidth,e.body.removeChild(t)}return W=function(){return d.tabcellSpace},d.tabcellSpace}function j(e,t){x||(C=e.document.createElement("div"),domUtils.setAttributes(C,{id:"ue_tableDragLine",unselectable:"on",contenteditable:!1,onresizestart:"return false",ondragstart:"return false",onselectstart:"return false",style:"background-color:blue;position:absolute;padding:0;margin:0;background-image:none;border:0px none;opacity:0;filter:alpha(opacity=0)"}),e.body.appendChild(C))}function X(e){if(!x)for(var t;t=e.document.getElementById("ue_tableDragLine");)domUtils.remove(t)}function Y(e,t){if(t){var i,n=domUtils.findParentByTagName(t,"table"),o=n.getElementsByTagName("caption"),r=n.offsetWidth,a=n.offsetHeight-(o.length>0?o[0].offsetHeight:0),s=domUtils.getXY(n),l=domUtils.getXY(t);switch(e){case"h":i="height:"+a+"px;top:"+(s.y+(o.length>0?o[0].offsetHeight:0))+"px;left:"+(l.x+t.offsetWidth),C.style.cssText=i+"px;position: absolute;display:block;background-color:blue;width:1px;border:0; color:blue;opacity:.3;filter:alpha(opacity=30)";break;case"v":i="width:"+r+"px;left:"+s.x+"px;top:"+(l.y+t.offsetHeight),C.style.cssText=i+"px;overflow:hidden;position: absolute;display:block;background-color:blue;height:1px;border:0;color:blue;opacity:.2;filter:alpha(opacity=20)"}}}function K(e,t){for(var i,n,o=domUtils.getElementsByTagName(e.body,"table"),r=0;n=o[r++];){var a=domUtils.getElementsByTagName(n,"td");a[0]&&(t?(i=a[0].style.borderColor.replace(/\s/g,""),/(#ffffff)|(rgb\(255,255,255\))/gi.test(i)&&domUtils.addClass(n,"noBorderTable")):domUtils.removeClasses(n,"noBorderTable"))}}function G(e,t){var i,n=domUtils.findParentByTagName(t.target||t.srcElement,["td","th"],!0);if(!n)return null;if(i=R(n,k(t)),!n)return null;if("h1"===i&&n.previousSibling){var o=domUtils.getXY(n),r=n.offsetWidth;Math.abs(o.x+r-t.clientX)>r/3&&(n=n.previousSibling)}else if("v1"===i&&n.parentNode.previousSibling){o=domUtils.getXY(n);var a=n.offsetHeight;Math.abs(o.y+a-t.clientY)>a/3&&(n=n.parentNode.previousSibling.firstChild)}return n&&!0!==e.fireEvent("excludetable",n)?n:null}e.ready(function(){var t,i,n,o;utils.cssRule("table",".selectTdClass{background-color:#edf5fa !important}table.noBorderTable td,table.noBorderTable th,table.noBorderTable caption{border:1px dashed #ddd !important}table{margin-bottom:10px;border-collapse:collapse;display:table;}td,th{padding: 5px 10px;border: 1px solid #DDD;}caption{border:1px dashed #DDD;border-bottom:0;padding:3px;text-align:center;}th{border-top:1px solid #BBB;background-color:#F7F7F7;}table tr.firstRow th{border-top-width:2px;}.ue-table-interlace-color-single{ background-color: #fcfcfc; } .ue-table-interlace-color-double{ background-color: #f7faff; }td p{margin:0;padding:0;}",e.document),e.addListener("keydown",function(e,o){var r=this,a=o.keyCode||o.which;if(8==a){var s;(s=u(r))&&s.selectedTds.length&&(s.isFullCol()?r.execCommand("deletecol"):s.isFullRow()?r.execCommand("deleterow"):r.fireEvent("delcells"),domUtils.preventDefault(o));var l=domUtils.findParentByTagName(r.selection.getStart(),"caption",!0),d=r.selection.getRange();if(d.collapsed&&l&&S(l)){r.fireEvent("saveScene");var c=l.parentNode;domUtils.remove(l),c&&d.setStart(c.rows[0].cells[0],0).setCursor(!1,!0),r.fireEvent("saveScene")}}if(46==a&&(s=u(r))){r.fireEvent("saveScene");for(var m=0;y=s.selectedTds[m++];)domUtils.fillNode(r.document,y);r.fireEvent("saveScene"),domUtils.preventDefault(o)}if(13==a){var f=r.selection.getRange();if(l=domUtils.findParentByTagName(f.startContainer,"caption",!0)){var c=domUtils.findParentByTagName(l,"table");return f.collapsed?l&&f.setStart(c.rows[0].cells[0],0).setCursor(!1,!0):(f.deleteContents(),r.fireEvent("saveScene")),void domUtils.preventDefault(o)}if(f.collapsed)if(c=domUtils.findParentByTagName(f.startContainer,"table")){var h=c.rows[0].cells[0],p=domUtils.findParentByTagName(r.selection.getStart(),["td","th"],!0),g=c.previousSibling;if(h===p&&(!g||1==g.nodeType&&"TABLE"==g.tagName)&&domUtils.isStartInblock(f)){var v=domUtils.findParent(r.selection.getStart(),function(e){return domUtils.isBlockElm(e)},!0);v&&(/t(h|d)/i.test(v.tagName)||v===p.firstChild)&&(r.execCommand("insertparagraphbeforetable"),domUtils.preventDefault(o))}}}if((o.ctrlKey||o.metaKey)&&"67"==o.keyCode&&(t=null,s=u(r))){var b=s.selectedTds;i=s.isFullCol(),n=s.isFullRow(),t=[[s.cloneCell(b[0],null,!0)]];var y;for(m=1;y=b[m];m++)y.parentNode!==b[m-1].parentNode?t.push([s.cloneCell(y,null,!0)]):t[t.length-1].push(s.cloneCell(y,null,!0))}}),e.addListener("tablehasdeleted",function(){A(this,!1,"",null),b&&domUtils.remove(b)}),e.addListener("beforepaste",function(e,o){var r=this,a=r.selection.getRange();if(domUtils.findParentByTagName(a.startContainer,"caption",!0))return(s=r.document.createElement("div")).innerHTML=o.html,void(o.html=s[browser.ie9below?"innerText":"textContent"]);var s,l,f=u(r);if(t){r.fireEvent("saveScene");a=r.selection.getRange();var h,p,g=domUtils.findParentByTagName(a.startContainer,["td","th"],!0);if(g){var v=c(g);if(n){var b=v.getCellInfo(g).rowIndex;"TH"==g.tagName&&b++;for(var y=0;E=t[y++];){for(var C=v.insertRow(b++,"td"),N=0;_=E[N];N++){var x=C.cells[N];x||(x=C.insertCell(N)),x.innerHTML=_.innerHTML,_.getAttribute("width")&&x.setAttribute("width",_.getAttribute("width")),_.getAttribute("vAlign")&&x.setAttribute("vAlign",_.getAttribute("vAlign")),_.getAttribute("align")&&x.setAttribute("align",_.getAttribute("align")),_.style.cssText&&(x.style.cssText=_.style.cssText)}for(N=0;(_=C.cells[N])&&E[N];N++)_.innerHTML=E[N].innerHTML,E[N].getAttribute("width")&&_.setAttribute("width",E[N].getAttribute("width")),E[N].getAttribute("vAlign")&&_.setAttribute("vAlign",E[N].getAttribute("vAlign")),E[N].getAttribute("align")&&_.setAttribute("align",E[N].getAttribute("align")),E[N].style.cssText&&(_.style.cssText=E[N].style.cssText)}}else{if(i){k=v.getCellInfo(g);for(var w=0,E=(N=0,t[0]);_=E[N++];)w+=_.colSpan||1;for(r.__hasEnterExecCommand=!0,y=0;y<w;y++)r.execCommand("insertcol");r.__hasEnterExecCommand=!1,"TH"==(g=v.table.rows[0].cells[k.cellIndex]).tagName&&(g=v.table.rows[1].cells[k.cellIndex])}for(y=0;E=t[y++];){h=g;for(N=0;_=E[N++];)if(g)g.innerHTML=_.innerHTML,_.getAttribute("width")&&g.setAttribute("width",_.getAttribute("width")),_.getAttribute("vAlign")&&g.setAttribute("vAlign",_.getAttribute("vAlign")),_.getAttribute("align")&&g.setAttribute("align",_.getAttribute("align")),_.style.cssText&&(g.style.cssText=_.style.cssText),p=g,g=g.nextSibling;else{var T=_.cloneNode(!0);domUtils.removeAttributes(T,["class","rowSpan","colSpan"]),p.parentNode.appendChild(T)}if(g=v.getNextCell(h,!0,!0),!t[y])break;if(!g){var k=v.getCellInfo(h);v.table.insertRow(v.table.rows.length),v.update(),g=v.getVSideCell(h,!0)}}}v.update()}else{f=r.document.createElement("table");for(y=0;E=t[y++];){var _;for(C=f.insertRow(f.rows.length),N=0;_=E[N++];)T=d.cloneCell(_,null,!0),domUtils.removeAttributes(T,["class"]),C.appendChild(T);2==N&&T.rowSpan>1&&(T.rowSpan=1)}var B=m(r),I=r.body.offsetWidth-2*parseInt(domUtils.getComputedStyle(r.body,"margin-left"),10)-2*B.tableBorder-(r.options.offsetWidth||0);r.execCommand("insertHTML","<table  "+(i&&n?'width="'+I+'"':"")+">"+f.innerHTML.replace(/>\s*</g,"><").replace(/\bth\b/gi,"td")+"</table>")}return r.fireEvent("contentchange"),r.fireEvent("saveScene"),o.html="",!0}(s=r.document.createElement("div")).innerHTML=o.html,l=s.getElementsByTagName("table"),domUtils.findParentByTagName(r.selection.getStart(),"table")?(utils.each(l,function(e){domUtils.remove(e)}),domUtils.findParentByTagName(r.selection.getStart(),"caption",!0)&&(s.innerHTML=s[browser.ie?"innerText":"textContent"])):utils.each(l,function(e){U(e,!0),domUtils.removeAttributes(e,["style","border"]),utils.each(domUtils.getElementsByTagName(e,"td"),function(e){S(e)&&domUtils.fillNode(r.document,e),U(e,!0)})}),o.html=s.innerHTML}),e.addListener("afterpaste",function(){utils.each(domUtils.getElementsByTagName(e.body,"table"),function(t){if(t.offsetWidth>e.body.offsetWidth){var i=m(e,t);t.style.width=e.body.offsetWidth-2*parseInt(domUtils.getComputedStyle(e.body,"margin-left"),10)-2*i.tableBorder-(e.options.offsetWidth||0)+"px"}})}),e.addListener("blur",function(){t=null}),e.addListener("keydown",function(){clearTimeout(o),o=setTimeout(function(){var t=e.selection.getRange(),i=domUtils.findParentByTagName(t.startContainer,["th","td"],!0);if(i){var n=i.parentNode.parentNode.parentNode;n.offsetWidth>n.getAttribute("width")&&(i.style.wordBreak="break-all")}},100)}),e.addListener("selectionchange",function(){A(e,!1,"",null)}),e.addListener("contentchange",function(){var e=this;if(X(e),!u(e)){var t=e.selection.getRange().startContainer;t=domUtils.findParentByTagName(t,["td","th"],!0),utils.each(domUtils.getElementsByTagName(e.document,"table"),function(t){!0!==e.fireEvent("excludetable",t)&&(t.ueTable=new d(t),t.onmouseover=function(){e.fireEvent("tablemouseover",t)},t.onmousemove=function(){e.fireEvent("tablemousemove",t),e.options.tableDragable&&B(!0,this,e),utils.defer(function(){e.fireEvent("contentchange",50)},!0)},t.onmouseout=function(){e.fireEvent("tablemouseout",t),A(e,!1,"",null),X(e)},t.onclick=function(t){var i=T((t=e.window.event||t).target||t.srcElement);if(i){var n,o=c(i),r=o.table,a=o.getCellInfo(i),s=e.selection.getRange();if(I(r,i,t,!0)){var l=o.getCell(o.indexTable[o.rowsNum-1][a.colIndex].rowIndex,o.indexTable[o.rowsNum-1][a.colIndex].cellIndex);t.shiftKey&&o.selectedTds.length?o.selectedTds[0]!==l?(n=o.getCellsRange(o.selectedTds[0],l),o.setSelected(n)):s&&s.selectNodeContents(l).select():i!==l?(n=o.getCellsRange(i,l),o.setSelected(n)):s&&s.selectNodeContents(l).select()}else if(I(r,i,t)){var d=o.getCell(o.indexTable[a.rowIndex][o.colsNum-1].rowIndex,o.indexTable[a.rowIndex][o.colsNum-1].cellIndex);t.shiftKey&&o.selectedTds.length?o.selectedTds[0]!==d?(n=o.getCellsRange(o.selectedTds[0],d),o.setSelected(n)):s&&s.selectNodeContents(d).select():i!==d?(n=o.getCellsRange(i,d),o.setSelected(n)):s&&s.selectNodeContents(d).select()}}})}),K(e,!0)}}),domUtils.on(e.document,"mousemove",_),domUtils.on(e.document,"mouseout",function(t){"TABLE"==(t.target||t.srcElement).tagName&&A(e,!1,"",null)}),e.addListener("interlacetable",function(e,t,i){if(t)for(var n,o,r,a=t.rows,s=a.length,l=0;l<s;l++)a[l].className=(n=i||this.options.classList,r=!0,n[o=l]?n[o]:r?n[o%n.length]:"")}),e.addListener("uninterlacetable",function(e,t){if(t)for(var i=t.rows,n=this.options.classList,o=i.length,r=0;r<o;r++)domUtils.removeClasses(i[r],n)}),e.addListener("mousedown",L),e.addListener("mouseup",H),domUtils.on(e.body,"dragstart",function(t){H.call(e,"dragstart",t)}),e.addOutputRule(function(e){utils.each(e.getNodesByTagName("div"),function(e){"ue_tableDragLine"==e.getAttr("id")&&e.parentNode.removeChild(e)})});var r=0;e.addListener("mousedown",function(){r=0}),e.addListener("tabkeydown",function(){var t=this.selection.getRange(),i=t.getCommonAncestor(!0,!0),n=domUtils.findParentByTagName(i,"table");if(n){if(domUtils.findParentByTagName(i,"caption",!0)){(o=domUtils.getElementsByTagName(n,"th td"))&&o.length&&t.setStart(o[0],0).setCursor(!1,!0)}else{var o=domUtils.findParentByTagName(i,["td","th"],!0),a=c(o);r=o.rowSpan>1?r:a.getCellInfo(o).rowIndex;var s=a.getTabNextCell(o,r);s?S(s)?t.setStart(s,0).setCursor(!1,!0):t.selectNodeContents(s).select():(e.fireEvent("saveScene"),e.__hasEnterExecCommand=!0,this.execCommand("insertrownext"),e.__hasEnterExecCommand=!1,(t=this.selection.getRange()).setStart(n.rows[n.rows.length-1].cells[0],0).setCursor(),e.fireEvent("saveScene"))}return!0}}),browser.ie&&e.addListener("selectionchange",function(){A(this,!1,"",null)}),e.addListener("keydown",function(e,t){var i=t.keyCode||t.which;if(8!=i&&46!=i){var n=!(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey);n&&f(domUtils.getElementsByTagName(this.body,"td"));var o=u(this);o&&n&&o.clearSelected()}}),e.addListener("beforegetcontent",function(){K(this,!1),browser.ie&&utils.each(this.document.getElementsByTagName("caption"),function(e){domUtils.isEmptyNode(e)&&(e.innerHTML="&nbsp;")})}),e.addListener("aftergetcontent",function(){K(this,!0)}),e.addListener("getAllHtml",function(){f(e.document.getElementsByTagName("td"))}),e.addListener("fullscreenchanged",function(t,i){if(!i){var n=this.body.offsetWidth/document.body.offsetWidth,o=domUtils.getElementsByTagName(this.body,"table");utils.each(o,function(t){if(t.offsetWidth<e.body.offsetWidth)return!1;var i=domUtils.getElementsByTagName(t,"td"),o=[];utils.each(i,function(e){o.push(e.offsetWidth)});for(var r,a=0;r=i[a];a++)r.setAttribute("width",Math.floor(o[a]*n));t.setAttribute("width",Math.floor(function(e,t,i){var n=e.body;return n.offsetWidth-(t?2*parseInt(domUtils.getComputedStyle(n,"margin-left"),10):0)-2*i.tableBorder-(e.options.offsetWidth||0)}(e,!0,m(e))))})}});var a=e.execCommand;e.execCommand=function(e,t){var i=this;e=e.toLowerCase();var n,o,r=u(i),s=new dom.Range(i.document),l=i.commands[e]||UE.commands[e];if(l){if(!r||w[e]||l.notNeedUndo||i.__hasEnterExecCommand)o=a.apply(i,arguments);else{i.__hasEnterExecCommand=!0,i.fireEvent("beforeexeccommand",e),n=r.selectedTds;for(var d,c,m,f=-2,h=-2,p=0;m=n[p];p++)S(m)?s.setStart(m,0).setCursor(!1,!0):s.selectNode(m).select(!0),c=i.queryCommandState(e),d=i.queryCommandValue(e),-1!=c&&(f===c&&h===d||(i._ignoreContentChange=!0,o=a.apply(i,arguments),i._ignoreContentChange=!1),f=i.queryCommandState(e),h=i.queryCommandValue(e),domUtils.isEmptyBlock(m)&&domUtils.fillNode(i.document,m));s.setStart(n[0],0).shrinkBoundary(!0).setCursor(!1,!0),i.fireEvent("contentchange"),i.fireEvent("afterexeccommand",e),i.__hasEnterExecCommand=!1,i._selectionChange()}return o}}})},UE.UETable.prototype.sortTable=function(e,t){var i=this.table,n=i.rows,o=[],r="TH"===n[0].cells[0].tagName,a=0;if(this.selectedTds.length){for(var s=this.cellsRange,l=s.endRowIndex+1,d=s.beginRowIndex;d<l;d++)o[d]=n[d];o.splice(0,s.beginRowIndex),a=s.endRowIndex+1===this.rowsNum?0:s.endRowIndex+1}else for(d=0,l=n.length;d<l;d++)o[d]=n[d];var c={reversecurrent:function(e,t){return 1},orderbyasc:function(e,t){var i=e.innerText||e.textContent,n=t.innerText||t.textContent;return i.localeCompare(n)},reversebyasc:function(e,t){var i=e.innerHTML;return t.innerHTML.localeCompare(i)},orderbynum:function(e,t){var i=e[browser.ie?"innerText":"textContent"].match(/\d+/),n=t[browser.ie?"innerText":"textContent"].match(/\d+/);return i&&(i=+i[0]),n&&(n=+n[0]),(i||0)-(n||0)},reversebynum:function(e,t){var i=e[browser.ie?"innerText":"textContent"].match(/\d+/),n=t[browser.ie?"innerText":"textContent"].match(/\d+/);return i&&(i=+i[0]),n&&(n=+n[0]),(n||0)-(i||0)}};i.setAttribute("data-sort-type",t&&"string"==typeof t&&c[t]?t:""),r&&o.splice(0,1),o=utils.sort(o,function(i,n){return t&&"function"==typeof t?t.call(this,i.cells[e],n.cells[e]):t&&"number"==typeof t?1:t&&"string"==typeof t&&c[t]?c[t].call(this,i.cells[e],n.cells[e]):c.orderbyasc.call(this,i.cells[e],n.cells[e])});var u=i.ownerDocument.createDocumentFragment(),m=0;for(l=o.length;m<l;m++)u.appendChild(o[m]);var f=i.getElementsByTagName("tbody")[0];a?f.insertBefore(u,n[a-s.endRowIndex+s.beginRowIndex-1]):f.appendChild(u)},UE.plugins.tablesort=function(){var e=this,t=UE.UETable,i=function(e){return t.getTableItemsByRange(e)};e.ready(function(){utils.cssRule("tablesort","table.sortEnabled tr.firstRow th,table.sortEnabled tr.firstRow td{padding-right:20px;background-repeat: no-repeat;background-position: center right;   background-image:url("+e.options.themePath+e.options.theme+"/images/sortable.png);}",e.document),e.addListener("afterexeccommand",function(e,t){"mergeright"!=t&&"mergedown"!=t&&"mergecells"!=t||this.execCommand("disablesort")})}),UE.commands.sorttable={queryCommandState:function(){var e=i(this);if(!e.cell)return-1;for(var t,n=e.table.getElementsByTagName("td"),o=0;t=n[o++];)if(1!=t.rowSpan||1!=t.colSpan)return-1;return 0},execCommand:function(e,n){var o,r=this.selection.getRange(),a=r.createBookmark(!0),s=i(this),l=s.cell,d=(o=s.table,t.getUETable(o)),c=d.getCellInfo(l);d.sortTable(c.cellIndex,n),r.moveToBookmark(a);try{r.select()}catch(e){}}},UE.commands.enablesort=UE.commands.disablesort={queryCommandState:function(e){var t=i(this).table;if(t&&"enablesort"==e)for(var n=domUtils.getElementsByTagName(t,"th td"),o=0;o<n.length;o++)if(n[o].getAttribute("colspan")>1||n[o].getAttribute("rowspan")>1)return-1;return t?"enablesort"==e^"sortEnabled"!=t.getAttribute("data-sort")?-1:0:-1},execCommand:function(e){var t=i(this).table;t.setAttribute("data-sort","enablesort"==e?"sortEnabled":"sortDisabled"),"enablesort"==e?domUtils.addClass(t,"sortEnabled"):domUtils.removeClasses(t,"sortEnabled")}}},UE.plugins.contextmenu=function(){if("undefined"==typeof is_mobile_cms||1!=is_mobile_cms){var e=this;if(e.setOpt("enableContextMenu",!0),!1!==e.getOpt("enableContextMenu")){var t,i=e.getLang("contextMenu"),n=e.options.contextMenu||[{label:i.selectall,cmdName:"selectall"},{label:i.cleardoc,cmdName:"cleardoc",exec:function(){confirm(i.confirmclear)&&this.execCommand("cleardoc")}},"-",{label:i.unlink,cmdName:"unlink"},"-",{group:i.paragraph,icon:"justifyjustify",subMenu:[{label:i.justifyleft,cmdName:"justify",value:"left"},{label:i.justifyright,cmdName:"justify",value:"right"},{label:i.justifycenter,cmdName:"justify",value:"center"},{label:i.justifyjustify,cmdName:"justify",value:"justify"}]},"-",{group:i.table,icon:"table",subMenu:[{label:i.inserttable,cmdName:"inserttable"},{label:i.deletetable,cmdName:"deletetable"},"-",{label:i.deleterow,cmdName:"deleterow"},{label:i.deletecol,cmdName:"deletecol"},{label:i.insertcol,cmdName:"insertcol"},{label:i.insertcolnext,cmdName:"insertcolnext"},{label:i.insertrow,cmdName:"insertrow"},{label:i.insertrownext,cmdName:"insertrownext"},"-",{label:i.insertcaption,cmdName:"insertcaption"},{label:i.deletecaption,cmdName:"deletecaption"},{label:i.inserttitle,cmdName:"inserttitle"},{label:i.deletetitle,cmdName:"deletetitle"},{label:i.inserttitlecol,cmdName:"inserttitlecol"},{label:i.deletetitlecol,cmdName:"deletetitlecol"},"-",{label:i.mergecells,cmdName:"mergecells"},{label:i.mergeright,cmdName:"mergeright"},{label:i.mergedown,cmdName:"mergedown"},"-",{label:i.splittorows,cmdName:"splittorows"},{label:i.splittocols,cmdName:"splittocols"},{label:i.splittocells,cmdName:"splittocells"},"-",{label:i.averageDiseRow,cmdName:"averagedistributerow"},{label:i.averageDisCol,cmdName:"averagedistributecol"},"-",{label:i.edittd,cmdName:"edittd",exec:function(){UE.ui.edittd&&new UE.ui.edittd(this),this.getDialog("edittd").open()}},{label:i.edittable,cmdName:"edittable",exec:function(){UE.ui.edittable&&new UE.ui.edittable(this),this.getDialog("edittable").open()}},{label:i.setbordervisible,cmdName:"setbordervisible"}]},{group:i.tablesort,icon:"tablesort",subMenu:[{label:i.enablesort,cmdName:"enablesort"},{label:i.disablesort,cmdName:"disablesort"},"-",{label:i.reversecurrent,cmdName:"sorttable",value:"reversecurrent"},{label:i.orderbyasc,cmdName:"sorttable",value:"orderbyasc"},{label:i.reversebyasc,cmdName:"sorttable",value:"reversebyasc"},{label:i.orderbynum,cmdName:"sorttable",value:"orderbynum"},{label:i.reversebynum,cmdName:"sorttable",value:"reversebynum"}]},{group:i.borderbk,icon:"borderBack",subMenu:[{label:i.setcolor,cmdName:"interlacetable",exec:function(){this.execCommand("interlacetable")}},{label:i.unsetcolor,cmdName:"uninterlacetable",exec:function(){this.execCommand("uninterlacetable")}},{label:i.setbackground,cmdName:"settablebackground",exec:function(){this.execCommand("settablebackground",{repeat:!0,colorList:["#bbb","#ccc"]})}},{label:i.unsetbackground,cmdName:"cleartablebackground",exec:function(){this.execCommand("cleartablebackground")}},{label:i.redandblue,cmdName:"settablebackground",exec:function(){this.execCommand("settablebackground",{repeat:!0,colorList:["red","blue"]})}},{label:i.threecolorgradient,cmdName:"settablebackground",exec:function(){this.execCommand("settablebackground",{repeat:!0,colorList:["#aaa","#bbb","#ccc"]})}}]},{group:i.aligntd,icon:"aligntd",subMenu:[{cmdName:"cellalignment",value:{align:"left",vAlign:"top"}},{cmdName:"cellalignment",value:{align:"center",vAlign:"top"}},{cmdName:"cellalignment",value:{align:"right",vAlign:"top"}},{cmdName:"cellalignment",value:{align:"left",vAlign:"middle"}},{cmdName:"cellalignment",value:{align:"center",vAlign:"middle"}},{cmdName:"cellalignment",value:{align:"right",vAlign:"middle"}},{cmdName:"cellalignment",value:{align:"left",vAlign:"bottom"}},{cmdName:"cellalignment",value:{align:"center",vAlign:"bottom"}},{cmdName:"cellalignment",value:{align:"right",vAlign:"bottom"}}]},{group:i.aligntable,icon:"aligntable",subMenu:[{cmdName:"tablealignment",className:"left",label:i.tableleft,value:"left"},{cmdName:"tablealignment",className:"center",label:i.tablecenter,value:"center"},{cmdName:"tablealignment",className:"right",label:i.tableright,value:"right"}]},"-",{label:i.insertparagraphbefore,cmdName:"insertparagraph",value:!0},{label:i.insertparagraphafter,cmdName:"insertparagraph"},{label:i.copy,cmdName:"copy"},{label:i.paste,cmdName:"paste"}];if(n.length){var o=UE.ui.uiUtils;e.addListener("contextmenu",function(r,a){var s=o.getViewportOffsetByEvent(a);e.fireEvent("beforeselectionchange"),t&&t.destroy();for(var l,d=0,c=[];l=n[d];d++){var u;!function(t){if("-"==t)(u=c[c.length-1])&&"-"!==u&&c.push("-");else if(t.hasOwnProperty("group")){for(var n,o=0,r=[];n=t.subMenu[o];o++)!function(t){"-"==t?(u=r[r.length-1])&&"-"!==u?r.push("-"):r.splice(r.length-1):(e.commands[t.cmdName]||UE.commands[t.cmdName]||t.query)&&(t.query?t.query():e.queryCommandState(t.cmdName))>-1&&r.push({label:t.label||e.getLang("contextMenu."+t.cmdName+(t.value||""))||"",className:"edui-for-"+t.cmdName+(t.className?" edui-for-"+t.cmdName+"-"+t.className:""),onclick:t.exec?function(){t.exec.call(e)}:function(){e.execCommand(t.cmdName,t.value)}})}(n);if(r.length){c.push({label:function(){switch(t.icon){case"table":return e.getLang("contextMenu.table");case"justifyjustify":return e.getLang("contextMenu.paragraph");case"aligntd":return e.getLang("contextMenu.aligntd");case"aligntable":return e.getLang("contextMenu.aligntable");case"tablesort":return i.tablesort;case"borderBack":return i.borderbk;default:return""}}(),className:"edui-for-"+t.icon,subMenu:{items:r,editor:e}})}}else(e.commands[t.cmdName]||UE.commands[t.cmdName]||t.query)&&(t.query?t.query.call(e):e.queryCommandState(t.cmdName))>-1&&c.push({label:t.label||e.getLang("contextMenu."+t.cmdName),className:"edui-for-"+(t.icon?t.icon:t.cmdName+(t.value||"")),onclick:t.exec?function(){t.exec.call(e)}:function(){e.execCommand(t.cmdName,t.value)}})}(l)}if("-"==c[c.length-1]&&c.pop(),(t=new UE.ui.Menu({items:c,className:"edui-contextmenu",editor:e})).render(),t.showAt(s),e.fireEvent("aftershowcontextmenu",t),domUtils.preventDefault(a),browser.ie){var m;try{m=e.selection.getNative().createRange()}catch(e){return}if(m.item)new dom.Range(e.document).selectNode(m.item(0)).select(!0,!0)}}),e.addListener("aftershowcontextmenu",function(e,t){})}}}},UE.plugins.shortcutmenu=function(){var e,t=this.options.shortcutMenu||[];t.length&&(this.addListener("contextmenu mouseup",function(i,n){var o=this,r={type:i,target:n.target||n.srcElement,screenX:n.screenX,screenY:n.screenY,clientX:n.clientX,clientY:n.clientY};if(setTimeout(function(){!1!==o.selection.getRange().collapsed&&"contextmenu"!=i||(e||((e=new baidu.editor.ui.ShortCutMenu({editor:o,items:t,theme:o.options.theme,className:"edui-shortcutmenu"})).render(),o.fireEvent("afterrendershortcutmenu",e)),e.show(r,!!UE.plugins.contextmenu))}),"contextmenu"==i&&(domUtils.preventDefault(n),browser.ie9below)){var a;try{a=o.selection.getNative().createRange()}catch(n){return}if(a.item)new dom.Range(o.document).selectNode(a.item(0)).select(!0,!0)}}),this.addListener("keydown",function(t){"keydown"==t&&e&&!e.isHidden&&e.hide()}))},UE.plugins.basestyle=function(){var e={bold:["strong","b"],italic:["em","i"],subscript:["sub"],superscript:["sup"]},t=function(e,t){return domUtils.filterNodeList(e.selection.getStartElementPath(),t)},i=this;for(var n in i.addshortcutkey({Bold:"ctrl+66",Italic:"ctrl+73",Underline:"ctrl+85"}),i.addInputRule(function(e){utils.each(e.getNodesByTagName("b i"),function(e){switch(e.tagName){case"b":e.tagName="strong";break;case"i":e.tagName="em"}})}),e)!function(e,n){i.commands[e]={execCommand:function(e){var o=i.selection.getRange(),r=t(this,n);if(o.collapsed){if(r){var a=i.document.createTextNode("");o.insertNode(a).removeInlineStyle(n),o.setStartBefore(a),domUtils.remove(a)}else{var s=o.document.createElement(n[0]);"superscript"!=e&&"subscript"!=e||(a=i.document.createTextNode(""),o.insertNode(a).removeInlineStyle(["sub","sup"]).setStartBefore(a).collapse(!0)),o.insertNode(s).setStart(s,0)}o.collapse(!0)}else"superscript"!=e&&"subscript"!=e||r&&r.tagName.toLowerCase()==e||o.removeInlineStyle(["sub","sup"]),r?o.removeInlineStyle(n):o.applyInlineStyle(n[0]);o.select()},queryCommandState:function(){return t(this,n)?1:0}}}(n,e[n])},UE.plugins.elementpath=function(){var e,t,i=this;i.setOpt("elementPathEnabled",!0),i.options.elementPathEnabled&&(i.commands.elementpath={execCommand:function(n,o){var r=t[o],a=i.selection.getRange();e=1*o,a.selectNode(r).select()},queryCommandValue:function(){var i=[].concat(this.selection.getStartElementPath()).reverse(),n=[];t=i;for(var o,r=0;o=i[r];r++)if(3!=o.nodeType){var a=o.tagName.toLowerCase();if("img"==a&&o.getAttribute("anchorname")&&(a="anchor"),n[r]=a,e==r){e=-1;break}}return n}})},UE.plugins.formatmatch=function(){var e,t=this,i=[],n=0;function o(r,a){if(browser.webkit)var s="IMG"==a.target.tagName?a.target:null;t.undoManger&&t.undoManger.save();var l=t.selection.getRange(),d=s||l.getClosedNode();if(e&&d&&"IMG"==d.tagName)d.style.cssText+=";float:"+(e.style.cssFloat||e.style.styleFloat||"none")+";display:"+(e.style.display||"inline"),e=null;else if(!e){if(l.collapsed){var c=t.document.createTextNode("match");l.insertNode(c).select()}t.__hasEnterExecCommand=!0;var u=t.options.removeFormatAttributes;t.options.removeFormatAttributes="",t.execCommand("removeformat"),t.options.removeFormatAttributes=u,t.__hasEnterExecCommand=!1,l=t.selection.getRange(),i.length&&function(e){c&&e.selectNode(c),e.applyInlineStyle(i[i.length-1].tagName,null,i)}(l),c&&l.setStartBefore(c).collapse(!0),l.select(),c&&domUtils.remove(c)}t.undoManger&&t.undoManger.save(),t.removeListener("mouseup",o),n=0}t.addListener("reset",function(){i=[],n=0}),t.commands.formatmatch={execCommand:function(r){if(n)return n=0,i=[],void t.removeListener("mouseup",o);var a=t.selection.getRange();if(!(e=a.getClosedNode())||"IMG"!=e.tagName){a.collapse(!0).shrinkBoundary();var s=a.startContainer;i=domUtils.findParents(s,!0,function(e){return!domUtils.isBlockElm(e)&&1==e.nodeType});for(var l,d=0;l=i[d];d++)if("A"==l.tagName){i.splice(d,1);break}}t.addListener("mouseup",o),n=1},queryCommandState:function(){return n},notNeedUndo:1}},UE.plugin.register("searchreplace",function(){var e=this,t={table:1,tbody:1,tr:1,ol:1,ul:1};function i(e,t,i){var n=t.searchStr;-1==t.dir&&(e=e.split("").reverse().join(""),n=n.split("").reverse().join(""),i=e.length-i);for(var o,r=new RegExp(n,"g"+(t.casesensitive?"":"i"));o=r.exec(e);)if(o.index>=i)return-1==t.dir?e.length-o.index-t.searchStr.length:o.index;return-1}function n(e,t,i){for(var o,r=0,a=e.firstChild,s=0;a;){if(3==a.nodeType){if((r+=s=a.nodeValue.replace(/(^[\t\r\n]+)|([\t\r\n]+$)/,"").length)>=t)return{node:a,index:s-(r-t)}}else if(!dtd.$empty[a.tagName]&&(r+=s=a[browser.ie?"innerText":"textContent"].replace(/(^[\t\r\n]+)|([\t\r\n]+$)/,"").length)>=t&&(o=n(a,s-(r-t),i)))return o;a=domUtils.getNextDomNode(a)}}function o(e,o){var a,s=e.selection.getRange(),l=o.searchStr,d=e.document.createElement("span");if(d.innerHTML="$$ueditor_searchreplace_key$$",s.shrinkBoundary(!0),!s.collapsed){s.select();var c=e.selection.getText();if(new RegExp("^"+o.searchStr+"$",o.casesensitive?"":"i").test(c)){if(null!=o.replaceStr)return r(s,o.replaceStr),s.select(),!0;s.collapse(-1==o.dir)}}s.insertNode(d),s.enlargeToBlockElm(!0);var u=(a=s.startContainer)[browser.ie?"innerText":"textContent"].indexOf("$$ueditor_searchreplace_key$$");s.setStartBefore(d),domUtils.remove(d);var m=function(e,n,o){var r,a=o.all||1==o.dir?"getNextDomNode":"getPreDomNode";for(domUtils.isBody(e)&&(e=e.firstChild);e;){if(-1!=(r=i(3==e.nodeType?e.nodeValue:e[browser.ie?"innerText":"textContent"],o,n)))return{node:e,index:r};for(e=domUtils[a](e);e&&t[e.nodeName.toLowerCase()];)e=domUtils[a](e,!0);e&&(n=-1==o.dir?(3==e.nodeType?e.nodeValue:e[browser.ie?"innerText":"textContent"]).length:0)}}(a,u,o);if(m){var f=n(m.node,m.index,l),h=n(m.node,m.index+l.length,l);return s.setStart(f.node,f.index).setEnd(h.node,h.index),void 0!==o.replaceStr&&r(s,o.replaceStr),s.select(),!0}s.setCursor()}function r(t,i){i=e.document.createTextNode(i),t.deleteContents().insertNode(i)}return{commands:{searchreplace:{execCommand:function(t,i){utils.extend(i,{all:!1,casesensitive:!1,dir:1},!0);var n=0;if(i.all){var r=e.selection.getRange(),a=e.body.firstChild;for(a&&1==a.nodeType?(r.setStart(a,0),r.shrinkBoundary(!0)):3==a.nodeType&&r.setStartBefore(a),r.collapse(!0).select(!0),void 0!==i.replaceStr&&e.fireEvent("saveScene");o(this,i);)n++;n&&e.fireEvent("saveScene")}else void 0!==i.replaceStr&&e.fireEvent("saveScene"),o(this,i)&&n++,n&&e.fireEvent("saveScene");return n},notNeedUndo:1}}}}),UE.plugins.customstyle=function(){var e=this;e.setOpt({customstyle:[{tag:"h1",name:"tc",style:"font-size:32px;font-weight:bold;border-bottom:#ccc 2px solid;padding:0 4px 0 0;text-align:center;margin:0 0 20px 0;"},{tag:"h1",name:"tl",style:"font-size:32px;font-weight:bold;border-bottom:#ccc 2px solid;padding:0 4px 0 0;text-align:left;margin:0 0 10px 0;"},{tag:"span",name:"im",style:"font-size:16px;font-style:italic;font-weight:bold;line-height:18px;"},{tag:"span",name:"hi",style:"font-size:16px;font-style:italic;font-weight:bold;color:rgb(51, 153, 204);line-height:18px;"}]}),e.commands.customstyle={execCommand:function(e,t){var i,n,o=this,r=t.tag,a=domUtils.findParent(o.selection.getStart(),function(e){return e.getAttribute("label")},!0),s={};for(var l in t)void 0!==t[l]&&(s[l]=t[l]);if(delete s.tag,a&&a.getAttribute("label")==t.label){if(n=(i=this.selection.getRange()).createBookmark(),i.collapsed)if(dtd.$block[a.tagName]){var d=o.document.createElement("p");domUtils.moveChild(a,d),a.parentNode.insertBefore(d,a),domUtils.remove(a)}else domUtils.remove(a,!0);else{var c=domUtils.getCommonAncestor(n.start,n.end),u=domUtils.getElementsByTagName(c,r);new RegExp(r,"i").test(c.tagName)&&u.push(c);for(var m,f=0;m=u[f++];)if(m.getAttribute("label")==t.label){var h=domUtils.getPosition(m,n.start),p=domUtils.getPosition(m,n.end);if((h&domUtils.POSITION_FOLLOWING||h&domUtils.POSITION_CONTAINS)&&(p&domUtils.POSITION_PRECEDING||p&domUtils.POSITION_CONTAINS)&&dtd.$block[r]){d=o.document.createElement("p");domUtils.moveChild(m,d),m.parentNode.insertBefore(d,m)}domUtils.remove(m,!0)}(a=domUtils.findParent(c,function(e){return e.getAttribute("label")==t.label},!0))&&domUtils.remove(a,!0)}i.moveToBookmark(n).select()}else if(dtd.$block[r]){if(this.execCommand("paragraph",r,s,"customstyle"),!(i=o.selection.getRange()).collapsed){i.collapse(),a=domUtils.findParent(o.selection.getStart(),function(e){return e.getAttribute("label")==t.label},!0);var g=o.document.createElement("p");domUtils.insertAfter(a,g),domUtils.fillNode(o.document,g),i.setStart(g,0).setCursor()}}else{if((i=o.selection.getRange()).collapsed)return a=o.document.createElement(r),domUtils.setAttributes(a,s),void i.insertNode(a).setStart(a,0).setCursor();n=i.createBookmark(),i.applyInlineStyle(r,s).moveToBookmark(n).select()}},queryCommandValue:function(){var e=domUtils.filterNodeList(this.selection.getStartElementPath(),function(e){return e.getAttribute("label")});return e?e.getAttribute("label"):""}},e.addListener("keyup",function(t,i){var n=i.keyCode||i.which;if(32==n||13==n){var o=e.selection.getRange();if(o.collapsed){var r=domUtils.findParent(e.selection.getStart(),function(e){return e.getAttribute("label")},!0);if(r&&dtd.$block[r.tagName]&&domUtils.isEmptyNode(r)){var a=e.document.createElement("p");domUtils.insertAfter(r,a),domUtils.fillNode(e.document,a),domUtils.remove(r),o.setStart(a,0).setCursor()}}}})},UE.plugins.catchremoteimage=function(){var me=this,ajax=UE.ajax;!1!==me.options.catchRemoteImageEnable&&(me.setOpt({catchRemoteImageEnable:!1}),me.addListener("afterpaste",function(){me.fireEvent("catchRemoteImage")}),me.addListener("catchRemoteImage",function(){for(var catcherLocalDomain=me.getOpt("catcherLocalDomain"),catcherActionUrl=me.getActionUrl(me.getOpt("catcherActionName")),catcherUrlPrefix=me.getOpt("catcherUrlPrefix"),catcherFieldName=me.getOpt("catcherFieldName"),remoteImages=[],imgs=domUtils.getElementsByTagName(me.document,"img"),test=function(e,t){if(-1!=e.indexOf(location.host)||/(^\.)|(^\/)/.test(e))return!0;if(t)for(var i,n=0;i=t[n++];)if(-1!==e.indexOf(i))return!0;return!1},i=0,ci;ci=imgs[i++];)if(!ci.getAttribute("word_img")){var src=ci.getAttribute("_src")||ci.src||"";/^(https?|ftp):/i.test(src)&&!test(src,catcherLocalDomain)&&remoteImages.push(src)}function catchremoteimage(e,t){var i=utils.serializeParam(me.queryCommandValue("serverparam"))||"",n=utils.formatUrl(catcherActionUrl+(-1==catcherActionUrl.indexOf("?")?"?":"&")+i),o={method:"POST",dataType:utils.isCrossDomainUrl(n)?"jsonp":"",timeout:6e4,onsuccess:t.success,onerror:t.error};o[catcherFieldName]=e,ajax.request(n,o)}remoteImages.length&&catchremoteimage(remoteImages,{success:function(r){try{var info=void 0!==r.state?r:eval("("+r.responseText+")")}catch(e){return}var i,j,ci,cj,oldSrc,newSrc,list=info.list;for(i=0;ci=imgs[i++];)for(oldSrc=ci.getAttribute("_src")||ci.src||"",j=0;cj=list[j++];)if(oldSrc==cj.source&&"SUCCESS"==cj.state){newSrc=catcherUrlPrefix+cj.url,domUtils.setAttributes(ci,{src:newSrc,_src:newSrc});break}me.fireEvent("catchremotesuccess")},error:function(){me.fireEvent("catchremoteerror")}})}))},UE.plugin.register("snapscreen",function(){var me=this,snapplugin;function getLocation(e){var t,i=document.createElement("a"),n=utils.serializeParam(me.queryCommandValue("serverparam"))||"";return i.href=e,browser.ie&&(i.href=i.href),t=i.search,n&&(t=(t=t+(-1==t.indexOf("?")?"?":"&")+n).replace(/[&]+/gi,"&")),{port:i.port,hostname:i.hostname,path:i.pathname+t||+i.hash}}return{commands:{snapscreen:{execCommand:function(cmd){var url,local,res,lang=me.getLang("snapScreen_plugin");if(!snapplugin){var container=me.container,doc=me.container.ownerDocument||me.container.document;snapplugin=doc.createElement("object");try{snapplugin.type="application/x-pluginbaidusnap"}catch(e){return}snapplugin.style.cssText="position:absolute;left:-9999px;width:0;height:0;",snapplugin.setAttribute("width","0"),snapplugin.setAttribute("height","0"),container.appendChild(snapplugin)}function onSuccess(rs){try{if(rs=eval("("+rs+")"),"SUCCESS"==rs.state){var opt=me.options;me.execCommand("insertimage",{src:opt.snapscreenUrlPrefix+rs.url,_src:opt.snapscreenUrlPrefix+rs.url,alt:rs.title||"",floatStyle:opt.snapscreenImgAlign})}else alert(rs.state)}catch(e){alert(lang.callBackErrorMsg)}}url=me.getActionUrl(me.getOpt("snapscreenActionName")),local=getLocation(url),setTimeout(function(){try{res=snapplugin.saveSnapshot(local.hostname,local.path,local.port)}catch(e){return void me.ui._dialogs.snapscreenDialog.open()}onSuccess(res)},50)},queryCommandState:function(){return-1!=navigator.userAgent.indexOf("Windows",0)?0:-1}}}}}),UE.commands.insertparagraph={execCommand:function(e,t){for(var i,n=this.selection.getRange(),o=n.startContainer;o&&!domUtils.isBody(o);)i=o,o=o.parentNode;if(i){var r=this.document.createElement("p");t?i.parentNode.insertBefore(r,i):i.parentNode.insertBefore(r,i.nextSibling),domUtils.fillNode(this.document,r),n.setStart(r,0).setCursor(!1,!0)}}},UE.plugin.register("webapp",function(){var e=this;function t(t,i){return i?'<iframe class="edui-faked-webapp" title="'+t.title+'" '+(t.align&&!t.cssfloat?'align="'+t.align+'"':"")+(t.cssfloat?'style="float:'+t.cssfloat+'"':"")+'width="'+t.width+'" height="'+t.height+'"  scrolling="no" frameborder="0" src="'+t.url+'" logo_url = "'+t.logo+'"></iframe>':'<img title="'+t.title+'" width="'+t.width+'" height="'+t.height+'" src="'+e.options.UEDITOR_HOME_URL+'themes/default/images/spacer.gif" _logo_url="'+t.logo+'" style="background:url('+t.logo+') no-repeat center center; border:1px solid gray;" class="edui-faked-webapp" _url="'+t.url+'" '+(t.align&&!t.cssfloat?'align="'+t.align+'"':"")+(t.cssfloat?'style="float:'+t.cssfloat+'"':"")+"/>"}return{outputRule:function(e){utils.each(e.getNodesByTagName("img"),function(e){var i;if("edui-faked-webapp"==e.getAttr("class")){i=t({title:e.getAttr("title"),width:e.getAttr("width"),height:e.getAttr("height"),align:e.getAttr("align"),cssfloat:e.getStyle("float"),url:e.getAttr("_url"),logo:e.getAttr("_logo_url")},!0);var n=UE.uNode.createElement(i);e.parentNode.replaceChild(n,e)}})},inputRule:function(e){utils.each(e.getNodesByTagName("iframe"),function(e){if("edui-faked-webapp"==e.getAttr("class")){var i=UE.uNode.createElement(t({title:e.getAttr("title"),width:e.getAttr("width"),height:e.getAttr("height"),align:e.getAttr("align"),cssfloat:e.getStyle("float"),url:e.getAttr("src"),logo:e.getAttr("logo_url")}));e.parentNode.replaceChild(i,e)}})},commands:{webapp:{execCommand:function(e,i){var n=t(utils.extend(i,{align:"none"}),!1);this.execCommand("inserthtml",n)},queryCommandState:function(){var e=this.selection.getRange().getClosedNode();return e&&"edui-faked-webapp"==e.className?1:0}}}}}),UE.plugins.template=function(){UE.commands.template={execCommand:function(e,t){t.html&&this.execCommand("inserthtml",t.html)}},this.addListener("click",function(e,t){var i=t.target||t.srcElement,n=this.selection.getRange(),o=domUtils.findParent(i,function(e){if(e.className&&domUtils.hasClass(e,"ue_t"))return e},!0);o&&n.selectNode(o).shrinkBoundary().select()}),this.addListener("keydown",function(e,t){var i=this.selection.getRange();if(!i.collapsed&&!(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)){var n=domUtils.findParent(i.startContainer,function(e){if(e.className&&domUtils.hasClass(e,"ue_t"))return e},!0);n&&domUtils.removeClasses(n,["ue_t"])}})},UE.plugin.register("music",function(){var e=this;function t(t,i,n,o,r,a){return a?'<embed type="application/x-shockwave-flash" class="edui-faked-music" pluginspage="http://www.macromedia.com/go/getflashplayer" src="'+t+'" width="'+i+'" height="'+n+'" '+(o&&!r?'align="'+o+'"':"")+(r?'style="float:'+r+'"':"")+' wmode="transparent" play="true" loop="false" menu="false" allowscriptaccess="never" allowfullscreen="true" >':"<img "+(o&&!r?'align="'+o+'"':"")+(r?'style="float:'+r+'"':"")+' width="'+i+'" height="'+n+'" _url="'+t+'" class="edui-faked-music" src="'+e.options.langPath+e.options.lang+'/images/music.png" />'}return{outputRule:function(e){utils.each(e.getNodesByTagName("img"),function(e){var i;if("edui-faked-music"==e.getAttr("class")){var n=e.getStyle("float"),o=e.getAttr("align");i=t(e.getAttr("_url"),e.getAttr("width"),e.getAttr("height"),o,n,!0);var r=UE.uNode.createElement(i);e.parentNode.replaceChild(r,e)}})},inputRule:function(e){utils.each(e.getNodesByTagName("embed"),function(e){if("edui-faked-music"==e.getAttr("class")){var i=e.getStyle("float"),n=e.getAttr("align");html=t(e.getAttr("src"),e.getAttr("width"),e.getAttr("height"),n,i,!1);var o=UE.uNode.createElement(html);e.parentNode.replaceChild(o,e)}})},commands:{music:{execCommand:function(e,i){var n=t(i.url,i.width||400,i.height||95,"none",!1);this.execCommand("inserthtml",n)},queryCommandState:function(){var e=this.selection.getRange().getClosedNode();return e&&"edui-faked-music"==e.className?1:0}}}}}),UE.plugin.register("autoupload",function(){function e(e,t){var i,n,o,r,a,s,l,d,c=t,u=/image\/\w+/i.test(e.type)?"image":"file",m="loading_"+(+new Date).toString(36);if(i=c.getOpt(u+"FieldName"),n=c.getOpt(u+"UrlPrefix"),o=c.getOpt(u+"MaxSize"),r=c.getOpt(u+"AllowFiles"),a=c.getActionUrl(c.getOpt(u+"ActionName")),l=function(e){var t=c.document.getElementById(m);t&&domUtils.remove(t),c.fireEvent("showmessage",{id:m,content:e,type:"error",timeout:4e3})},"image"==u?(s='<img class="loadingclass" id="'+m+'" src="'+c.options.themePath+c.options.theme+'/images/spacer.gif" title="'+(c.getLang("autoupload.loading")||"")+'" >',d=function(e){var t=n+e.url,i=c.document.getElementById(m);i&&(i.setAttribute("src",t),i.setAttribute("_src",t),i.setAttribute("title",e.title||""),i.setAttribute("alt",e.original||""),i.removeAttribute("id"),domUtils.removeClasses(i,"loadingclass"))}):(s='<p><img class="loadingclass" id="'+m+'" src="'+c.options.themePath+c.options.theme+'/images/spacer.gif" title="'+(c.getLang("autoupload.loading")||"")+'" ></p>',d=function(e){var t=n+e.url,i=c.document.getElementById(m),o=c.selection.getRange(),r=o.createBookmark();o.selectNode(i).select(),c.execCommand("insertfile",{url:t}),o.moveToBookmark(r).select()}),c.execCommand("inserthtml",s),c.getOpt(u+"ActionName"))if(e.size>o)l(c.getLang("autoupload.exceedSizeError"));else{var f=e.name?e.name.substr(e.name.lastIndexOf(".")):"";if(f&&"image"!=u||r&&-1==(r.join("")+".").indexOf(f.toLowerCase()+"."))l(c.getLang("autoupload.exceedTypeError"));else{var h=new XMLHttpRequest,p=new FormData,g=utils.serializeParam(c.queryCommandValue("serverparam"))||"",v=utils.formatUrl(a+(-1==a.indexOf("?")?"?":"&")+g);p.append(i,e,e.name||"blob."+e.type.substr("image/".length)),p.append("type","ajax"),h.open("post",v,!0),h.setRequestHeader("X-Requested-With","XMLHttpRequest"),h.addEventListener("load",function(e){try{var t=new Function("return "+utils.trim(e.target.response))();"SUCCESS"==t.state&&t.url?d(t):l(t.state)}catch(e){l(c.getLang("autoupload.loadError"))}}),h.send(p)}}else l(c.getLang("autoupload.errorLoadConfig"))}return{outputRule:function(e){utils.each(e.getNodesByTagName("img"),function(e){/\b(loaderrorclass)|(bloaderrorclass)\b/.test(e.getAttr("class"))&&e.parentNode.removeChild(e)}),utils.each(e.getNodesByTagName("p"),function(e){/\bloadpara\b/.test(e.getAttr("class"))&&e.parentNode.removeChild(e)})},bindEvents:{ready:function(t){var i=this;window.FormData&&window.FileReader&&(domUtils.on(i.body,"paste drop",function(t){var n,o=!1;if(n="paste"==t.type?function(e){return e.clipboardData&&e.clipboardData.items&&1==e.clipboardData.items.length&&/^image\//.test(e.clipboardData.items[0].type)?e.clipboardData.items:null}(t):function(e){return e.dataTransfer&&e.dataTransfer.files?e.dataTransfer.files:null}(t)){for(var r,a=n.length;a--;)(r=n[a]).getAsFile&&(r=r.getAsFile()),r&&r.size>0&&(e(r,i),o=!0);o&&t.preventDefault()}}),domUtils.on(i.body,"dragover",function(e){"Files"==e.dataTransfer.types[0]&&e.preventDefault()}),utils.cssRule("loading",".loadingclass{display:inline-block;cursor:default;background: url('"+this.options.themePath+this.options.theme+"/images/loading.gif') no-repeat center center transparent;border:1px solid #cccccc;margin-left:1px;height: 22px;width: 22px;}\n.loaderrorclass{display:inline-block;cursor:default;background: url('"+this.options.themePath+this.options.theme+"/images/loaderror.png') no-repeat center center transparent;border:1px solid #cccccc;margin-right:1px;height: 22px;width: 22px;}",this.document))}}}}),UE.plugin.register("autosave",function(){var e=this,t=new Date,i=20,n=null;function o(o){var r;new Date-t<i||(o.hasContents()?(t=new Date,o._saveFlag=null,r=e.body.innerHTML,!1!==o.fireEvent("beforeautosave",{content:r})&&(e.setPreferences(n,r),o.fireEvent("afterautosave",{content:r}))):n&&e.removePreferences(n))}return{defaultOptions:{saveInterval:0},bindEvents:{ready:function(){var t=null;t=e.key?e.key+"-drafts-data":(e.container.parentNode.id||"ue-common")+"-drafts-data",n=(location.protocol+location.host+location.pathname).replace(/[.:\/]/g,"_")+t},contentchange:function(){n&&(e._saveFlag&&window.clearTimeout(e._saveFlag),e.options.saveInterval>0?e._saveFlag=window.setTimeout(function(){o(e)},e.options.saveInterval):o(e))}},commands:{clearlocaldata:{execCommand:function(t,i){n&&e.getPreferences(n)&&e.removePreferences(n)},notNeedUndo:!0,ignoreContentChange:!0},getlocaldata:{execCommand:function(t,i){return n&&e.getPreferences(n)||""},notNeedUndo:!0,ignoreContentChange:!0},drafts:{execCommand:function(t,i){e.body.innerHTML=e.body.innerHTML+"<p><br></p>",e.focus(!0)},queryCommandState:function(){return n?null===e.getPreferences(n)?-1:0:-1},notNeedUndo:!0,ignoreContentChange:!0}}}}),UE.plugin.register("charts",function(){var e=this;return{bindEvents:{chartserror:function(){}},commands:{charts:{execCommand:function(i,n){var o=domUtils.findParentByTagName(this.selection.getRange().startContainer,"table",!0),r=[],a={};if(!o)return!1;if(!t(o))return e.fireEvent("chartserror"),!1;for(var s in a.title=n.title||"",a.subTitle=n.subTitle||"",a.xTitle=n.xTitle||"",a.yTitle=n.yTitle||"",a.suffix=n.suffix||"",a.tip=n.tip||"",a.dataFormat=n.tableDataFormat||"",a.chartType=n.chartType||0,a)a.hasOwnProperty(s)&&r.push(s+":"+a[s]);o.setAttribute("data-chart",r.join(";")),domUtils.addClass(o,"edui-charts-table")},queryCommandState:function(e,i){var n=domUtils.findParentByTagName(this.selection.getRange().startContainer,"table",!0);return n&&t(n)?0:-1}}},inputRule:function(e){utils.each(e.getNodesByTagName("table"),function(e){void 0!==e.getAttr("data-chart")&&e.setAttr("style")})},outputRule:function(e){utils.each(e.getNodesByTagName("table"),function(e){void 0!==e.getAttr("data-chart")&&e.setAttr("style","display: none;")})}};function t(e){var t,i;if(e.rows.length<2)return!1;if(e.rows[0].cells.length<2)return!1;i=(t=e.rows[0].cells).length;for(var n=0;r=t[n];n++)if("th"!==r.tagName.toLowerCase())return!1;var o;for(n=1;o=e.rows[n];n++){if(o.cells.length!=i)return!1;if("th"!==o.cells[0].tagName.toLowerCase())return!1;for(var r,a=1;r=o.cells[a];a++){var s=utils.trim(r.innerText||r.textContent||"");if(s=s.replace(new RegExp(UE.dom.domUtils.fillChar,"g"),"").replace(/^\s+|\s+$/g,""),!/^\d*\.?\d+$/.test(s))return!1}}return!0}}),UE.plugin.register("section",function(){function e(e){var t=new function(e){this.tag="",this.level=-1,this.dom=null,this.nextSection=null,this.previousSection=null,this.parentSection=null,this.startAddress=[],this.endAddress=[],this.children=[]};return utils.extend(t,e)}function t(e,t){for(var i=t,n=0;n<e.length;n++){if(!i.childNodes)return null;i=i.childNodes[e[n]]}return i}var i=this;return{bindMultiEvents:{type:"aftersetcontent afterscencerestore",handler:function(){i.fireEvent("updateSections")}},bindEvents:{ready:function(){i.fireEvent("updateSections"),domUtils.on(i.body,"drop paste",function(){i.fireEvent("updateSections")})},afterexeccommand:function(e,t){"paragraph"==t&&i.fireEvent("updateSections")},keyup:function(e,t){if(1!=this.selection.getRange().collapsed)this.fireEvent("updateSections");else{var i=t.keyCode||t.which;13!=i&&8!=i&&46!=i||this.fireEvent("updateSections")}}},commands:{getsections:{execCommand:function(t,i){for(var n=i||["h1","h2","h3","h4","h5","h6"],o=0;o<n.length;o++)"string"==typeof n[o]?n[o]=function(e){return function(t){return t.tagName==e.toUpperCase()}}(n[o]):"function"!=typeof n[o]&&(n[o]=function(e){return null});function r(e){for(var t=0;t<n.length;t++)if(n[t](e))return t;return-1}var a=this,s=e({level:-1,title:"root"}),l=s;return function t(i,n){for(var o,s,d,c=null,u=i.childNodes,m=0,f=u.length;m<f;m++)if((o=r(d=u[m]))>=0){var h=a.selection.getRange().selectNode(d).createAddress(!0).startAddress,p=e({tag:d.tagName,title:d.innerText||d.textContent||"",level:o,dom:d,startAddress:utils.clone(h,[]),endAddress:utils.clone(h,[]),children:[]});for(l.nextSection=p,p.previousSection=l,s=l;o<=s.level;)s=s.parentSection;p.parentSection=s,s.children.push(p),c=l=p}else 1===d.nodeType&&t(d,n),c&&c.endAddress[c.endAddress.length-1]++}(a.body,s),s},notNeedUndo:!0},movesection:{execCommand:function(e,i,n,o){var r,a;if(i&&n&&-1!=n.level&&(a=t(r=o?n.endAddress:n.startAddress,this.body),r&&a&&!function(e,t,i){for(var n=!1,o=!1,r=0;r<e.length&&!(r>=i.length);r++){if(i[r]>e[r]){n=!0;break}if(i[r]<e[r])break}for(var r=0;r<t.length&&!(r>=i.length);r++){if(i[r]<e[r]){o=!0;break}if(i[r]>e[r])break}return n&&o}(i.startAddress,i.endAddress,r))){var s,l,d=t(i.startAddress,this.body),c=t(i.endAddress,this.body);if(o)for(s=c;s&&!(domUtils.getPosition(d,s)&domUtils.POSITION_FOLLOWING)&&(l=s.previousSibling,domUtils.insertAfter(a,s),s!=d);)s=l;else for(s=d;s&&!(domUtils.getPosition(s,c)&domUtils.POSITION_FOLLOWING)&&(l=s.nextSibling,a.parentNode.insertBefore(s,a),s!=c);)s=l;this.fireEvent("updateSections")}}},deletesection:{execCommand:function(e,t,i){var n=this;if(t){var o,r=l(t.startAddress),a=l(t.endAddress),s=r;if(i)domUtils.remove(s);else for(;s&&domUtils.inDoc(a,n.document)&&!(domUtils.getPosition(s,a)&domUtils.POSITION_FOLLOWING);)o=s.nextSibling,domUtils.remove(s),s=o;n.fireEvent("updateSections")}function l(e){for(var t=n.body,i=0;i<e.length;i++){if(!t.childNodes)return null;t=t.childNodes[e[i]]}return t}}},selectsection:{execCommand:function(e,t){if(!t&&!t.dom)return!1;var i=this.selection.getRange(),n={startAddress:utils.clone(t.startAddress,[]),endAddress:utils.clone(t.endAddress,[])};return n.endAddress[n.endAddress.length-1]++,i.moveToAddress(n).select().scrollToView(),!0},notNeedUndo:!0},scrolltosection:{execCommand:function(e,t){if(!t&&!t.dom)return!1;var i=this.selection.getRange(),n={startAddress:t.startAddress,endAddress:t.endAddress};return n.endAddress[n.endAddress.length-1]++,i.moveToAddress(n).scrollToView(),!0},notNeedUndo:!0}}}}),UE.plugin.register("simpleupload",function(){var e,t=this,i=!1;function n(){var n=e.offsetWidth||20,o=e.offsetHeight||20,r=document.createElement("iframe"),a=document.createElement("iframe"),s="display:block;width:"+n+"px;height:"+o+"px;overflow:hidden;border:0;margin:0;padding:0;position:absolute;top:0;left:0;filter:alpha(opacity=0);-moz-opacity:0;-khtml-opacity: 0;opacity: 0;cursor:pointer;";domUtils.on(r,"load",function(){var e,l,d,c=(+new Date).toString(36);d=(l=r.contentDocument||r.contentWindow.document).body,(e=l.createElement("div")).innerHTML='<form id="edui_form_'+c+'" target="edui_iframe_'+c+'" method="POST" enctype="multipart/form-data" action="'+t.getOpt("serverUrl")+'" style="'+s+'"><input id="edui_input_'+c+'" type="file" accept="image/*" name="'+t.options.imageFieldName+'" style="'+s+'"></form>',a.id="edui_iframe_"+c,a.name="edui_iframe_"+c,e.className="edui-"+t.options.theme,e.id=t.ui.id+"_iframeupload",d.style.cssText=s,d.style.width=n+"px",d.style.height=o+"px",d.appendChild(e),d.parentNode&&(d.parentNode.style.width=n+"px",d.parentNode.style.height=n+"px");var u,m=l.getElementById("edui_form_"+c),f=l.getElementById("edui_input_"+c),h=a;domUtils.on(f,"change",function(){if(f.value){var e="loading_"+(+new Date).toString(36),i=utils.serializeParam(t.queryCommandValue("serverparam"))||"",n=t.getActionUrl(t.getOpt("imageActionName")),o=t.getOpt("imageAllowFiles");if(t.focus(),t.execCommand("inserthtml",'<img class="loadingclass" id="'+e+'" src="'+t.options.themePath+t.options.theme+'/images/spacer.gif" title="'+(t.getLang("simpleupload.loading")||"")+'" >'),t.getOpt("imageActionName")){var r=f.value,a=r?r.substr(r.lastIndexOf(".")):"";!a||o&&-1==(o.join("")+".").indexOf(a.toLowerCase()+".")?s(t.getLang("simpleupload.exceedTypeError")):(domUtils.on(h,"load",function i(){try{var n,o,r,a=(h.contentDocument||h.contentWindow.document).body,l=a.innerText||a.textContent||"";o=new Function("return "+l)(),n=t.options.imageUrlPrefix+o.url,"SUCCESS"==o.state&&o.url?((r=t.document.getElementById(e)).setAttribute("src",n),r.setAttribute("_src",n),r.setAttribute("title",o.title||""),r.setAttribute("alt",o.original||""),r.removeAttribute("id"),domUtils.removeClasses(r,"loadingclass")):s&&s(o.state)}catch(e){s&&s(t.getLang("simpleupload.loadError"))}m.reset(),domUtils.un(h,"load",i)}),m.action=utils.formatUrl(n+(-1==n.indexOf("?")?"?":"&")+i),m.submit())}else errorHandler(t.getLang("autoupload.errorLoadConfig"))}function s(i){if(e){var n=t.document.getElementById(e);n&&domUtils.remove(n),t.fireEvent("showmessage",{id:e,content:i,type:"error",timeout:4e3})}}}),t.addListener("selectionchange",function(){clearTimeout(u),u=setTimeout(function(){var e=t.queryCommandState("simpleupload");f.disabled=-1==e&&"disabled"},400)}),i=!0}),r.style.cssText=s,e.appendChild(r),a.style.cssText="display:none;width:0;height:0;border:0;margin:0;padding:0;position:absolute;",e.appendChild(a)}return{bindEvents:{ready:function(){utils.cssRule("loading",".loadingclass{display:inline-block;cursor:default;background: url('"+this.options.themePath+this.options.theme+"/images/loading.gif') no-repeat center center transparent;border:1px solid #cccccc;margin-right:1px;height: 22px;width: 22px;}\n.loaderrorclass{display:inline-block;cursor:default;background: url('"+this.options.themePath+this.options.theme+"/images/loaderror.png') no-repeat center center transparent;border:1px solid #cccccc;margin-right:1px;height: 22px;width: 22px;}",this.document)},simpleuploadbtnready:function(i,o){e=o,t.afterConfigReady(n)}},outputRule:function(e){utils.each(e.getNodesByTagName("img"),function(e){/\b(loaderrorclass)|(bloaderrorclass)\b/.test(e.getAttr("class"))&&e.parentNode.removeChild(e)})},commands:{simpleupload:{queryCommandState:function(){return i?0:-1}}}}}),UE.plugin.register("serverparam",function(){var e={};return{commands:{serverparam:{execCommand:function(t,i,n){null==i?e={}:utils.isString(i)?null==n?delete e[i]:e[i]=n:utils.isObject(i)?utils.extend(e,i,!0):utils.isFunction(i)&&utils.extend(e,i(),!0)},queryCommandValue:function(){return e||{}}}}}}),UE.plugin.register("insertfile",function(){var e=this;function t(e){var t=e.indexOf("?");t>-1&&(e=e.substring(0,t));var i=e.substr(e.lastIndexOf(".")+1).toLowerCase(),n={rar:"icon_rar.gif",zip:"icon_rar.gif",tar:"icon_rar.gif",gz:"icon_rar.gif",bz2:"icon_rar.gif",doc:"icon_doc.gif",docx:"icon_doc.gif",pdf:"icon_pdf.gif",mp3:"icon_mp3.gif",xls:"icon_xls.gif",chm:"icon_chm.gif",ppt:"icon_ppt.gif",pptx:"icon_ppt.gif",avi:"icon_mv.gif",rmvb:"icon_mv.gif",mp4:"icon_mv.gif",wmv:"icon_mv.gif",flv:"icon_mv.gif",swf:"icon_mv.gif",rm:"icon_mv.gif",exe:"icon_exe.gif",psd:"icon_psd.gif",txt:"icon_txt.gif",jpg:"icon_jpg.gif",png:"icon_jpg.gif",jpeg:"icon_jpg.gif",gif:"icon_jpg.gif",ico:"icon_jpg.gif",bmp:"icon_jpg.gif"};return n[i]?n[i]:n.txt}return{commands:{insertfile:{execCommand:function(i,n){n=utils.isArray(n)?n:[n];var o,r,a,s,l="",d=e.getOpt("UEDITOR_HOME_URL"),c=d+("/"==d.substr(d.length-1)?"":"/")+"dialogs/attachment/fileTypeImages/";for(o=0;o<n.length;o++)a=c+t((r=n[o]).url),s=r.title||r.url.substr(r.url.lastIndexOf("/")+1),name=r.name||r.url.substr(r.url.lastIndexOf("/")+1),l+='<p style="line-height: 16px;"><img style="vertical-align: middle; margin-right: 2px;" src="'+a+'" _src="'+a+'" /><a style="font-size:12px; color:#0066cc;" href="'+r.url+'" title="'+s+'">'+name+"</a></p>";e.execCommand("insertHtml",l)}}}}}),UE.plugins.xssFilter=function(){var e,t=UEDITOR_CONFIG,i=t.whitList;function n(e){var t=e.tagName,n=e.attrs;if(!i.hasOwnProperty(t))return e.parentNode.removeChild(e),!1;UE.utils.each(n,function(n,o){-1===i[t].indexOf(o)&&e.setAttr(o)})}i&&t.xssFilterRules&&(this.options.filterRules=(e={},UE.utils.each(i,function(t,i){e[i]=function(e){return n(e)}}),e));var o=[];UE.utils.each(i,function(e,t){o.push(t)}),i&&t.inputXssFilter&&this.addInputRule(function(e){e.traversal(function(e){if("element"!==e.type)return!1;n(e)})}),i&&t.outputXssFilter&&this.addOutputRule(function(e){e.traversal(function(e){if("element"!==e.type)return!1;n(e)})})};var baidu=baidu||{};baidu.editor=baidu.editor||{},UE.ui=baidu.editor.ui={},function(){var e=baidu.editor.browser,t=baidu.editor.dom.domUtils,i=window.$EDITORUI={},n=0,o=baidu.editor.ui.uiUtils={uid:function(e){return e?e.ID$EDITORUI||(e.ID$EDITORUI=++n):++n},hook:function(e,t){var i;return e&&e._callbacks?i=e:(i=function(){var t;e&&(t=e.apply(this,arguments));for(var n=i._callbacks,o=n.length;o--;){var r=n[o].apply(this,arguments);void 0===t&&(t=r)}return t})._callbacks=[],i._callbacks.push(t),i},createElementByHtml:function(e){var t=document.createElement("div");return t.innerHTML=e,(t=t.firstChild).parentNode.removeChild(t),t},getViewportElement:function(){return e.ie&&e.quirks?document.body:document.documentElement},getClientRect:function(e){var i;try{i=e.getBoundingClientRect()}catch(e){i={left:0,top:0,height:0,width:0}}for(var n,o={left:Math.round(i.left),top:Math.round(i.top),height:Math.round(i.bottom-i.top),width:Math.round(i.right-i.left)};(n=e.ownerDocument)!==document&&(e=t.getWindow(n).frameElement);)i=e.getBoundingClientRect(),o.left+=i.left,o.top+=i.top;return o.bottom=o.top+o.height,o.right=o.left+o.width,o},getViewportRect:function(){var e=o.getViewportElement(),t=0|(window.innerWidth||e.clientWidth),i=0|(window.innerHeight||e.clientHeight);return{left:0,top:0,height:i,width:t,bottom:i,right:t}},setViewportOffset:function(e,i){var n=o.getFixedLayer();e.parentNode===n?(e.style.left=i.left+"px",e.style.top=i.top+"px"):t.setViewportOffset(e,i)},getEventOffset:function(e){var t=e.target||e.srcElement,i=o.getClientRect(t),n=o.getViewportOffsetByEvent(e);return{left:n.left-i.left,top:n.top-i.top}},getViewportOffsetByEvent:function(e){var i=e.target||e.srcElement,n=t.getWindow(i).frameElement,r={left:e.clientX,top:e.clientY};if(n&&i.ownerDocument!==document){var a=o.getClientRect(n);r.left+=a.left,r.top+=a.top}return r},setGlobal:function(e,t){return i[e]=t,'$EDITORUI["'+e+'"]'},unsetGlobal:function(e){delete i[e]},copyAttributes:function(i,n){for(var o=n.attributes,r=o.length;r--;){var a=o[r];"style"==a.nodeName||"class"==a.nodeName||e.ie&&!a.specified||i.setAttribute(a.nodeName,a.nodeValue)}n.className&&t.addClass(i,n.className),n.style.cssText&&(i.style.cssText+=";"+n.style.cssText)},removeStyle:function(e,t){if(e.style.removeProperty)e.style.removeProperty(t);else{if(!e.style.removeAttribute)throw"";e.style.removeAttribute(t)}},contains:function(e,t){return e&&t&&e!==t&&(e.contains?e.contains(t):16&e.compareDocumentPosition(t))},startDrag:function(e,t,i){i=i||document;var n=e.clientX,o=e.clientY;function r(e){var i=e.clientX-n,r=e.clientY-o;t.ondragmove(i,r,e),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}if(i.addEventListener){function a(e){i.removeEventListener("mousemove",r,!0),i.removeEventListener("mouseup",a,!0),window.removeEventListener("mouseup",a,!0),t.ondragstop()}i.addEventListener("mousemove",r,!0),i.addEventListener("mouseup",a,!0),window.addEventListener("mouseup",a,!0),e.preventDefault()}else{var s=e.srcElement;function l(){s.releaseCapture(),s.detachEvent("onmousemove",r),s.detachEvent("onmouseup",l),s.detachEvent("onlosecaptrue",l),t.ondragstop()}s.setCapture(),s.attachEvent("onmousemove",r),s.attachEvent("onmouseup",l),s.attachEvent("onlosecaptrue",l),e.returnValue=!1}t.ondragstart()},getFixedLayer:function(){var i=document.getElementById("edui_fixedlayer");return null==i&&((i=document.createElement("div")).id="edui_fixedlayer",document.body.appendChild(i),e.ie&&e.version<=8?(i.style.position="absolute",t.on(window,"scroll",r),t.on(window,"resize",baidu.editor.utils.defer(r,0,!0)),setTimeout(r)):i.style.position="fixed",i.style.left="0",i.style.top="0",i.style.width="0",i.style.height="0"),i},makeUnselectable:function(t){if(e.opera||e.ie&&e.version<9){if(t.unselectable="on",t.hasChildNodes())for(var i=0;i<t.childNodes.length;i++)1==t.childNodes[i].nodeType&&o.makeUnselectable(t.childNodes[i])}else void 0!==t.style.MozUserSelect?t.style.MozUserSelect="none":void 0!==t.style.WebkitUserSelect?t.style.WebkitUserSelect="none":void 0!==t.style.KhtmlUserSelect&&(t.style.KhtmlUserSelect="none")}};function r(){var e=document.getElementById("edui_fixedlayer");o.setViewportOffset(e,{left:0,top:0})}}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.uiUtils,i=baidu.editor.EventBase,n=baidu.editor.ui.UIBase=function(){};n.prototype={className:"",uiName:"",initOptions:function(e){for(var i in e)this[i]=e[i];this.id=this.id||"edui"+t.uid()},initUIBase:function(){this._globalKey=e.unhtml(t.setGlobal(this.id,this))},render:function(e){for(var i,n=this.renderHtml(),o=t.createElementByHtml(n),r=domUtils.getElementsByTagName(o,"*"),a="edui-"+(this.theme||this.editor.options.theme),s=document.getElementById("edui_fixedlayer"),l=0;i=r[l++];)domUtils.addClass(i,a);domUtils.addClass(o,a),s&&(s.className="",domUtils.addClass(s,a));var d=this.getDom();null!=d?(d.parentNode.replaceChild(o,d),t.copyAttributes(o,d)):("string"==typeof e&&(e=document.getElementById(e)),e=e||t.getFixedLayer(),domUtils.addClass(e,a),e.appendChild(o)),this.postRender()},getDom:function(e){return e?document.getElementById(this.id+"_"+e):document.getElementById(this.id)},postRender:function(){this.fireEvent("postrender")},getHtmlTpl:function(){return""},formatHtml:function(e){var t="edui-"+this.uiName;return e.replace(/##/g,this.id).replace(/%%-/g,this.uiName?t+"-":"").replace(/%%/g,(this.uiName?t:"")+" "+this.className).replace(/\$\$/g,this._globalKey)},getUid:function(){return this.id},renderHtml:function(){return this.formatHtml(this.getHtmlTpl())},dispose:function(){var e=this.getDom();e&&baidu.editor.dom.domUtils.remove(e),t.unsetGlobal(this.id)}},e.inherits(n,i)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.UIBase,i=baidu.editor.ui.Separator=function(e){this.initOptions(e),this.initSeparator()};i.prototype={uiName:"separator",initSeparator:function(){this.initUIBase()},getHtmlTpl:function(){return'<div id="##" class="edui-box %%"></div>'}},e.inherits(i,t)}(),function(){var e=baidu.editor.utils,t=baidu.editor.dom.domUtils,i=baidu.editor.ui.UIBase,n=baidu.editor.ui.uiUtils,o=baidu.editor.ui.Mask=function(e){this.initOptions(e),this.initUIBase()};o.prototype={getHtmlTpl:function(){return'<div id="##" class="edui-mask %%" onclick="return $$._onClick(event, this);" onmousedown="return $$._onMouseDown(event, this);"></div>'},postRender:function(){var e=this;t.on(window,"resize",function(){setTimeout(function(){e.isHidden()||e._fill()})})},show:function(e){this._fill(),this.getDom().style.display="",this.getDom().style.zIndex=e},hide:function(){this.getDom().style.display="none",this.getDom().style.zIndex=""},isHidden:function(){return"none"==this.getDom().style.display},_onMouseDown:function(){return!1},_onClick:function(e,t){this.fireEvent("click",e,t)},_fill:function(){var e=this.getDom(),t=n.getViewportRect();e.style.width=t.width+"px",e.style.height=t.height+"px"}},e.inherits(o,i)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.uiUtils,i=baidu.editor.dom.domUtils,n=baidu.editor.ui.UIBase,o=baidu.editor.ui.Popup=function(e){this.initOptions(e),this.initPopup()},r=[];function a(e,t){for(var i=0;i<r.length;i++){var n=r[i];if(!n.isHidden()&&!1!==n.queryAutoHide(t)){if(e&&/scroll/gi.test(e.type)&&"edui-wordpastepop"==n.className)return;n.hide()}}r.length&&n.editor.fireEvent("afterhidepop")}o.postHide=a;var s=["edui-anchor-topleft","edui-anchor-topright","edui-anchor-bottomleft","edui-anchor-bottomright"];o.prototype={SHADOW_RADIUS:5,content:null,_hidden:!1,autoRender:!0,canSideLeft:!0,canSideUp:!0,initPopup:function(){this.initUIBase(),r.push(this)},getHtmlTpl:function(){return'<div id="##" class="edui-popup %%" onmousedown="return false;"> <div id="##_body" class="edui-popup-body"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank"></iframe> <div class="edui-shadow"></div> <div id="##_content" class="edui-popup-content">'+this.getContentHtmlTpl()+"  </div> </div></div>"},getContentHtmlTpl:function(){return this.content?"string"==typeof this.content?this.content:this.content.renderHtml():""},_UIBase_postRender:n.prototype.postRender,postRender:function(){if(this.content instanceof n&&this.content.postRender(),this.captureWheel&&!this.captured){this.captured=!0;var e=(document.documentElement.clientHeight||document.body.clientHeight)-80,o=this.getDom().offsetHeight,r=t.getClientRect(this.combox.getDom()).top,a=this.getDom("content"),s=this.getDom("body").getElementsByTagName("iframe"),l=this;for(s.length&&(s=s[0]);r+o>e;)o-=30;a.style.height=o+"px",s&&(s.style.height=o+"px"),window.XMLHttpRequest?i.on(a,"onmousewheel"in document.body?"mousewheel":"DOMMouseScroll",function(e){e.preventDefault?e.preventDefault():e.returnValue=!1,e.wheelDelta?a.scrollTop-=e.wheelDelta/120*60:a.scrollTop-=e.detail/-3*60}):i.on(this.getDom(),"mousewheel",function(e){e.returnValue=!1,l.getDom("content").scrollTop-=e.wheelDelta/120*60})}this.fireEvent("postRenderAfter"),this.hide(!0),this._UIBase_postRender()},_doAutoRender:function(){!this.getDom()&&this.autoRender&&this.render()},mesureSize:function(){var e=this.getDom("content");return t.getClientRect(e)},fitSize:function(){if(this.captureWheel&&this.sized)return this.__size;this.sized=!0;var e=this.getDom("body");e.style.width="",e.style.height="";var t=this.mesureSize();if(this.captureWheel){e.style.width=-(-20-t.width)+"px";var i=parseInt(this.getDom("content").style.height,10);!window.isNaN(i)&&(t.height=i)}else e.style.width=t.width+"px";return e.style.height=t.height+"px",this.__size=t,this.captureWheel&&(this.getDom("content").style.overflow="auto"),t},showAnchor:function(e,i){this.showAnchorRect(t.getClientRect(e),i)},showAnchorRect:function(e,n,o){this._doAutoRender();var r=t.getViewportRect();this.getDom().style.visibility="hidden",this._show();var a,l,d,c,u=this.fitSize();n?(a=this.canSideLeft&&e.right+u.width>r.right&&e.left>u.width,l=this.canSideUp&&e.top+u.height>r.bottom&&e.bottom>u.height,d=a?e.left-u.width:e.right,c=l?e.bottom-u.height:e.top):(a=this.canSideLeft&&e.right+u.width>r.right&&e.left>u.width,l=this.canSideUp&&e.top+u.height>r.bottom&&e.bottom>u.height,d=a?e.right-u.width:e.left,c=l?e.top-u.height:e.bottom);var m=this.getDom();t.setViewportOffset(m,{left:d,top:c}),i.removeClasses(m,s),m.className+=" "+s[2*(l?1:0)+(a?1:0)],this.editor&&(m.style.zIndex=1*this.editor.container.style.zIndex+10,baidu.editor.ui.uiUtils.getFixedLayer().style.zIndex=m.style.zIndex-1),this.getDom().style.visibility="visible"},showAt:function(e){var t=e.left,i=e.top,n={left:t,top:i,right:t,bottom:i,height:0,width:0};this.showAnchorRect(n,!1,!0)},_show:function(){this._hidden&&(this.getDom().style.display="",this._hidden=!1,this.fireEvent("show"))},isHidden:function(){return this._hidden},show:function(){this._doAutoRender(),this._show()},hide:function(e){!this._hidden&&this.getDom()&&(this.getDom().style.display="none",this._hidden=!0,e||this.fireEvent("hide"))},queryAutoHide:function(e){return!e||!t.contains(this.getDom(),e)}},e.inherits(o,n),i.on(document,"mousedown",function(e){a(e,e.target||e.srcElement)}),i.on(window,"scroll",function(e,t){a(e,t)})}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.UIBase,i=baidu.editor.ui.ColorPicker=function(e){this.initOptions(e),this.noColorText=this.noColorText||this.editor.getLang("clearColor"),this.initUIBase()};i.prototype={getHtmlTpl:function(){return function(e,t){for(var i='<div id="##" class="edui-colorpicker %%"><div class="edui-colorpicker-topbar edui-clearfix"><div unselectable="on" id="##_preview" class="edui-colorpicker-preview"></div><div unselectable="on" class="edui-colorpicker-nocolor" onclick="$$._onPickNoColor(event, this);">'+e+'</div></div><table  class="edui-box" style="border-collapse: collapse;" onmouseover="$$._onTableOver(event, this);" onmouseout="$$._onTableOut(event, this);" onclick="return $$._onTableClick(event, this);" cellspacing="0" cellpadding="0"><tr style="border-bottom: 1px solid #ddd;font-size: 13px;line-height: 25px;color:#39C;padding-top: 2px"><td colspan="10">'+t.getLang("themeColor")+'</td> </tr><tr class="edui-colorpicker-tablefirstrow" >',o=0;o<n.length;o++)o&&o%10==0&&(i+="</tr>"+(60==o?'<tr style="border-bottom: 1px solid #ddd;font-size: 13px;line-height: 25px;color:#39C;"><td colspan="10">'+t.getLang("standardColor")+"</td></tr>":"")+"<tr"+(60==o?' class="edui-colorpicker-tablefirstrow"':"")+">"),i+=o<70?'<td style="padding: 0 2px;"><a hidefocus title="'+n[o]+'" onclick="return false;" href="javascript:" unselectable="on" class="edui-box edui-colorpicker-colorcell" data-color="#'+n[o]+'" style="background-color:#'+n[o]+";border:solid #ccc;"+(o<10||o>=60?"border-width:1px;":o>=10&&o<20?"border-width:1px 1px 0 1px;":"border-width:0 1px 0 1px;")+'"></a></td>':"";return i+="</tr></table></div>"}(this.noColorText,this.editor)},_onTableClick:function(e){var t=(e.target||e.srcElement).getAttribute("data-color");t&&this.fireEvent("pickcolor",t)},_onTableOver:function(e){var t=(e.target||e.srcElement).getAttribute("data-color");t&&(this.getDom("preview").style.backgroundColor=t)},_onTableOut:function(){this.getDom("preview").style.backgroundColor=""},_onPickNoColor:function(){this.fireEvent("picknocolor")}},e.inherits(i,t);var n="ffffff,000000,eeece1,1f497d,4f81bd,c0504d,9bbb59,8064a2,4bacc6,f79646,f2f2f2,7f7f7f,ddd9c3,c6d9f0,dbe5f1,f2dcdb,ebf1dd,e5e0ec,dbeef3,fdeada,d8d8d8,595959,c4bd97,8db3e2,b8cce4,e5b9b7,d7e3bc,ccc1d9,b7dde8,fbd5b5,bfbfbf,3f3f3f,938953,548dd4,95b3d7,d99694,c3d69b,b2a2c7,92cddc,fac08f,a5a5a5,262626,494429,17365d,366092,953734,76923c,5f497a,31859b,e36c09,7f7f7f,0c0c0c,1d1b10,0f243e,244061,632423,4f6128,3f3151,205867,974806,c00000,ff0000,ffc000,ffff00,92d050,00b050,00b0f0,0070c0,002060,7030a0,".split(",")}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.uiUtils,i=baidu.editor.ui.UIBase,n=baidu.editor.ui.TablePicker=function(e){this.initOptions(e),this.initTablePicker()};n.prototype={defaultNumRows:10,defaultNumCols:10,maxNumRows:20,maxNumCols:20,numRows:10,numCols:10,lengthOfCellSide:22,initTablePicker:function(){this.initUIBase()},getHtmlTpl:function(){return'<div id="##" class="edui-tablepicker %%"><div class="edui-tablepicker-body"><div class="edui-infoarea"><span id="##_label" class="edui-label"></span></div><div class="edui-pickarea" onmousemove="$$._onMouseMove(event, this);" onmouseover="$$._onMouseOver(event, this);" onmouseout="$$._onMouseOut(event, this);" onclick="$$._onClick(event, this);"><div id="##_overlay" class="edui-overlay"></div></div></div></div>'},_UIBase_render:i.prototype.render,render:function(e){this._UIBase_render(e),this.getDom("label").innerHTML="0"+this.editor.getLang("t_row")+" x 0"+this.editor.getLang("t_col")},_track:function(e,t){var i=this.getDom("overlay").style,n=this.lengthOfCellSide;i.width=e*n+"px",i.height=t*n+"px",this.getDom("label").innerHTML=e+this.editor.getLang("t_col")+" x "+t+this.editor.getLang("t_row"),this.numCols=e,this.numRows=t},_onMouseOver:function(e,i){var n=e.relatedTarget||e.fromElement;t.contains(i,n)||i===n||(this.getDom("label").innerHTML="0"+this.editor.getLang("t_col")+" x 0"+this.editor.getLang("t_row"),this.getDom("overlay").style.visibility="")},_onMouseOut:function(e,i){var n=e.relatedTarget||e.toElement;t.contains(i,n)||i===n||(this.getDom("label").innerHTML="0"+this.editor.getLang("t_col")+" x 0"+this.editor.getLang("t_row"),this.getDom("overlay").style.visibility="hidden")},_onMouseMove:function(e,i){this.getDom("overlay").style;var n=t.getEventOffset(e),o=this.lengthOfCellSide,r=Math.ceil(n.left/o),a=Math.ceil(n.top/o);this._track(r,a)},_onClick:function(){this.fireEvent("picktable",this.numCols,this.numRows)}},e.inherits(n,i)}(),function(){var e=baidu.editor.browser,t=baidu.editor.dom.domUtils,i=baidu.editor.ui.uiUtils,n='onmousedown="$$.Stateful_onMouseDown(event, this);" onmouseup="$$.Stateful_onMouseUp(event, this);"'+(e.ie?' onmouseenter="$$.Stateful_onMouseEnter(event, this);" onmouseleave="$$.Stateful_onMouseLeave(event, this);"':' onmouseover="$$.Stateful_onMouseOver(event, this);" onmouseout="$$.Stateful_onMouseOut(event, this);"');baidu.editor.ui.Stateful={alwalysHoverable:!1,target:null,Stateful_init:function(){this._Stateful_dGetHtmlTpl=this.getHtmlTpl,this.getHtmlTpl=this.Stateful_getHtmlTpl},Stateful_getHtmlTpl:function(){return this._Stateful_dGetHtmlTpl().replace(/stateful/g,function(){return n})},Stateful_onMouseEnter:function(e,t){this.target=t,this.isDisabled()&&!this.alwalysHoverable||(this.addState("hover"),this.fireEvent("over"))},Stateful_onMouseLeave:function(e,t){this.isDisabled()&&!this.alwalysHoverable||(this.removeState("hover"),this.removeState("active"),this.fireEvent("out"))},Stateful_onMouseOver:function(e,t){var n=e.relatedTarget;i.contains(t,n)||t===n||this.Stateful_onMouseEnter(e,t)},Stateful_onMouseOut:function(e,t){var n=e.relatedTarget;i.contains(t,n)||t===n||this.Stateful_onMouseLeave(e,t)},Stateful_onMouseDown:function(e,t){this.isDisabled()||this.addState("active")},Stateful_onMouseUp:function(e,t){this.isDisabled()||this.removeState("active")},Stateful_postRender:function(){this.disabled&&!this.hasState("disabled")&&this.addState("disabled")},hasState:function(e){return t.hasClass(this.getStateDom(),"edui-state-"+e)},addState:function(e){this.hasState(e)||(this.getStateDom().className+=" edui-state-"+e)},removeState:function(e){this.hasState(e)&&t.removeClasses(this.getStateDom(),["edui-state-"+e])},getStateDom:function(){return this.getDom("state")},isChecked:function(){return this.hasState("checked")},setChecked:function(e){!this.isDisabled()&&e?this.addState("checked"):this.removeState("checked")},isDisabled:function(){return this.hasState("disabled")},setDisabled:function(e){e?(this.removeState("hover"),this.removeState("checked"),this.removeState("active"),this.addState("disabled")):this.removeState("disabled")}}}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.UIBase,i=baidu.editor.ui.Stateful,n=baidu.editor.ui.Button=function(e){if(e.name){var t=e.name,i=e.cssRules;e.className||(e.className="edui-for-"+t),e.cssRules=".edui-default  .edui-for-"+t+" .edui-icon {"+i+"}"}this.initOptions(e),this.initButton()};n.prototype={uiName:"button",label:"",title:"",showIcon:!0,showText:!0,cssRules:"",initButton:function(){this.initUIBase(),this.Stateful_init(),this.cssRules&&e.cssRule("edui-customize-"+this.name+"-style",this.cssRules)},getHtmlTpl:function(){return'<div id="##" class="edui-box %%"><div id="##_state" stateful><div class="%%-wrap"><div id="##_body" unselectable="on" '+(this.title?'title="'+this.title+'"':"")+' class="%%-body" onmousedown="return $$._onMouseDown(event, this);" onclick="return $$._onClick(event, this);">'+(this.showIcon?'<div class="edui-box edui-icon"></div>':"")+(this.showText?'<div class="edui-box edui-label">'+this.label+"</div>":"")+"</div></div></div></div>"},postRender:function(){this.Stateful_postRender(),this.setDisabled(this.disabled)},_onMouseDown:function(e){var t=e.target||e.srcElement,i=t&&t.tagName&&t.tagName.toLowerCase();if("input"==i||"object"==i||"object"==i)return!1},_onClick:function(){this.isDisabled()||this.fireEvent("click")},setTitle:function(e){this.getDom("label").innerHTML=e}},e.inherits(n,t),e.extend(n.prototype,i)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.uiUtils,i=(baidu.editor.dom.domUtils,baidu.editor.ui.UIBase),n=baidu.editor.ui.Stateful,o=baidu.editor.ui.SplitButton=function(e){this.initOptions(e),this.initSplitButton()};o.prototype={popup:null,uiName:"splitbutton",title:"",initSplitButton:function(){this.initUIBase(),this.Stateful_init();if(null!=this.popup){var e=this.popup;this.popup=null,this.setPopup(e)}},_UIBase_postRender:i.prototype.postRender,postRender:function(){this.Stateful_postRender(),this._UIBase_postRender()},setPopup:function(i){this.popup!==i&&(null!=this.popup&&this.popup.dispose(),i.addListener("show",e.bind(this._onPopupShow,this)),i.addListener("hide",e.bind(this._onPopupHide,this)),i.addListener("postrender",e.bind(function(){i.getDom("body").appendChild(t.createElementByHtml('<div id="'+this.popup.id+'_bordereraser" class="edui-bordereraser edui-background" style="width:'+(t.getClientRect(this.getDom()).width+20)+'px"></div>')),i.getDom().className+=" "+this.className},this)),this.popup=i)},_onPopupShow:function(){this.addState("opened")},_onPopupHide:function(){this.removeState("opened")},getHtmlTpl:function(){return'<div id="##" class="edui-box %%"><div '+(this.title?'title="'+this.title+'"':"")+' id="##_state" stateful><div class="%%-body"><div id="##_button_body" class="edui-box edui-button-body" onclick="$$._onButtonClick(event, this);"><div class="edui-box edui-icon"></div></div><div class="edui-box edui-splitborder"></div><div class="edui-box edui-arrow" onclick="$$._onArrowClick();"></div></div></div></div>'},showPopup:function(){var e=t.getClientRect(this.getDom());e.top-=this.popup.SHADOW_RADIUS,e.height+=this.popup.SHADOW_RADIUS,this.popup.showAnchorRect(e)},_onArrowClick:function(e,t){this.isDisabled()||this.showPopup()},_onButtonClick:function(){this.isDisabled()||this.fireEvent("buttonclick")}},e.inherits(o,i),e.extend(o.prototype,n,!0)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.uiUtils,i=baidu.editor.ui.ColorPicker,n=baidu.editor.ui.Popup,o=baidu.editor.ui.SplitButton,r=baidu.editor.ui.ColorButton=function(e){this.initOptions(e),this.initColorButton()};r.prototype={initColorButton:function(){var e=this;this.popup=new n({content:new i({noColorText:e.editor.getLang("clearColor"),editor:e.editor,onpickcolor:function(t,i){e._onPickColor(i)},onpicknocolor:function(t,i){e._onPickNoColor(i)}}),editor:e.editor}),this.initSplitButton()},_SplitButton_postRender:o.prototype.postRender,postRender:function(){this._SplitButton_postRender(),this.getDom("button_body").appendChild(t.createElementByHtml('<div id="'+this.id+'_colorlump" class="edui-colorlump"></div>')),this.getDom().className+=" edui-colorbutton"},setColor:function(e){this.getDom("colorlump").style.backgroundColor=e,this.color=e},_onPickColor:function(e){!1!==this.fireEvent("pickcolor",e)&&(this.setColor(e),this.popup.hide())},_onPickNoColor:function(e){!1!==this.fireEvent("picknocolor")&&this.popup.hide()}},e.inherits(r,o)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.Popup,i=baidu.editor.ui.TablePicker,n=baidu.editor.ui.SplitButton,o=baidu.editor.ui.TableButton=function(e){this.initOptions(e),this.initTableButton()};o.prototype={initTableButton:function(){var e=this;this.popup=new t({content:new i({editor:e.editor,onpicktable:function(t,i,n){e._onPickTable(i,n)}}),editor:e.editor}),this.initSplitButton()},_onPickTable:function(e,t){!1!==this.fireEvent("picktable",e,t)&&this.popup.hide()}},e.inherits(o,n)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.UIBase,i=baidu.editor.ui.AutoTypeSetPicker=function(e){this.initOptions(e),this.initAutoTypeSetPicker()};i.prototype={initAutoTypeSetPicker:function(){this.initUIBase()},getHtmlTpl:function(){var e=this.editor,t=e.options.autotypeset,i=e.getLang("autoTypeSet"),n="textAlignValue"+e.uid,o="imageBlockLineValue"+e.uid,r="symbolConverValue"+e.uid;return'<div id="##" class="edui-autotypesetpicker %%"><div class="edui-autotypesetpicker-body"><table ><tr><td nowrap><input type="checkbox" name="mergeEmptyline" '+(t.mergeEmptyline?"checked":"")+">"+i.mergeLine+'</td><td colspan="2"><input type="checkbox" name="removeEmptyline" '+(t.removeEmptyline?"checked":"")+">"+i.delLine+'</td></tr><tr><td nowrap><input type="checkbox" name="removeClass" '+(t.removeClass?"checked":"")+">"+i.removeFormat+'</td><td colspan="2"><input type="checkbox" name="indent" '+(t.indent?"checked":"")+">"+i.indent+'</td></tr><tr><td nowrap><input type="checkbox" name="textAlign" '+(t.textAlign?"checked":"")+">"+i.alignment+'</td><td colspan="2" id="'+n+'"><input type="radio" name="'+n+'" value="left" '+(t.textAlign&&"left"==t.textAlign?"checked":"")+">"+e.getLang("justifyleft")+'<input type="radio" name="'+n+'" value="center" '+(t.textAlign&&"center"==t.textAlign?"checked":"")+">"+e.getLang("justifycenter")+'<input type="radio" name="'+n+'" value="right" '+(t.textAlign&&"right"==t.textAlign?"checked":"")+">"+e.getLang("justifyright")+'</td></tr><tr><td nowrap><input type="checkbox" name="imageBlockLine" '+(t.imageBlockLine?"checked":"")+">"+i.imageFloat+'</td><td nowrap id="'+o+'"><input type="radio" name="'+o+'" value="none" '+(t.imageBlockLine&&"none"==t.imageBlockLine?"checked":"")+">"+e.getLang("default")+'<input type="radio" name="'+o+'" value="left" '+(t.imageBlockLine&&"left"==t.imageBlockLine?"checked":"")+">"+e.getLang("justifyleft")+'<input type="radio" name="'+o+'" value="center" '+(t.imageBlockLine&&"center"==t.imageBlockLine?"checked":"")+">"+e.getLang("justifycenter")+'<input type="radio" name="'+o+'" value="right" '+(t.imageBlockLine&&"right"==t.imageBlockLine?"checked":"")+">"+e.getLang("justifyright")+'</td></tr><tr><td nowrap><input type="checkbox" name="clearFontSize" '+(t.clearFontSize?"checked":"")+">"+i.removeFontsize+'</td><td colspan="2"><input type="checkbox" name="clearFontFamily" '+(t.clearFontFamily?"checked":"")+">"+i.removeFontFamily+'</td></tr><tr><td nowrap colspan="3"><input type="checkbox" name="removeEmptyNode" '+(t.removeEmptyNode?"checked":"")+">"+i.removeHtml+'</td></tr><tr><td nowrap colspan="3"><input type="checkbox" name="pasteFilter" '+(t.pasteFilter?"checked":"")+">"+i.pasteFilter+'</td></tr><tr><td nowrap><input type="checkbox" name="symbolConver" '+(t.bdc2sb||t.tobdc?"checked":"")+">"+i.symbol+'</td><td id="'+r+'"><input type="radio" name="bdc" value="bdc2sb" '+(t.bdc2sb?"checked":"")+">"+i.bdc2sb+'<input type="radio" name="bdc" value="tobdc" '+(t.tobdc?"checked":"")+">"+i.tobdc+'</td><td nowrap align="right"><button >'+i.run+"</button></td></tr></table></div></div>"},_UIBase_render:t.prototype.render},e.inherits(i,t)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.Popup,i=baidu.editor.ui.AutoTypeSetPicker,n=baidu.editor.ui.SplitButton,o=baidu.editor.ui.AutoTypeSetButton=function(e){this.initOptions(e),this.initAutoTypeSetButton()};function r(t){for(var i,n={},o=t.getDom(),r=t.editor.uid,a=null,s=domUtils.getElementsByTagName(o,"input"),l=s.length-1;i=s[l--];)if("checkbox"==i.getAttribute("type"))if(n[a=i.getAttribute("name")]&&delete n[a],i.checked){var d=document.getElementById(a+"Value"+r);if(d){if(/input/gi.test(d.tagName))n[a]=d.value;else for(var c,u=d.getElementsByTagName("input"),m=u.length-1;c=u[m--];)if(c.checked){n[a]=c.value;break}}else n[a]=!0}else n[a]=!1;else n[i.getAttribute("value")]=i.checked;var f,h=domUtils.getElementsByTagName(o,"select");for(l=0;f=h[l++];){var p=f.getAttribute("name");n[p]=n[p]?f.value:""}e.extend(t.editor.options.autotypeset,n),t.editor.setPreferences("autotypeset",n)}o.prototype={initAutoTypeSetButton:function(){var e=this;this.popup=new t({content:new i({editor:e.editor}),editor:e.editor,hide:function(){!this._hidden&&this.getDom()&&(r(this),this.getDom().style.display="none",this._hidden=!0,this.fireEvent("hide"))}});var n=0;this.popup.addListener("postRenderAfter",function(){var t=this;if(!n){var i=this.getDom();i.getElementsByTagName("button")[0].onclick=function(){r(t),e.editor.execCommand("autotypeset"),t.hide()},domUtils.on(i,"click",function(i){var n=i.target||i.srcElement,o=e.editor.uid;if(n&&"INPUT"==n.tagName){if("imageBlockLine"==n.name||"textAlign"==n.name||"symbolConver"==n.name)for(var a=n.checked,s=document.getElementById(n.name+"Value"+o).getElementsByTagName("input"),l={imageBlockLine:"none",textAlign:"left",symbolConver:"tobdc"},d=0;d<s.length;d++)a?s[d].value==l[n.name]&&(s[d].checked="checked"):s[d].checked=!1;if(n.name=="imageBlockLineValue"+o||n.name=="textAlignValue"+o||"bdc"==n.name){var c=n.parentNode.previousSibling.getElementsByTagName("input");c&&(c[0].checked=!0)}r(t)}}),n=1}}),this.initSplitButton()}},e.inherits(o,n)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.Popup,i=baidu.editor.ui.Stateful,n=baidu.editor.ui.UIBase,o=baidu.editor.ui.CellAlignPicker=function(e){this.initOptions(e),this.initSelected(),this.initCellAlignPicker()};o.prototype={initSelected:function(){var e={top:0,middle:1,bottom:2},t={left:0,center:1,right:2},i=3;this.selected&&(this.selectedIndex=e[this.selected.valign]*i+t[this.selected.align])},initCellAlignPicker:function(){this.initUIBase(),this.Stateful_init()},getHtmlTpl:function(){for(var e=["left","center","right"],t=null,i=-1,n=[],o=0;o<9;o++)t=this.selectedIndex===o?' class="edui-cellalign-selected" ':"",0===(i=o%3)&&n.push("<tr>"),n.push('<td index="'+o+'" '+t+' stateful><div class="edui-icon edui-'+e[i]+'"></div></td>'),2===i&&n.push("</tr>");return'<div id="##" class="edui-cellalignpicker %%"><div class="edui-cellalignpicker-body"><table onclick="$$._onClick(event);">'+n.join("")+"</table></div></div>"},getStateDom:function(){return this.target},_onClick:function(e){var i=e.target||e.srcElement;/icon/.test(i.className)&&(this.items[i.parentNode.getAttribute("index")].onclick(),t.postHide(e))},_UIBase_render:n.prototype.render},e.inherits(o,n),e.extend(o.prototype,i,!0)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.Stateful,i=baidu.editor.ui.uiUtils,n=baidu.editor.ui.UIBase,o=baidu.editor.ui.PastePicker=function(e){this.initOptions(e),this.initPastePicker()};o.prototype={initPastePicker:function(){this.initUIBase(),this.Stateful_init()},getHtmlTpl:function(){return'<div class="edui-pasteicon" onclick="$$._onClick(this)"></div><div class="edui-pastecontainer"><div class="edui-title">'+this.editor.getLang("pasteOpt")+'</div><div class="edui-button"><div title="'+this.editor.getLang("pasteSourceFormat")+'" onclick="$$.format(false)" stateful><div class="edui-richtxticon"></div></div><div title="'+this.editor.getLang("tagFormat")+'" onclick="$$.format(2)" stateful><div class="edui-tagicon"></div></div><div title="'+this.editor.getLang("pasteTextFormat")+'" onclick="$$.format(true)" stateful><div class="edui-plaintxticon"></div></div></div></div></div>'},getStateDom:function(){return this.target},format:function(e){this.editor.ui._isTransfer=!0,this.editor.fireEvent("pasteTransfer",e)},_onClick:function(e){var t=domUtils.getNextDomNode(e),n=i.getViewportRect().height,o=i.getClientRect(t);o.top+o.height>n?t.style.top=-o.height-e.offsetHeight+"px":t.style.top="",/hidden/gi.test(domUtils.getComputedStyle(t,"visibility"))?(t.style.visibility="visible",domUtils.addClass(e,"edui-state-opened")):(t.style.visibility="hidden",domUtils.removeClasses(e,"edui-state-opened"))},_UIBase_render:n.prototype.render},e.inherits(o,n),e.extend(o.prototype,t,!0)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.uiUtils,i=baidu.editor.ui.UIBase,n=baidu.editor.ui.Toolbar=function(e){this.initOptions(e),this.initToolbar()};n.prototype={items:null,initToolbar:function(){this.items=this.items||[],this.initUIBase()},add:function(e,t){void 0===t?this.items.push(e):this.items.splice(t,0,e)},getHtmlTpl:function(){for(var e=[],t=0;t<this.items.length;t++)e[t]=this.items[t].renderHtml();return'<div id="##" class="edui-toolbar %%" onselectstart="return false;" onmousedown="return $$._onMouseDown(event, this);">'+e.join("")+"</div>"},postRender:function(){for(var e=this.getDom(),i=0;i<this.items.length;i++)this.items[i].postRender();t.makeUnselectable(e)},_onMouseDown:function(e){var t=e.target||e.srcElement,i=t&&t.tagName&&t.tagName.toLowerCase();if("input"==i||"object"==i||"object"==i)return!1}},e.inherits(n,i)}(),function(){var e=baidu.editor.utils,t=baidu.editor.dom.domUtils,i=baidu.editor.ui.uiUtils,n=baidu.editor.ui.UIBase,o=baidu.editor.ui.Popup,r=baidu.editor.ui.Stateful,a=baidu.editor.ui.CellAlignPicker,s=baidu.editor.ui.Menu=function(e){this.initOptions(e),this.initMenu()},l={renderHtml:function(){return'<div class="edui-menuitem edui-menuseparator"><div class="edui-menuseparator-inner"></div></div>'},postRender:function(){},queryAutoHide:function(){return!0}};s.prototype={items:null,uiName:"menu",initMenu:function(){this.items=this.items||[],this.initPopup(),this.initItems()},initItems:function(){for(var e=0;e<this.items.length;e++){var t=this.items[e];"-"==t?this.items[e]=this.getSeparator():t instanceof d||(t.editor=this.editor,t.theme=this.editor.options.theme,this.items[e]=this.createItem(t))}},getSeparator:function(){return l},createItem:function(e){return e.menu=this,new d(e)},_Popup_getContentHtmlTpl:o.prototype.getContentHtmlTpl,getContentHtmlTpl:function(){if(0==this.items.length)return this._Popup_getContentHtmlTpl();for(var e=[],t=0;t<this.items.length;t++){var i=this.items[t];e[t]=i.renderHtml()}return'<div class="%%-body">'+e.join("")+"</div>"},_Popup_postRender:o.prototype.postRender,postRender:function(){for(var e=this,n=0;n<this.items.length;n++){var o=this.items[n];o.ownerMenu=this,o.postRender()}t.on(this.getDom(),"mouseover",function(t){var n=(t=t||event).relatedTarget||t.fromElement,o=e.getDom();i.contains(o,n)||o===n||e.fireEvent("over")}),this._Popup_postRender()},queryAutoHide:function(e){if(e){if(i.contains(this.getDom(),e))return!1;for(var t=0;t<this.items.length;t++){if(!1===this.items[t].queryAutoHide(e))return!1}}},clearItems:function(){for(var e=0;e<this.items.length;e++){var t=this.items[e];clearTimeout(t._showingTimer),clearTimeout(t._closingTimer),t.subMenu&&t.subMenu.destroy()}this.items=[]},destroy:function(){this.getDom()&&t.remove(this.getDom()),this.clearItems()},dispose:function(){this.destroy()}},e.inherits(s,o);var d=baidu.editor.ui.MenuItem=function(e){if(this.initOptions(e),this.initUIBase(),this.Stateful_init(),this.subMenu&&!(this.subMenu instanceof s))if(e.className&&-1!=e.className.indexOf("aligntd")){var i=this;this.subMenu.selected=this.editor.queryCommandValue("cellalignment"),this.subMenu=new o({content:new a(this.subMenu),parentMenu:i,editor:i.editor,destroy:function(){this.getDom()&&t.remove(this.getDom())}}),this.subMenu.addListener("postRenderAfter",function(){t.on(this.getDom(),"mouseover",function(){i.addState("opened")})})}else this.subMenu=new s(this.subMenu)};d.prototype={label:"",subMenu:null,ownerMenu:null,uiName:"menuitem",alwalysHoverable:!0,getHtmlTpl:function(){return'<div id="##" class="%%" stateful onclick="$$._onClick(event, this);"><div class="%%-body">'+this.renderLabelHtml()+"</div></div>"},postRender:function(){var e=this;this.addListener("over",function(){e.ownerMenu.fireEvent("submenuover",e),e.subMenu&&e.delayShowSubMenu()}),this.subMenu&&(this.getDom().className+=" edui-hassubmenu",this.subMenu.render(),this.addListener("out",function(){e.delayHideSubMenu()}),this.subMenu.addListener("over",function(){clearTimeout(e._closingTimer),e._closingTimer=null,e.addState("opened")}),this.ownerMenu.addListener("hide",function(){e.hideSubMenu()}),this.ownerMenu.addListener("submenuover",function(t,i){i!==e&&e.delayHideSubMenu()}),this.subMenu._bakQueryAutoHide=this.subMenu.queryAutoHide,this.subMenu.queryAutoHide=function(t){return(!t||!i.contains(e.getDom(),t))&&this._bakQueryAutoHide(t)}),this.getDom().style.tabIndex="-1",i.makeUnselectable(this.getDom()),this.Stateful_postRender()},delayShowSubMenu:function(){var e=this;e.isDisabled()||(e.addState("opened"),clearTimeout(e._showingTimer),clearTimeout(e._closingTimer),e._closingTimer=null,e._showingTimer=setTimeout(function(){e.showSubMenu()},250))},delayHideSubMenu:function(){var e=this;e.isDisabled()||(e.removeState("opened"),clearTimeout(e._showingTimer),e._closingTimer||(e._closingTimer=setTimeout(function(){e.hasState("opened")||e.hideSubMenu(),e._closingTimer=null},400)))},renderLabelHtml:function(){return'<div class="edui-arrow"></div><div class="edui-box edui-icon"></div><div class="edui-box edui-label %%-label">'+(this.label||"")+"</div>"},getStateDom:function(){return this.getDom()},queryAutoHide:function(e){if(this.subMenu&&this.hasState("opened"))return this.subMenu.queryAutoHide(e)},_onClick:function(e,t){this.hasState("disabled")||!1!==this.fireEvent("click",e,t)&&(this.subMenu?this.showSubMenu():o.postHide(e))},showSubMenu:function(){var e=i.getClientRect(this.getDom());e.right-=5,e.left+=2,e.width-=7,e.top-=4,e.bottom+=4,e.height+=8,this.subMenu.showAnchorRect(e,!0,!0)},hideSubMenu:function(){this.subMenu.hide()}},e.inherits(d,n),e.extend(d.prototype,r,!0)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.uiUtils,i=baidu.editor.ui.Menu,n=baidu.editor.ui.SplitButton,o=baidu.editor.ui.Combox=function(e){this.initOptions(e),this.initCombox()};o.prototype={uiName:"combox",onbuttonclick:function(){this.showPopup()},initCombox:function(){var e=this;this.items=this.items||[];for(var t=0;t<this.items.length;t++){var n=this.items[t];n.uiName="listitem",n.index=t,n.onclick=function(){e.selectByIndex(this.index)}}this.popup=new i({items:this.items,uiName:"list",editor:this.editor,captureWheel:!0,combox:this}),this.initSplitButton()},_SplitButton_postRender:n.prototype.postRender,postRender:function(){this._SplitButton_postRender(),this.setLabel(this.label||""),this.setValue(this.initValue||"")},showPopup:function(){var e=t.getClientRect(this.getDom());e.top+=1,e.bottom-=1,e.height-=2,this.popup.showAnchorRect(e)},getValue:function(){return this.value},setValue:function(e){var t=this.indexByValue(e);-1!=t?(this.selectedIndex=t,this.setLabel(this.items[t].label),this.value=this.items[t].value):(this.selectedIndex=-1,this.setLabel(this.getLabelForUnknowValue(e)),this.value=e)},setLabel:function(e){this.getDom("button_body").innerHTML=e,this.label=e},getLabelForUnknowValue:function(e){return e},indexByValue:function(e){for(var t=0;t<this.items.length;t++)if(e==this.items[t].value)return t;return-1},getItem:function(e){return this.items[e]},selectByIndex:function(e){e<this.items.length&&!1!==this.fireEvent("select",e)&&(this.selectedIndex=e,this.value=this.items[e].value,this.setLabel(this.items[e].label))}},e.inherits(o,n)}(),function(){var e,t,i,n=baidu.editor.utils,o=baidu.editor.dom.domUtils,r=baidu.editor.ui.uiUtils,a=baidu.editor.ui.Mask,s=baidu.editor.ui.UIBase,l=baidu.editor.ui.Button,d=baidu.editor.ui.Dialog=function(e){if(e.name){var t=e.name,i=e.cssRules;e.className||(e.className="edui-for-"+t),i&&(e.cssRules=".edui-default .edui-for-"+t+" .edui-dialog-content  {"+i+"}")}this.initOptions(n.extend({autoReset:!0,draggable:!0,onok:function(){},oncancel:function(){},onclose:function(e,t){return t?this.onok():this.oncancel()},holdScroll:!1},e)),this.initDialog()};d.prototype={draggable:!1,uiName:"dialog",initDialog:function(){var o=this,r=this.editor.options.theme;if(this.cssRules&&n.cssRule("edui-customize-"+this.name+"-style",this.cssRules),this.initUIBase(),this.modalMask=e||(e=new a({className:"edui-dialog-modalmask",theme:r,onclick:function(){i&&i.close(!1)}})),this.dragMask=t||(t=new a({className:"edui-dialog-dragmask",theme:r})),this.closeButton=new l({className:"edui-dialog-closebutton",title:o.closeDialog,theme:r,onclick:function(){o.close(!1)}}),this.fullscreen&&this.initResizeEvent(),this.buttons)for(var s=0;s<this.buttons.length;s++)this.buttons[s]instanceof l||(this.buttons[s]=new l(n.extend(this.buttons[s],{editor:this.editor},!0)))},initResizeEvent:function(){var e=this;o.on(window,"resize",function(){e._hidden||void 0===e._hidden||(e.__resizeTimer&&window.clearTimeout(e.__resizeTimer),e.__resizeTimer=window.setTimeout(function(){e.__resizeTimer=null;var t=e.getDom(),i=e.getDom("content"),n=UE.ui.uiUtils.getClientRect(t),o=UE.ui.uiUtils.getClientRect(i),a=r.getViewportRect();i.style.width=a.width-n.width+o.width+"px",i.style.height=a.height-n.height+o.height+"px",t.style.width=a.width+"px",t.style.height=a.height+"px",e.fireEvent("resize")},100))})},fitSize:function(){var e=this.getDom("body"),t=this.mesureSize();return e.style.width=t.width+"px",e.style.height=t.height+"px",t},safeSetOffset:function(e){var t=this.getDom(),i=r.getViewportRect(),n=r.getClientRect(t),o=e.left;o+n.width>i.right&&(o=i.right-n.width);var a=e.top;a+n.height>i.bottom&&(a=i.bottom-n.height),t.style.left=Math.max(o,0)+"px",t.style.top=Math.max(a,0)+"px"},showAtCenter:function(){var e=r.getViewportRect();if(this.fullscreen){var t=this.getDom(),i=this.getDom("content");t.style.display="block";var n=UE.ui.uiUtils.getClientRect(t),a=UE.ui.uiUtils.getClientRect(i);t.style.left="-100000px",i.style.width=e.width-n.width+a.width+"px",i.style.height=e.height-n.height+a.height+"px",t.style.width=e.width+"px",t.style.height=e.height+"px",t.style.left=0,this._originalContext={html:{overflowX:document.documentElement.style.overflowX,overflowY:document.documentElement.style.overflowY},body:{overflowX:document.body.style.overflowX,overflowY:document.body.style.overflowY}},document.documentElement.style.overflowX="hidden",document.documentElement.style.overflowY="hidden",document.body.style.overflowX="hidden",document.body.style.overflowY="hidden"}else{this.getDom().style.display="";var s=this.fitSize(),l=0|this.getDom("titlebar").offsetHeight,d=e.width/2-s.width/2,c=e.height/2-(s.height-l)/2-l,u=this.getDom();this.safeSetOffset({left:Math.max(0|d,0),top:Math.max(0|c,0)}),o.hasClass(u,"edui-state-centered")||(u.className+=" edui-state-centered")}this._show()},getContentHtml:function(){var e="";return"string"==typeof this.content?e=this.content:this.iframeUrl&&(e='<span id="'+this.id+'_contmask" class="dialogcontmask"></span><iframe id="'+this.id+'_iframe" class="%%-iframe" height="100%" width="100%" frameborder="0" src="'+this.iframeUrl+'"></iframe>'),e},getHtmlTpl:function(){var e="";if(this.buttons){for(var t=[],i=0;i<this.buttons.length;i++)t[i]=this.buttons[i].renderHtml();e='<div class="%%-foot"><div id="##_buttons" class="%%-buttons">'+t.join("")+"</div></div>"}return'<div id="##" class="%%"><div '+(this.fullscreen?'class="%%-wrap edui-dialog-fullscreen-flag"':'class="%%"')+'><div id="##_body" class="%%-body"><div class="%%-shadow"></div><div id="##_titlebar" class="%%-titlebar"><div class="%%-draghandle" onmousedown="$$._onTitlebarMouseDown(event, this);"><span class="%%-caption">'+(this.title||"")+"</span></div>"+this.closeButton.renderHtml()+'</div><div id="##_content" class="%%-content">'+(this.autoReset?"":this.getContentHtml())+"</div>"+e+"</div></div></div>"},postRender:function(){this.modalMask.getDom()||(this.modalMask.render(),this.modalMask.hide()),this.dragMask.getDom()||(this.dragMask.render(),this.dragMask.hide());var e=this;if(this.addListener("show",function(){e.modalMask.show(this.getDom().style.zIndex-2)}),this.addListener("hide",function(){e.modalMask.hide()}),this.buttons)for(var t=0;t<this.buttons.length;t++)this.buttons[t].postRender();o.on(window,"resize",function(){setTimeout(function(){e.isHidden()||e.safeSetOffset(r.getClientRect(e.getDom()))})}),this._hide()},mesureSize:function(){var e=this.getDom("body"),t=r.getClientRect(this.getDom("content")).width;return e.style.width=t,r.getClientRect(e)},_onTitlebarMouseDown:function(e,t){if(this.draggable){r.getViewportRect();var i,n=this;r.startDrag(e,{ondragstart:function(){i=r.getClientRect(n.getDom()),n.getDom("contmask").style.visibility="visible",n.dragMask.show(n.getDom().style.zIndex-1)},ondragmove:function(e,t){var o=i.left+e,r=i.top+t;n.safeSetOffset({left:o,top:r})},ondragstop:function(){n.getDom("contmask").style.visibility="hidden",o.removeClasses(n.getDom(),["edui-state-centered"]),n.dragMask.hide()}})}},reset:function(){this.getDom("content").innerHTML=this.getContentHtml(),this.fireEvent("dialogafterreset")},_show:function(){this._hidden&&(this.getDom().style.display="",this.editor.container.style.zIndex&&(this.getDom().style.zIndex=1*this.editor.container.style.zIndex+10),this._hidden=!1,this.fireEvent("show"),baidu.editor.ui.uiUtils.getFixedLayer().style.zIndex=this.getDom().style.zIndex-4)},isHidden:function(){return this._hidden},_hide:function(){if(!this._hidden){var e=this.getDom();e.style.display="none",e.style.zIndex="",e.style.width="",e.style.height="",this._hidden=!0,this.fireEvent("hide")}},open:function(){if(this.autoReset)try{this.reset()}catch(e){this.render(),this.open()}if(this.showAtCenter(),this.iframeUrl)try{this.getDom("iframe").focus()}catch(e){}i=this},_onCloseButtonClick:function(e,t){this.close(!1)},close:function(e){if(!1!==this.fireEvent("close",e)){this.fullscreen&&(document.documentElement.style.overflowX=this._originalContext.html.overflowX,document.documentElement.style.overflowY=this._originalContext.html.overflowY,document.body.style.overflowX=this._originalContext.body.overflowX,document.body.style.overflowY=this._originalContext.body.overflowY,delete this._originalContext),this._hide();var t=this.getDom("content"),i=this.getDom("iframe");if(t&&i){var n=i.contentDocument||i.contentWindow.document;n&&(n.body.innerHTML=""),o.remove(t)}}}},n.inherits(d,s)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.Menu,i=baidu.editor.ui.SplitButton,n=baidu.editor.ui.MenuButton=function(e){this.initOptions(e),this.initMenuButton()};n.prototype={initMenuButton:function(){var e=this;this.uiName="menubutton",this.popup=new t({items:e.items,className:e.className,editor:e.editor}),this.popup.addListener("show",function(){for(var t=0;t<this.items.length;t++)this.items[t].removeState("checked"),this.items[t].value==e._value&&(this.items[t].addState("checked"),this.value=e._value)}),this.initSplitButton()},setValue:function(e){this._value=e}},e.inherits(n,i)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.Popup,i=baidu.editor.ui.SplitButton,n=baidu.editor.ui.MultiMenuPop=function(e){this.initOptions(e),this.initMultiMenu()};n.prototype={initMultiMenu:function(){var e=this;this.popup=new t({content:"",editor:e.editor,iframe_rendered:!1,onshow:function(){this.iframe_rendered||(this.iframe_rendered=!0,this.getDom("content").innerHTML='<iframe id="'+e.id+'_iframe" src="'+e.iframeUrl+'" frameborder="0"></iframe>',e.editor.container.style.zIndex&&(this.getDom().style.zIndex=1*e.editor.container.style.zIndex+1))}}),this.onbuttonclick=function(){this.showPopup()},this.initSplitButton()}},e.inherits(n,i)}(),function(){var e,t=baidu.editor.ui,i=t.UIBase,n=t.uiUtils,o=baidu.editor.utils,r=baidu.editor.dom.domUtils,a=[],s=!1,l=t.ShortCutMenu=function(e){this.initOptions(e),this.initShortCutMenu()};function d(e){var t=e.target||e.srcElement;if(!r.findParent(t,function(e){return r.hasClass(e,"edui-shortcutmenu")||r.hasClass(e,"edui-popup")},!0))for(var i,n=0;i=a[n++];)i.hide()}l.postHide=d,l.prototype={isHidden:!0,SPACE:5,initShortCutMenu:function(){this.items=this.items||[],this.initUIBase(),this.initItems(),this.initEvent(),a.push(this)},initEvent:function(){var t=this,i=t.editor.document;r.on(i,"mousemove",function(i){if(!1===t.isHidden){if(t.getSubMenuMark()||"contextmenu"==t.eventType)return;var n=!0,o=t.getDom(),r=o.offsetWidth,a=o.offsetHeight,s=r/2+t.SPACE,l=a/2,d=Math.abs(i.screenX-t.left),c=Math.abs(i.screenY-t.top);clearTimeout(e),e=setTimeout(function(){c>0&&c<l?t.setOpacity(o,"1"):c>l&&c<l+70?(t.setOpacity(o,"0.5"),n=!1):c>l+70&&c<l+140&&t.hide(),n&&d>0&&d<s?t.setOpacity(o,"1"):d>s&&d<s+70?t.setOpacity(o,"0.5"):d>s+70&&d<s+140&&t.hide()})}}),browser.chrome&&r.on(i,"mouseout",function(e){var i=e.relatedTarget||e.toElement;null!=i&&"HTML"!=i.tagName||t.hide()}),t.editor.addListener("afterhidepop",function(){t.isHidden||(s=!0)})},initItems:function(){if(o.isArray(this.items))for(var e=0,i=this.items.length;e<i;e++){var n=this.items[e].toLowerCase();t[n]&&(this.items[e]=new t[n](this.editor),this.items[e].className+=" edui-shortcutsubmenu ")}},setOpacity:function(e,t){browser.ie&&browser.version<9?e.style.filter="alpha(opacity = "+100*parseFloat(t)+");":e.style.opacity=t},getSubMenuMark:function(){s=!1;for(var e,t=n.getFixedLayer(),i=r.getElementsByTagName(t,"div",function(e){return r.hasClass(e,"edui-shortcutsubmenu edui-popup")}),o=0;e=i[o++];)"none"!=e.style.display&&(s=!0);return s},show:function(e,t){var i=this,o={},a=this.getDom(),s=n.getFixedLayer();function l(e){e.left<0&&(e.left=0),e.top<0&&(e.top=0),a.style.cssText="position:absolute;left:"+e.left+"px;top:"+e.top+"px;"}function d(e){e.tagName||(e=e.getDom()),o.left=parseInt(e.style.left),o.top=parseInt(e.style.top),o.top-=a.offsetHeight+15,l(o)}if(i.eventType=e.type,a.style.cssText="display:block;left:-9999px","contextmenu"==e.type&&t){var c=r.getElementsByTagName(s,"div","edui-contextmenu")[0];c?d(c):i.editor.addListener("aftershowcontextmenu",function(e,t){d(t)})}else(o=n.getViewportOffsetByEvent(e)).top-=a.offsetHeight+i.SPACE,o.left+=i.SPACE+20,l(o),i.setOpacity(a,.2);i.isHidden=!1,i.left=e.screenX+a.offsetWidth/2-i.SPACE,i.top=e.screenY-a.offsetHeight/2-i.SPACE,i.editor&&(a.style.zIndex=1*i.editor.container.style.zIndex+10,s.style.zIndex=a.style.zIndex-1)},hide:function(){this.getDom()&&(this.getDom().style.display="none"),this.isHidden=!0},postRender:function(){if(o.isArray(this.items))for(var e,t=0;e=this.items[t++];)e.postRender()},getHtmlTpl:function(){var e;if(o.isArray(this.items)){e=[];for(var t=0;t<this.items.length;t++)e[t]=this.items[t].renderHtml();e=e.join("")}else e=this.items;return'<div id="##" class="%% edui-toolbar" data-src="shortcutmenu" onmousedown="return false;" onselectstart="return false;" >'+e+"</div>"}},o.inherits(l,i),r.on(document,"mousedown",function(e){d(e)}),r.on(window,"scroll",function(e){d(e)})}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui.UIBase,i=baidu.editor.ui.Breakline=function(e){this.initOptions(e),this.initSeparator()};i.prototype={uiName:"Breakline",initSeparator:function(){this.initUIBase()},getHtmlTpl:function(){return"<br/>"}},e.inherits(i,t)}(),function(){var e=baidu.editor.utils,t=baidu.editor.dom.domUtils,i=baidu.editor.ui.UIBase,n=baidu.editor.ui.Message=function(e){this.initOptions(e),this.initMessage()};n.prototype={initMessage:function(){this.initUIBase()},getHtmlTpl:function(){return'<div id="##" class="edui-message %%"> <div id="##_closer" class="edui-message-closer">×</div> <div id="##_body" class="edui-message-body edui-message-type-info"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank"></iframe> <div class="edui-shadow"></div> <div id="##_content" class="edui-message-content">  </div> </div></div>'},reset:function(e){var t=this;e.keepshow||(clearTimeout(this.timer),t.timer=setTimeout(function(){t.hide()},e.timeout||4e3)),void 0!==e.content&&t.setContent(e.content),void 0!==e.type&&t.setType(e.type),t.show()},postRender:function(){var e=this,i=this.getDom("closer");i&&t.on(i,"click",function(){e.hide()})},setContent:function(e){this.getDom("content").innerHTML=e},setType:function(e){e=e||"info";var t=this.getDom("body");t.className=t.className.replace(/edui-message-type-[\w-]+/,"edui-message-type-"+e)},getContent:function(){return this.getDom("content").innerHTML},getType:function(){var e=this.getDom("body").match(/edui-message-type-([\w-]+)/);return e?e[1]:""},show:function(){this.getDom().style.display="block"},hide:function(){var e=this.getDom();e&&(e.style.display="none",e.parentNode&&e.parentNode.removeChild(e))}},e.inherits(n,i)}(),function(){var e=baidu.editor.utils,t=baidu.editor.ui,i=t.Dialog;t.buttons={},t.Dialog=function(e){var t=new i(e);return t.addListener("hide",function(){if(t.editor){var e=t.editor;try{if(browser.gecko){var i=e.window.scrollY,n=e.window.scrollX;e.body.focus(),e.window.scrollTo(n,i)}else e.focus()}catch(e){}}}),t};for(var n={anchor:"~/dialogs/anchor/anchor.html",insertimage:"~/dialogs/image/image.html",link:"~/dialogs/link/link.html",spechars:"~/dialogs/spechars/spechars.html",searchreplace:"~/dialogs/searchreplace/searchreplace.html",map:"~/dialogs/map/map.html#ak="+uedtiro_baidumap_ak,gmap:"~/dialogs/gmap/gmap.html",insertvideo:"~/dialogs/video/video.html",help:"~/dialogs/help/help.html",preview:"~/dialogs/preview/preview.html",emotion:"~/dialogs/emotion/emotion.html",wordimage:"~/dialogs/wordimage/wordimage.html",attachment:"~/dialogs/attachment/attachment.html",insertframe:"~/dialogs/insertframe/insertframe.html",edittip:"~/dialogs/table/edittip.html",edittable:"~/dialogs/table/edittable.html",edittd:"~/dialogs/table/edittd.html",webapp:"~/dialogs/webapp/webapp.html",snapscreen:"~/dialogs/snapscreen/snapscreen.html",scrawl:"~/dialogs/scrawl/scrawl.html",music:"~/dialogs/music/music.html",template:"~/dialogs/template/template.html",background:"~/dialogs/background/background.html",charts:"~/dialogs/charts/charts.html"},o=["undo","redo","formatmatch","bold","italic","underline","fontborder","touppercase","tolowercase","strikethrough","subscript","superscript","source","indent","outdent","blockquote","pasteplain","pagebreak","selectall","print","horizontal","removeformat","time","date","unlink","insertparagraphbeforetable","insertrow","insertcol","mergeright","mergedown","deleterow","deletecol","splittorows","splittocols","splittocells","mergecells","deletetable","drafts"],r=0;l=o[r++];)l=l.toLowerCase(),t[l]=function(e){return function(i){var n=new t.Button({className:"edui-for-"+e,title:i.options.labelMap[e]||i.getLang("labelMap."+e)||"",onclick:function(){i.execCommand(e)},theme:i.options.theme,showText:!1});return t.buttons[e]=n,i.addListener("selectionchange",function(t,o,r){var a=i.queryCommandState(e);-1==a?(n.setDisabled(!0),n.setChecked(!1)):r||(n.setDisabled(!1),n.setChecked(a))}),n}}(l);t.cleardoc=function(e){var i=new t.Button({className:"edui-for-cleardoc",title:e.options.labelMap.cleardoc||e.getLang("labelMap.cleardoc")||"",theme:e.options.theme,onclick:function(){confirm(e.getLang("confirmClear"))&&e.execCommand("cleardoc")}});return t.buttons.cleardoc=i,e.addListener("selectionchange",function(){i.setDisabled(-1==e.queryCommandState("cleardoc"))}),i};var a={justify:["left","right","center","justify"],imagefloat:["none","left","center","right"],directionality:["ltr","rtl"]};for(var s in a)!function(e,i){for(var n,o=0;n=i[o++];)!function(i){t[e.replace("float","")+i]=function(n){var o=new t.Button({className:"edui-for-"+e.replace("float","")+i,title:n.options.labelMap[e.replace("float","")+i]||n.getLang("labelMap."+e.replace("float","")+i)||"",theme:n.options.theme,onclick:function(){n.execCommand(e,i)}});return t.buttons[e]=o,n.addListener("selectionchange",function(t,r,a){o.setDisabled(-1==n.queryCommandState(e)),o.setChecked(n.queryCommandValue(e)==i&&!a)}),o}}(n)}(s,a[s]);var l;for(r=0;l=["backcolor","forecolor"][r++];)t[l]=function(e){return function(i){var n=new t.ColorButton({className:"edui-for-"+e,color:"default",title:i.options.labelMap[e]||i.getLang("labelMap."+e)||"",editor:i,onpickcolor:function(t,n){i.execCommand(e,n)},onpicknocolor:function(){i.execCommand(e,"default"),this.setColor("transparent"),this.color="default"},onbuttonclick:function(){i.execCommand(e,this.color)}});return t.buttons[e]=n,i.addListener("selectionchange",function(){n.setDisabled(-1==i.queryCommandState(e))}),n}}(l);var d={noOk:["searchreplace","help","spechars","webapp","preview"],ok:["attachment","anchor","link","insertimage","map","gmap","insertframe","wordimage","insertvideo","insertframe","edittip","edittable","edittd","scrawl","template","music","background","charts"]};for(var s in d)!function(i,o){for(var r,a=0;r=o[a++];)browser.opera&&"searchreplace"===r||function(o){t[o]=function(r,a,s){var l;a=a||(r.options.iframeUrlMap||{})[o]||n[o],s=r.options.labelMap[o]||r.getLang("labelMap."+o)||"",a&&(l=new t.Dialog(e.extend({iframeUrl:r.ui.mapUrl(a),editor:r,className:"edui-for-"+o,title:s,holdScroll:"insertimage"===o,fullscreen:/charts|preview/.test(o),closeDialog:r.getLang("closeDialog")},"ok"==i?{buttons:[{className:"edui-okbutton",label:r.getLang("ok"),editor:r,onclick:function(){l.close(!0)}},{className:"edui-cancelbutton",label:r.getLang("cancel"),editor:r,onclick:function(){l.close(!1)}}]}:{})),r.ui._dialogs[o+"Dialog"]=l);var d=new t.Button({className:"edui-for-"+o,title:s,onclick:function(){if(l)switch(o){case"wordimage":var e=r.execCommand("wordimage");e&&e.length&&(l.render(),l.open());break;case"scrawl":-1!=r.queryCommandState("scrawl")&&(l.render(),l.open());break;default:l.render(),l.open()}},theme:r.options.theme,disabled:"scrawl"==o&&-1==r.queryCommandState("scrawl")||"charts"==o});return t.buttons[o]=d,r.addListener("selectionchange",function(){if(!(o in{edittable:1})){var e=r.queryCommandState(o);d.getDom()&&(d.setDisabled(-1==e),d.setChecked(e))}}),d}}(r.toLowerCase())}(s,d[s]);t.snapscreen=function(e,i,o){o=e.options.labelMap.snapscreen||e.getLang("labelMap.snapscreen")||"";var r=new t.Button({className:"edui-for-snapscreen",title:o,onclick:function(){e.execCommand("snapscreen")},theme:e.options.theme});if(t.buttons.snapscreen=r,i=i||(e.options.iframeUrlMap||{}).snapscreen||n.snapscreen){var a=new t.Dialog({iframeUrl:e.ui.mapUrl(i),editor:e,className:"edui-for-snapscreen",title:o,buttons:[{className:"edui-okbutton",label:e.getLang("ok"),editor:e,onclick:function(){a.close(!0)}},{className:"edui-cancelbutton",label:e.getLang("cancel"),editor:e,onclick:function(){a.close(!1)}}]});a.render(),e.ui._dialogs.snapscreenDialog=a}return e.addListener("selectionchange",function(){r.setDisabled(-1==e.queryCommandState("snapscreen"))}),r},t.insertcode=function(i,n,o){n=i.options.insertcode||[],o=i.options.labelMap.insertcode||i.getLang("labelMap.insertcode")||"";var r=[];e.each(n,function(e,t){r.push({label:e,value:t,theme:i.options.theme,renderLabelHtml:function(){return'<div class="edui-label %%-label" >'+(this.label||"")+"</div>"}})});var a=new t.Combox({editor:i,items:r,onselect:function(e,t){i.execCommand("insertcode",this.items[t].value)},onbuttonclick:function(){this.showPopup()},title:o,initValue:o,className:"edui-for-insertcode",indexByValue:function(e){if(e)for(var t,i=0;t=this.items[i];i++)if(-1!=t.value.indexOf(e))return i;return-1}});return t.buttons.insertcode=a,i.addListener("selectionchange",function(e,t,n){if(!n)if(-1==i.queryCommandState("insertcode"))a.setDisabled(!0);else{a.setDisabled(!1);var r=i.queryCommandValue("insertcode");if(!r)return void a.setValue(o);r&&(r=r.replace(/['"]/g,"").split(",")[0]),a.setValue(r)}}),a},t.fontfamily=function(i,n,o){if(n=i.options.fontfamily||[],o=i.options.labelMap.fontfamily||i.getLang("labelMap.fontfamily")||"",n.length){for(var r,a=0,s=[];r=n[a];a++){var l=i.getLang("fontfamily")[r.name]||"";d=r.label||l,c=r.val,s.push({label:d,value:c,theme:i.options.theme,renderLabelHtml:function(){return'<div class="edui-label %%-label" style="font-family:'+e.unhtml(this.value)+'">'+(this.label||"")+"</div>"}})}var d,c,u=new t.Combox({editor:i,items:s,onselect:function(e,t){i.execCommand("FontFamily",this.items[t].value)},onbuttonclick:function(){this.showPopup()},title:o,initValue:o,className:"edui-for-fontfamily",indexByValue:function(e){if(e)for(var t,i=0;t=this.items[i];i++)if(-1!=t.value.indexOf(e))return i;return-1}});return t.buttons.fontfamily=u,i.addListener("selectionchange",function(e,t,n){if(!n)if(-1==i.queryCommandState("FontFamily"))u.setDisabled(!0);else{u.setDisabled(!1);var o=i.queryCommandValue("FontFamily");o&&(o=o.replace(/['"]/g,"").split(",")[0]),u.setValue(o)}}),u}},t.fontsize=function(e,i,n){if(n=e.options.labelMap.fontsize||e.getLang("labelMap.fontsize")||"",(i=i||e.options.fontsize||[]).length){for(var o=[],r=0;r<i.length;r++){var a=i[r]+"px";o.push({label:a,value:a,theme:e.options.theme,renderLabelHtml:function(){return'<div class="edui-label %%-label" style="line-height:1;font-size:'+this.value+'">'+(this.label||"")+"</div>"}})}var s=new t.Combox({editor:e,items:o,title:n,initValue:n,onselect:function(t,i){e.execCommand("FontSize",this.items[i].value)},onbuttonclick:function(){this.showPopup()},className:"edui-for-fontsize"});return t.buttons.fontsize=s,e.addListener("selectionchange",function(t,i,n){n||(-1==e.queryCommandState("FontSize")?s.setDisabled(!0):(s.setDisabled(!1),s.setValue(e.queryCommandValue("FontSize"))))}),s}},t.paragraph=function(i,n,o){if(o=i.options.labelMap.paragraph||i.getLang("labelMap.paragraph")||"",n=i.options.paragraph||[],!e.isEmptyObject(n)){var r=[];for(var a in n)r.push({value:a,label:n[a]||i.getLang("paragraph")[a],theme:i.options.theme,renderLabelHtml:function(){return'<div class="edui-label %%-label"><span class="edui-for-'+this.value+'">'+(this.label||"")+"</span></div>"}});var s=new t.Combox({editor:i,items:r,title:o,initValue:o,className:"edui-for-paragraph",onselect:function(e,t){i.execCommand("Paragraph",this.items[t].value)},onbuttonclick:function(){this.showPopup()}});return t.buttons.paragraph=s,i.addListener("selectionchange",function(e,t,n){if(!n)if(-1==i.queryCommandState("Paragraph"))s.setDisabled(!0);else{s.setDisabled(!1);var o=i.queryCommandValue("Paragraph");-1!=s.indexByValue(o)?s.setValue(o):s.setValue(s.initValue)}}),s}},t.customstyle=function(e){var i=e.options.customstyle||[],n=e.options.labelMap.customstyle||e.getLang("labelMap.customstyle")||"";if(i.length){for(var o,r=e.getLang("customstyle"),a=0,s=[];o=i[a++];)!function(t){var i={};i.label=t.label?t.label:r[t.name],i.style=t.style,i.className=t.className,i.tag=t.tag,s.push({label:i.label,value:i,theme:e.options.theme,renderLabelHtml:function(){return'<div class="edui-label %%-label"><'+i.tag+" "+(i.className?' class="'+i.className+'"':"")+(i.style?' style="'+i.style+'"':"")+">"+i.label+"</"+i.tag+"></div>"}})}(o);var l=new t.Combox({editor:e,items:s,title:n,initValue:n,className:"edui-for-customstyle",onselect:function(t,i){e.execCommand("customstyle",this.items[i].value)},onbuttonclick:function(){this.showPopup()},indexByValue:function(e){for(var t,i=0;t=this.items[i++];)if(t.label==e)return i-1;return-1}});return t.buttons.customstyle=l,e.addListener("selectionchange",function(t,i,n){if(!n)if(-1==e.queryCommandState("customstyle"))l.setDisabled(!0);else{l.setDisabled(!1);var o=e.queryCommandValue("customstyle");-1!=l.indexByValue(o)?l.setValue(o):l.setValue(l.initValue)}}),l}},t.inserttable=function(e,i,n){n=e.options.labelMap.inserttable||e.getLang("labelMap.inserttable")||"";var o=new t.TableButton({editor:e,title:n,className:"edui-for-inserttable",onpicktable:function(t,i,n){e.execCommand("InsertTable",{numRows:n,numCols:i,border:1})},onbuttonclick:function(){this.showPopup()}});return t.buttons.inserttable=o,e.addListener("selectionchange",function(){o.setDisabled(-1==e.queryCommandState("inserttable"))}),o},t.lineheight=function(e){var i=e.options.lineheight||[];if(i.length){for(var n,o=0,r=[];n=i[o++];)r.push({label:n,value:n,theme:e.options.theme,onclick:function(){e.execCommand("lineheight",this.value)}});var a=new t.MenuButton({editor:e,className:"edui-for-lineheight",title:e.options.labelMap.lineheight||e.getLang("labelMap.lineheight")||"",items:r,onbuttonclick:function(){var t=e.queryCommandValue("LineHeight")||this.value;e.execCommand("LineHeight",t)}});return t.buttons.lineheight=a,e.addListener("selectionchange",function(){var t=e.queryCommandState("LineHeight");if(-1==t)a.setDisabled(!0);else{a.setDisabled(!1);var i=e.queryCommandValue("LineHeight");i&&a.setValue((i+"").replace(/cm/,"")),a.setChecked(t)}}),a}};for(var c,u=["top","bottom"],m=0;c=u[m++];)!function(e){t["rowspacing"+e]=function(i){var n=i.options["rowspacing"+e]||[];if(!n.length)return null;for(var o,r=0,a=[];o=n[r++];)a.push({label:o,value:o,theme:i.options.theme,onclick:function(){i.execCommand("rowspacing",this.value,e)}});var s=new t.MenuButton({editor:i,className:"edui-for-rowspacing"+e,title:i.options.labelMap["rowspacing"+e]||i.getLang("labelMap.rowspacing"+e)||"",items:a,onbuttonclick:function(){var t=i.queryCommandValue("rowspacing",e)||this.value;i.execCommand("rowspacing",t,e)}});return t.buttons[e]=s,i.addListener("selectionchange",function(){var t=i.queryCommandState("rowspacing",e);if(-1==t)s.setDisabled(!0);else{s.setDisabled(!1);var n=i.queryCommandValue("rowspacing",e);n&&s.setValue((n+"").replace(/%/,"")),s.setChecked(t)}}),s}}(c);for(var f,h=["insertorderedlist","insertunorderedlist"],p=0;f=h[p++];)!function(e){t[e]=function(i){var n=i.options[e],o=function(){i.execCommand(e,this.value)},r=[];for(var a in n)r.push({label:n[a]||i.getLang()[e][a]||"",value:a,theme:i.options.theme,onclick:o});var s=new t.MenuButton({editor:i,className:"edui-for-"+e,title:i.getLang("labelMap."+e)||"",items:r,onbuttonclick:function(){var t=i.queryCommandValue(e)||this.value;i.execCommand(e,t)}});return t.buttons[e]=s,i.addListener("selectionchange",function(){var t=i.queryCommandState(e);if(-1==t)s.setDisabled(!0);else{s.setDisabled(!1);var n=i.queryCommandValue(e);s.setValue(n),s.setChecked(t)}}),s}}(f);t.fullscreen=function(e,i){i=e.options.labelMap.fullscreen||e.getLang("labelMap.fullscreen")||"";var n=new t.Button({className:"edui-for-fullscreen",title:i,theme:e.options.theme,onclick:function(){e.ui&&e.ui.setFullScreen(!e.ui.isFullScreen()),this.setChecked(e.ui.isFullScreen())}});return t.buttons.fullscreen=n,e.addListener("selectionchange",function(){var t=e.queryCommandState("fullscreen");n.setDisabled(-1==t),n.setChecked(e.ui.isFullScreen())}),n},t.emotion=function(e,i){var o="emotion",r=new t.MultiMenuPop({title:e.options.labelMap[o]||e.getLang("labelMap."+o)||"",editor:e,className:"edui-for-"+o,iframeUrl:e.ui.mapUrl(i||(e.options.iframeUrlMap||{})[o]||n[o])});return t.buttons[o]=r,e.addListener("selectionchange",function(){r.setDisabled(-1==e.queryCommandState(o))}),r},t.autotypeset=function(e){var i=new t.AutoTypeSetButton({editor:e,title:e.options.labelMap.autotypeset||e.getLang("labelMap.autotypeset")||"",className:"edui-for-autotypeset",onbuttonclick:function(){e.execCommand("autotypeset")}});return t.buttons.autotypeset=i,e.addListener("selectionchange",function(){i.setDisabled(-1==e.queryCommandState("autotypeset"))}),i},t.simpleupload=function(e){var i="simpleupload",n=new t.Button({className:"edui-for-"+i,title:e.options.labelMap[i]||e.getLang("labelMap."+i)||"",onclick:function(){},theme:e.options.theme,showText:!1});return t.buttons[i]=n,e.addListener("ready",function(){var t=n.getDom("body").children[0];e.fireEvent("simpleuploadbtnready",t)}),e.addListener("selectionchange",function(t,o,r){var a=e.queryCommandState(i);-1==a?(n.setDisabled(!0),n.setChecked(!1)):r||(n.setDisabled(!1),n.setChecked(a))}),n}}(),function(){var utils=baidu.editor.utils,uiUtils=baidu.editor.ui.uiUtils,UIBase=baidu.editor.ui.UIBase,domUtils=baidu.editor.dom.domUtils,nodeStack=[];function EditorUI(e){this.initOptions(e),this.initEditorUI()}EditorUI.prototype={uiName:"editor",initEditorUI:function(){this.editor.ui=this,this._dialogs={},this.initUIBase(),this._initToolbars();var editor=this.editor,me=this;editor.addListener("ready",function(){if(editor.getDialog=function(e){return editor.ui._dialogs[e+"Dialog"]},domUtils.on(editor.window,"scroll",function(e){baidu.editor.ui.Popup.postHide(e)}),editor.ui._actualFrameWidth=editor.options.initialFrameWidth,UE.browser.ie&&6===UE.browser.version&&editor.container.ownerDocument.execCommand("BackgroundImageCache",!1,!0),editor.options.elementPathEnabled&&(editor.ui.getDom("elementpath").innerHTML='<div class="edui-editor-breadcrumb">'+editor.getLang("elementPathTip")+":</div>"),editor.options.wordCount){domUtils.on(editor.document,"click",function(){setCount(editor,me),domUtils.un(editor.document,"click",arguments.callee)}),editor.ui.getDom("wordcount").innerHTML=editor.getLang("wordCountTip")}editor.ui._scale(),editor.options.scaleEnabled?(editor.autoHeightEnabled&&editor.disableAutoHeight(),me.enableScale()):me.disableScale(),editor.options.elementPathEnabled||editor.options.wordCount||editor.options.scaleEnabled||(editor.ui.getDom("elementpath").style.display="none",editor.ui.getDom("wordcount").style.display="none",editor.ui.getDom("scale").style.display="none"),editor.selection.isFocus()&&editor.fireEvent("selectionchange",!1,!0)}),editor.addListener("mousedown",function(e,t){var i=t.target||t.srcElement;baidu.editor.ui.Popup.postHide(t,i),baidu.editor.ui.ShortCutMenu.postHide(t)}),editor.addListener("delcells",function(){UE.ui.edittip&&new UE.ui.edittip(editor),editor.getDialog("edittip").open()});var pastePop,isPaste=!1,timer;function setCount(e,t){e.setOpt({wordCount:!0,maximumWords:1e4,wordCountMsg:e.options.wordCountMsg||e.getLang("wordCountMsg"),wordOverFlowMsg:e.options.wordOverFlowMsg||e.getLang("wordOverFlowMsg")});var i=e.options,n=i.maximumWords,o=i.wordCountMsg,r=i.wordOverFlowMsg,a=t.getDom("wordcount");if(i.wordCount){var s=e.getContentLength(!0);s>n?(a.innerHTML=r,e.fireEvent("wordcountoverflow")):a.innerHTML=o.replace("{#leave}",n-s).replace("{#count}",s)}}editor.addListener("afterpaste",function(){editor.queryCommandState("pasteplain")||(baidu.editor.ui.PastePicker&&(pastePop=new baidu.editor.ui.Popup({content:new baidu.editor.ui.PastePicker({editor:editor}),editor:editor,className:"edui-wordpastepop"})).render(),isPaste=!0)}),editor.addListener("afterinserthtml",function(){clearTimeout(timer),timer=setTimeout(function(){if(pastePop&&(isPaste||editor.ui._isTransfer)){if(pastePop.isHidden()){var e=domUtils.createElement(editor.document,"span",{style:"line-height:0px;",innerHTML:"\ufeff"});editor.selection.getRange().insertNode(e);var t=getDomNode(e,"firstChild","previousSibling");t&&pastePop.showAnchor(3==t.nodeType?t.parentNode:t),domUtils.remove(e)}else pastePop.show();delete editor.ui._isTransfer,isPaste=!1}},200)}),editor.addListener("contextmenu",function(e,t){baidu.editor.ui.Popup.postHide(t)}),editor.addListener("keydown",function(e,t){pastePop&&pastePop.dispose(t);var i=t.keyCode||t.which;t.altKey&&90==i&&UE.ui.buttons.fullscreen.onclick()}),editor.addListener("wordcount",function(e){setCount(this,me)}),editor.addListener("selectionchange",function(){editor.options.elementPathEnabled&&me[(-1==editor.queryCommandState("elementpath")?"dis":"en")+"ableElementPath"](),editor.options.scaleEnabled&&me[(-1==editor.queryCommandState("scale")?"dis":"en")+"ableScale"]()});var popup=new baidu.editor.ui.Popup({editor:editor,content:"",className:"edui-bubble",_onEditButtonClick:function(){this.hide(),editor.ui._dialogs.linkDialog.open()},_onImgEditButtonClick:function(e){this.hide(),editor.ui._dialogs[e]&&editor.ui._dialogs[e].open()},_onImgSetFloat:function(e){this.hide(),editor.execCommand("imagefloat",e)},_setIframeAlign:function(e){var t=popup.anchorEl,i=t.cloneNode(!0);switch(e){case-2:i.setAttribute("align","");break;case-1:i.setAttribute("align","left");break;case 1:i.setAttribute("align","right")}t.parentNode.insertBefore(i,t),domUtils.remove(t),popup.anchorEl=i,popup.showAnchor(popup.anchorEl)},_updateIframe:function(){var e=editor._iframe=popup.anchorEl;domUtils.hasClass(e,"ueditor_baidumap")?(editor.selection.getRange().selectNode(e).select(),editor.ui._dialogs.mapDialog.open(),popup.hide()):(editor.ui._dialogs.insertframeDialog.open(),popup.hide())},_onRemoveButtonClick:function(e){editor.execCommand(e),this.hide()},queryAutoHide:function(e){return e&&e.ownerDocument==editor.document&&("img"==e.tagName.toLowerCase()||domUtils.findParentByTagName(e,"a",!0))?e!==popup.anchorEl:baidu.editor.ui.Popup.prototype.queryAutoHide.call(this,e)}});popup.render(),editor.options.imagePopup&&(editor.addListener("mouseover",function(e,t){var i=(t=t||window.event).target||t.srcElement;if(editor.ui._dialogs.insertframeDialog&&/iframe/gi.test(i.tagName)){var n=popup.formatHtml("<nobr>"+editor.getLang("property")+': <span onclick=$$._setIframeAlign(-2) class="edui-clickable">'+editor.getLang("default")+'</span>&nbsp;&nbsp;<span onclick=$$._setIframeAlign(-1) class="edui-clickable">'+editor.getLang("justifyleft")+'</span>&nbsp;&nbsp;<span onclick=$$._setIframeAlign(1) class="edui-clickable">'+editor.getLang("justifyright")+'</span>&nbsp;&nbsp; <span onclick="$$._updateIframe( this);" class="edui-clickable">'+editor.getLang("modify")+"</span></nobr>");n?(popup.getDom("content").innerHTML=n,popup.anchorEl=i,popup.showAnchor(popup.anchorEl)):popup.hide()}}),editor.addListener("selectionchange",function(t,causeByUi){if(causeByUi){var html="",str="",img=editor.selection.getRange().getClosedNode(),dialogs=editor.ui._dialogs;if(img&&"IMG"==img.tagName){var dialogName="insertimageDialog";if(-1==img.className.indexOf("edui-faked-video")&&-1==img.className.indexOf("edui-upload-video")||(dialogName="insertvideoDialog"),-1!=img.className.indexOf("edui-faked-webapp")&&(dialogName="webappDialog"),-1!=img.src.indexOf("http://api.map.baidu.com")&&(dialogName="mapDialog"),-1!=img.className.indexOf("edui-faked-music")&&(dialogName="musicDialog"),-1!=img.src.indexOf("http://maps.google.com/maps/api/staticmap")&&(dialogName="gmapDialog"),img.getAttribute("anchorname")&&(dialogName="anchorDialog",html=popup.formatHtml("<nobr>"+editor.getLang("property")+': <span onclick=$$._onImgEditButtonClick("anchorDialog") class="edui-clickable">'+editor.getLang("modify")+"</span>&nbsp;&nbsp;<span onclick=$$._onRemoveButtonClick('anchor') class=\"edui-clickable\">"+editor.getLang("delete")+"</span></nobr>")),img.getAttribute("word_img")&&(editor.word_img=[img.getAttribute("word_img")],dialogName="wordimageDialog"),(domUtils.hasClass(img,"loadingclass")||domUtils.hasClass(img,"loaderrorclass"))&&(dialogName=""),!dialogs[dialogName])return;if(0==editor.options.imageDqEnabled&&"insertimageDialog"==dialogName)return edustr='$EDITORUI["'+popup.getUid()+"\"]._onImgEditButtonClick('"+dialogName+"')",void eval(edustr);str="<nobr>"+editor.getLang("property")+': <span onclick=$$._onImgSetFloat("none") class="edui-clickable">'+editor.getLang("default")+'</span>&nbsp;&nbsp;<span onclick=$$._onImgSetFloat("left") class="edui-clickable">'+editor.getLang("justifyleft")+'</span>&nbsp;&nbsp;<span onclick=$$._onImgSetFloat("right") class="edui-clickable">'+editor.getLang("justifyright")+'</span>&nbsp;&nbsp;<span onclick=$$._onImgSetFloat("center") class="edui-clickable">'+editor.getLang("justifycenter")+"</span>&nbsp;&nbsp;<span onclick=\"$$._onImgEditButtonClick('"+dialogName+'\');" class="edui-clickable">'+editor.getLang("modify")+"</span></nobr>",!html&&(html=popup.formatHtml(str))}if(editor.ui._dialogs.linkDialog){var link=editor.queryCommandValue("link"),url;if(link&&(url=link.getAttribute("_href")||link.getAttribute("href",2))){var txt=url;url.length>30&&(txt=url.substring(0,20)+"..."),html&&(html+='<div style="height:5px;"></div>'),html+=popup.formatHtml("<nobr>"+editor.getLang("anthorMsg")+': <a target="_blank" href="'+url+'" title="'+url+'" >'+txt+'</a> <span class="edui-clickable" onclick="$$._onEditButtonClick();">'+editor.getLang("modify")+'</span> <span class="edui-clickable" onclick="$$._onRemoveButtonClick(\'unlink\');"> '+editor.getLang("clear")+"</span></nobr>"),popup.showAnchor(link)}}html?(popup.getDom("content").innerHTML=html,popup.anchorEl=img||link,popup.showAnchor(popup.anchorEl)):popup.hide()}}))},_initToolbars:function(){for(var e=this.editor,t=this.toolbars||[],i=[],n=0;n<t.length;n++){for(var o=t[n],r=new baidu.editor.ui.Toolbar({theme:e.options.theme}),a=0;a<o.length;a++){var s=o[a],l=null;if("string"==typeof s){if("emotion"==(s=s.toLowerCase())&&1==this.ismobile)continue;if("|"==s&&(s="Separator"),"||"==s&&(s="Breakline"),baidu.editor.ui[s]&&(l=new baidu.editor.ui[s](e)),"fullscreen"==s){i&&i[0]?i[0].items.splice(0,0,l):l&&r.items.splice(0,0,l);continue}}else l=s;l&&l.id&&r.add(l)}i[n]=r}utils.each(UE._customizeUI,function(t,i){var n,o;if(t.id&&t.id!=e.key)return!1;(n=t.execFn.call(e,e,i))&&(void 0===(o=t.index)&&(o=r.items.length),r.add(n,o))}),this.toolbars=i},getHtmlTpl:function(){return'<div id="##" class="%%"><div id="##_toolbarbox" class="%%-toolbarbox">'+(this.toolbars.length?'<div id="##_toolbarboxouter" class="%%-toolbarboxouter"><div class="%%-toolbarboxinner">'+this.renderToolbarBoxHtml()+"</div></div>":"")+'<div id="##_toolbarmsg" class="%%-toolbarmsg" style="display:none;"><div id = "##_upload_dialog" class="%%-toolbarmsg-upload" onclick="$$.showWordImageDialog();">'+this.editor.getLang("clickToUpload")+'</div><div class="%%-toolbarmsg-close" onclick="$$.hideToolbarMsg();">x</div><div id="##_toolbarmsg_label" class="%%-toolbarmsg-label"></div><div style="height:0;overflow:hidden;clear:both;"></div></div><div id="##_message_holder" class="%%-messageholder"></div></div><div id="##_iframeholder" class="%%-iframeholder"></div><div id="##_bottombar" class="%%-bottomContainer"><table><tr><td id="##_elementpath" class="%%-bottombar"></td><td id="##_wordcount" class="%%-wordcount"></td><td id="##_scale" class="%%-scale"><div class="%%-icon"></div></td></tr></table></div><div id="##_scalelayer"></div></div>'},showWordImageDialog:function(){this._dialogs.wordimageDialog.open()},renderToolbarBoxHtml:function(){for(var e=[],t=0;t<this.toolbars.length;t++)e.push(this.toolbars[t].renderHtml());return e.join("")},setFullScreen:function(e){var t=this.editor,i=t.container.parentNode.parentNode;if(this._fullscreen!=e){if(this._fullscreen=e,this.editor.fireEvent("beforefullscreenchange",e),baidu.editor.browser.gecko)var n=t.selection.getRange().createBookmark();if(e){for(;"BODY"!=i.tagName;){var o=baidu.editor.dom.domUtils.getComputedStyle(i,"position");nodeStack.push(o),i.style.position="static",i=i.parentNode}this._bakHtmlOverflow=document.documentElement.style.overflow,this._bakBodyOverflow=document.body.style.overflow,this._bakAutoHeight=this.editor.autoHeightEnabled,this._bakScrollTop=Math.max(document.documentElement.scrollTop,document.body.scrollTop),this._bakEditorContaninerWidth=t.iframe.parentNode.offsetWidth,this._bakAutoHeight&&(t.autoHeightEnabled=!1,this.editor.disableAutoHeight()),document.documentElement.style.overflow="hidden",window.scrollTo(0,window.scrollY),this._bakCssText=this.getDom().style.cssText,this._bakCssText1=this.getDom("iframeholder").style.cssText,t.iframe.parentNode.style.width="",this._updateFullScreen()}else{for(;"BODY"!=i.tagName;)i.style.position=nodeStack.shift(),i=i.parentNode;this.getDom().style.cssText=this._bakCssText,this.getDom("iframeholder").style.cssText=this._bakCssText1,this._bakAutoHeight&&(t.autoHeightEnabled=!0,this.editor.enableAutoHeight()),document.documentElement.style.overflow=this._bakHtmlOverflow,document.body.style.overflow=this._bakBodyOverflow,t.iframe.parentNode.style.width=this._bakEditorContaninerWidth+"px",window.scrollTo(0,this._bakScrollTop)}if(browser.gecko&&"true"===t.body.contentEditable){var r=document.createElement("input");document.body.appendChild(r),t.body.contentEditable=!1,setTimeout(function(){r.focus(),setTimeout(function(){t.body.contentEditable=!0,t.fireEvent("fullscreenchanged",e),t.selection.getRange().moveToBookmark(n).select(!0),baidu.editor.dom.domUtils.remove(r),e&&window.scroll(0,0)},0)},0)}"true"===t.body.contentEditable&&(this.editor.fireEvent("fullscreenchanged",e),this.triggerLayout())}},_updateFullScreen:function(){if(this._fullscreen){var e=uiUtils.getViewportRect();if(this.getDom().style.cssText="border:0;position:absolute;left:0;top:"+(this.editor.options.topOffset||0)+"px;width:"+e.width+"px;height:"+e.height+"px;z-index:"+(1*this.getDom().style.zIndex+100),uiUtils.setViewportOffset(this.getDom(),{left:0,top:this.editor.options.topOffset||0}),this.editor.setHeight(e.height-this.getDom("toolbarbox").offsetHeight-this.getDom("bottombar").offsetHeight-(this.editor.options.topOffset||0),!0),browser.gecko)try{window.onresize()}catch(e){}}},_updateElementPath:function(){var e,t=this.getDom("elementpath");if(this.elementPathEnabled&&(e=this.editor.queryCommandValue("elementpath"))){for(var i,n=[],o=0;i=e[o];o++)n[o]=this.formatHtml('<span unselectable="on" onclick="$$.editor.execCommand(&quot;elementpath&quot;, &quot;'+o+'&quot;);">'+i+"</span>");t.innerHTML='<div class="edui-editor-breadcrumb" onmousedown="return false;">'+this.editor.getLang("elementPathTip")+": "+n.join(" &gt; ")+"</div>"}else t.style.display="none"},disableElementPath:function(){var e=this.getDom("elementpath");e.innerHTML="",e.style.display="none",this.elementPathEnabled=!1},enableElementPath:function(){this.getDom("elementpath").style.display="",this.elementPathEnabled=!0,this._updateElementPath()},_scale:function(){var e=document,t=this.editor,i=t.container,n=t.document,o=this.getDom("toolbarbox"),r=this.getDom("bottombar"),a=this.getDom("scale"),s=this.getDom("scalelayer"),l=!1,d=null,c=0,u=t.options.minFrameWidth,m=0,f=0,h=0,p=0;function g(){d=domUtils.getXY(i),c||(c=t.options.minFrameHeight+o.offsetHeight+r.offsetHeight),s.style.cssText="position:absolute;left:0;display:;top:0;background-color:#41ABFF;opacity:0.4;filter: Alpha(opacity=40);width:"+i.offsetWidth+"px;height:"+i.offsetHeight+"px;z-index:"+(t.options.zIndex+1),domUtils.on(e,"mousemove",b),domUtils.on(n,"mouseup",y),domUtils.on(e,"mouseup",y)}var v=this;function b(t){C();var i=t||window.event;m=i.pageX||e.documentElement.scrollLeft+i.clientX,f=i.pageY||e.documentElement.scrollTop+i.clientY,h=m-d.x,p=f-d.y,h>=u&&(l=!0,s.style.width=h+"px"),p>=c&&(l=!0,s.style.height=p+"px")}function y(){l&&(l=!1,t.ui._actualFrameWidth=s.offsetWidth-2,i.style.width=t.ui._actualFrameWidth+"px",t.setHeight(s.offsetHeight-r.offsetHeight-o.offsetHeight-2,!0)),s&&(s.style.display="none"),C(),domUtils.un(e,"mousemove",b),domUtils.un(n,"mouseup",y),domUtils.un(e,"mouseup",y)}function C(){browser.ie?e.selection.clear():window.getSelection().removeAllRanges()}this.editor.addListener("fullscreenchanged",function(e,t){if(t)v.disableScale();else if(v.editor.options.scaleEnabled){v.enableScale();var i=v.editor.document.createElement("span");v.editor.body.appendChild(i),v.editor.body.style.height=Math.max(domUtils.getXY(i).y,v.editor.iframe.offsetHeight-20)+"px",domUtils.remove(i)}}),this.enableScale=function(){1!=t.queryCommandState("source")&&(a.style.display="",this.scaleEnabled=!0,domUtils.on(a,"mousedown",g))},this.disableScale=function(){a.style.display="none",this.scaleEnabled=!1,domUtils.un(a,"mousedown",g)}},isFullScreen:function(){return this._fullscreen},postRender:function(){UIBase.prototype.postRender.call(this);for(var e=0;e<this.toolbars.length;e++)this.toolbars[e].postRender();var t,i=this,n=baidu.editor.dom.domUtils,o=function(){clearTimeout(t),t=setTimeout(function(){i._updateFullScreen()})};n.on(window,"resize",o),i.addListener("destroy",function(){n.un(window,"resize",o),clearTimeout(t)})},showToolbarMsg:function(e,t){(this.getDom("toolbarmsg_label").innerHTML=e,this.getDom("toolbarmsg").style.display="",t)||(this.getDom("upload_dialog").style.display="none")},hideToolbarMsg:function(){this.getDom("toolbarmsg").style.display="none"},mapUrl:function(e){return e?e.replace("~/",this.editor.options.UEDITOR_HOME_URL||""):""},triggerLayout:function(){var e=this.getDom();"1"==e.style.zoom?e.style.zoom="100%":e.style.zoom="1"}},utils.inherits(EditorUI,baidu.editor.ui.UIBase);var instances={};UE.ui.Editor=function(e){var t=new UE.Editor(e);t.options.editor=t,utils.loadFile(document,{href:t.options.themePath+t.options.theme+"/css/ueditor.css",tag:"link",type:"text/css",rel:"stylesheet"});var i=t.render;return t.render=function(e){e.constructor===String&&(t.key=e,instances[e]=t),utils.domReady(function(){function n(){if(t.setOpt({labelMap:t.options.labelMap||t.getLang("labelMap")}),new EditorUI(t.options),e&&(e.constructor===String&&(e=document.getElementById(e)),e&&e.getAttribute("name")&&(t.options.textarea=e.getAttribute("name")),e&&/script|textarea/gi.test(e.tagName))){var n=document.createElement("div");e.parentNode.insertBefore(n,e);var o=e.value||e.innerHTML;t.options.initialContent=/^[\t\r\n ]*$/.test(o)?t.options.initialContent:o.replace(/>[\n\r\t]+([ ]{4})+/g,">").replace(/[\n\r\t]+([ ]{4})+</g,"<").replace(/>[\n\r\t]+</g,"><"),e.className&&(n.className=e.className),e.style.cssText&&(n.style.cssText=e.style.cssText),/textarea/i.test(e.tagName)?(t.textarea=e,t.textarea.style.display="none"):e.parentNode.removeChild(e),e.id&&(n.id=e.id,domUtils.removeAttributes(e,"id")),(e=n).innerHTML=""}domUtils.addClass(e,"edui-"+t.options.theme),t.ui.render(e);var r=t.options;t.container=t.ui.getDom();for(var a=domUtils.findParents(e,!0),s=[],l=0;c=a[l];l++)s[l]=c.style.display,c.style.display="block";if(r.initialFrameWidth)r.minFrameWidth=r.initialFrameWidth;else{r.minFrameWidth=r.initialFrameWidth=e.offsetWidth;var d=e.style.width;/%$/.test(d)&&(r.initialFrameWidth=d)}r.initialFrameHeight?r.minFrameHeight=r.initialFrameHeight:r.initialFrameHeight=r.minFrameHeight=e.offsetHeight;var c;for(l=0;c=a[l];l++)c.style.display=s[l];e.style.height&&(e.style.height=""),t.container.style.width=r.initialFrameWidth+(/%$/.test(r.initialFrameWidth)?"":"px"),t.container.style.zIndex=r.zIndex,i.call(t,t.ui.getDom("iframeholder")),t.fireEvent("afteruiready")}t.langIsReady?n():t.addListener("langReady",n)})},t},UE.getEditor=function(e,t){var i=instances[e];return i||(i=instances[e]=new UE.ui.Editor(t)).render(e),i},UE.delEditor=function(e){var t;(t=instances[e])&&(t.key&&t.destroy(),delete instances[e])},UE.registerUI=function(e,t,i,n){utils.each(e.split(/\s+/),function(e){UE._customizeUI[e]={id:n,execFn:t,index:i}})}}(),UE.registerUI("message",function(e){var t,i=baidu.editor.ui.Message,n=[],o=e;function r(){var e=o.ui.getDom("toolbarbox");e&&(t.style.top=e.offsetHeight+3+"px"),t.style.zIndex=Math.max(o.options.zIndex,o.iframe.style.zIndex)+1}o.addListener("ready",function(){t=document.getElementById(o.ui.id+"_message_holder"),r(),setTimeout(function(){r()},500)}),o.addListener("showmessage",function(e,a){a=utils.isString(a)?{content:a}:a;var s=new i({timeout:a.timeout,type:a.type,content:a.content,keepshow:a.keepshow,editor:o}),l=a.id||"msg_"+(+new Date).toString(36);return s.render(t),n[l]=s,s.reset(a),r(),l}),o.addListener("updatemessage",function(e,i,o){o=utils.isString(o)?{content:o}:o;var r=n[i];r.render(t),r&&r.reset(o)}),o.addListener("hidemessage",function(e,t){var i=n[t];i&&i.hide()})}),UE.registerUI("autosave",function(e){e.on("afterautosave",function(){})})}();