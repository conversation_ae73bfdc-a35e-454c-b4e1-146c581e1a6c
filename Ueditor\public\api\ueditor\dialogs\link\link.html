<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" src="../internal.js"></script>
    <style type="text/css">
        *{margin:0;padding:0;color: #838383;}
        table{font-size: 12px;margin: 10px;line-height: 30px}
        .txt{width:100%;height:21px;line-height:21px;border:1px solid #c2cad8;
            padding: 3px 0px;
            border-radius: 4px;}
        .xrtable {
            padding: 10px;
        }
        .xr-table-tr {
            margin: 10px 0;
        }
        .xr-table-td {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="xrtable">
        <div class="xr-table-tr">
            <div class="xr-table-td"><label for="text"> <var id="lang_input_text"></var></label></div>
            <div class="xr-table-td"><input class="txt" id="text" type="text" disabled="true"/></div>
        </div>
        <div class="xr-table-tr">
            <div class="xr-table-td"><label for="href"> <var id="lang_input_url"></var></label></div>
            <div class="xr-table-td"><input class="txt" id="href" type="text" /></div>
        </div>
        <div class="xr-table-tr">
            <div class="xr-table-td"><label for="title"> <var id="lang_input_title"></var></label></div>
            <div class="xr-table-td"><input class="txt" id="title" type="text"/></div>
        </div>
        <div class="xr-table-tr">
             <div class="xr-table-td" >
                 <label for="target"><var id="lang_input_target"></var></label>
                 <input id="target" type="checkbox"/>
             </div>
        </div>
        <div class="xr-table-tr">
            <div  class="xr-table-td" id="msg"></div>
        </div>
    </div>
<script type="text/javascript">
    var range = editor.selection.getRange(),
        link = range.collapsed ? editor.queryCommandValue( "link" ) : editor.selection.getStart(),
        url,
        text = $G('text'),
        rangeLink = domUtils.findParentByTagName(range.getCommonAncestor(),'a',true),
        orgText;
    link = domUtils.findParentByTagName( link, "a", true );
    if(link){
        url = utils.html(link.getAttribute( '_href' ) || link.getAttribute( 'href', 2 ));

        if(rangeLink === link && !link.getElementsByTagName('img').length){
            text.removeAttribute('disabled');
            orgText = text.value = link[browser.ie ? 'innerText':'textContent'];
        }else{
            text.setAttribute('disabled','true');
            text.value = lang.validLink;
        }

    }else{
        if(range.collapsed){
            text.removeAttribute('disabled');
            text.value = '';
        }else{
            text.setAttribute('disabled','true');
            text.value = lang.validLink;
        }

    }
    $G("title").value = url ? link.title : "";
    $G("href").value = url ? url: '';
    $G("target").checked = url && link.target == "_blank" ? true :  false;
    $focus($G("href"));

    function handleDialogOk(){
        var href =$G('href').value.replace(/^\s+|\s+$/g, '');
        if(href){
            if(!hrefStartWith(href,["http","/","ftp://",'#'])) {
                href  = "http://" + href;
            }
            var obj = {
                'href' : href,
                'target' : $G("target").checked ? "_blank" : '_self',
                'title' : $G("title").value.replace(/^\s+|\s+$/g, ''),
                '_href':href
            };
            //修改链接内容的情况太特殊了，所以先做到这里了
            //todo:情况多的时候，做到command里
            if(orgText && text.value != orgText){
                link[browser.ie ? 'innerText' : 'textContent'] =  obj.textValue = text.value;
                range.selectNode(link).select()
            }
            if(range.collapsed){
                obj.textValue = text.value;
            }
            editor.execCommand('link',utils.clearEmptyAttrs(obj) );
            dialog.close();
        }
    }
    dialog.onok = handleDialogOk;
    $G('href').onkeydown = $G('title').onkeydown = function(evt){
        evt = evt || window.event;
        if (evt.keyCode == 13) {
            handleDialogOk();
            return false;
        }
    };
    $G('href').onblur = function(){
        if(!hrefStartWith(this.value,["http","/","ftp://",'#'])){
            $G("msg").innerHTML = "<span style='color: red'>"+lang.httpPrompt+"</span>";
        }else{
            $G("msg").innerHTML = "";
        }
    };

    function hrefStartWith(href,arr){
        href = href.replace(/^\s+|\s+$/g, '');
        for(var i=0,ai;ai=arr[i++];){
            if(href.indexOf(ai)==0){
                return true;
            }
        }
        return false;
    }


</script>
</body>
</html>
