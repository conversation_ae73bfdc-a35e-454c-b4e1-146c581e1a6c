/*! jQuery UI - v1.10.4 - 2023-04-10
* http://jqueryui.com
* Includes: jquery.ui.core.js, jquery.ui.widget.js, jquery.ui.mouse.js, jquery.ui.draggable.js, jquery.ui.droppable.js, jquery.ui.resizable.js, jquery.ui.selectable.js, jquery.ui.sortable.js
* Copyright jQuery Foundation and other contributors; Licensed MIT */

!function(a,u){var t,e,n=0,i=/^ui-id-\d+$/;function s(t,e){var n,i,s=t.nodeName.toLowerCase();return"area"===s?(i=(n=t.parentNode).name,!(!t.href||!i||"map"!==n.nodeName.toLowerCase())&&(!!(i=a("img[usemap=#"+i+"]")[0])&&r(i))):(/input|select|textarea|button|object/.test(s)?!t.disabled:"a"===s&&t.href||e)&&r(t)}function r(t){return a.expr.filters.visible(t)&&!a(t).parents().addBack().filter(function(){return"hidden"===a.css(this,"visibility")}).length}a.ui=a.ui||{},a.extend(a.ui,{version:"1.10.4",keyCode:{BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,NUMPAD_ADD:107,NUMPAD_DECIMAL:110,NUMPAD_DIVIDE:111,NUMPAD_ENTER:108,NUMPAD_MULTIPLY:106,NUMPAD_SUBTRACT:109,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38}}),a.fn.extend({focus:(t=a.fn.focus,function(e,n){return"number"==typeof e?this.each(function(){var t=this;setTimeout(function(){a(t).focus(),n&&n.call(t)},e)}):t.apply(this,arguments)}),scrollParent:function(){var t=(a.ui.ie&&/(static|relative)/.test(this.css("position"))||/absolute/.test(this.css("position"))?this.parents().filter(function(){return/(relative|absolute|fixed)/.test(a.css(this,"position"))&&/(auto|scroll)/.test(a.css(this,"overflow")+a.css(this,"overflow-y")+a.css(this,"overflow-x"))}):this.parents().filter(function(){return/(auto|scroll)/.test(a.css(this,"overflow")+a.css(this,"overflow-y")+a.css(this,"overflow-x"))})).eq(0);return/fixed/.test(this.css("position"))||!t.length?a(document):t},zIndex:function(t){if(t!==u)return this.css("zIndex",t);if(this.length)for(var e,n,i=a(this[0]);i.length&&i[0]!==document;){if(("absolute"===(e=i.css("position"))||"relative"===e||"fixed"===e)&&(n=parseInt(i.css("zIndex"),10),!isNaN(n)&&0!==n))return n;i=i.parent()}return 0},uniqueId:function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++n)})},removeUniqueId:function(){return this.each(function(){i.test(this.id)&&a(this).removeAttr("id")})}}),a.extend(a.expr[":"],{data:a.expr.createPseudo?a.expr.createPseudo(function(e){return function(t){return!!a.data(t,e)}}):function(t,e,n){return!!a.data(t,n[3])},focusable:function(t){return s(t,!isNaN(a.attr(t,"tabindex")))},tabbable:function(t){var e=a.attr(t,"tabindex"),n=isNaN(e);return(n||0<=e)&&s(t,!n)}}),a("<a>").outerWidth(1).jquery||a.each(["Width","Height"],function(t,n){var s="Width"===n?["Left","Right"]:["Top","Bottom"],i=n.toLowerCase(),r={innerWidth:a.fn.innerWidth,innerHeight:a.fn.innerHeight,outerWidth:a.fn.outerWidth,outerHeight:a.fn.outerHeight};function o(t,e,n,i){return a.each(s,function(){e-=parseFloat(a.css(t,"padding"+this))||0,n&&(e-=parseFloat(a.css(t,"border"+this+"Width"))||0),i&&(e-=parseFloat(a.css(t,"margin"+this))||0)}),e}a.fn["inner"+n]=function(t){return t===u?r["inner"+n].call(this):this.each(function(){a(this).css(i,o(this,t)+"px")})},a.fn["outer"+n]=function(t,e){return"number"!=typeof t?r["outer"+n].call(this,t):this.each(function(){a(this).css(i,o(this,t,!0,e)+"px")})}}),a.fn.addBack||(a.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),a("<a>").data("a-b","a").removeData("a-b").data("a-b")&&(a.fn.removeData=(e=a.fn.removeData,function(t){return arguments.length?e.call(this,a.camelCase(t)):e.call(this)})),a.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase()),a.support.selectstart="onselectstart"in document.createElement("div"),a.fn.extend({disableSelection:function(){return this.bind((a.support.selectstart?"selectstart":"mousedown")+".ui-disableSelection",function(t){t.preventDefault()})},enableSelection:function(){return this.unbind(".ui-disableSelection")}}),a.extend(a.ui,{plugin:{add:function(t,e,n){var i,s=a.ui[t].prototype;for(i in n)s.plugins[i]=s.plugins[i]||[],s.plugins[i].push([e,n[i]])},call:function(t,e,n){var i,s=t.plugins[e];if(s&&t.element[0].parentNode&&11!==t.element[0].parentNode.nodeType)for(i=0;i<s.length;i++)t.options[s[i][0]]&&s[i][1].apply(t.element,n)}},hasScroll:function(t,e){if("hidden"===a(t).css("overflow"))return!1;var n=e&&"left"===e?"scrollLeft":"scrollTop",e=!1;return 0<t[n]||(t[n]=1,e=0<t[n],t[n]=0,e)}})}(jQuery);!function(d,r){var i=0,u=Array.prototype.slice,n=d.cleanData;d.cleanData=function(t){for(var e,i=0;null!=(e=t[i]);i++)try{d(e).triggerHandler("remove")}catch(t){}n(t)},d.widget=function(t,i,e){var n,s,o,a,r={},u=t.split(".")[0];t=t.split(".")[1],n=u+"-"+t,e||(e=i,i=d.Widget),d.expr[":"][n.toLowerCase()]=function(t){return!!d.data(t,n)},d[u]=d[u]||{},s=d[u][t],o=d[u][t]=function(t,e){if(!this._createWidget)return new o(t,e);arguments.length&&this._createWidget(t,e)},d.extend(o,s,{version:e.version,_proto:d.extend({},e),_childConstructors:[]}),(a=new i).options=d.widget.extend({},a.options),d.each(e,function(e,n){function s(){return i.prototype[e].apply(this,arguments)}function o(t){return i.prototype[e].apply(this,t)}d.isFunction(n)?r[e]=function(){var t,e=this._super,i=this._superApply;return this._super=s,this._superApply=o,t=n.apply(this,arguments),this._super=e,this._superApply=i,t}:r[e]=n}),o.prototype=d.widget.extend(a,{widgetEventPrefix:s&&a.widgetEventPrefix||t},r,{constructor:o,namespace:u,widgetName:t,widgetFullName:n}),s?(d.each(s._childConstructors,function(t,e){var i=e.prototype;d.widget(i.namespace+"."+i.widgetName,o,e._proto)}),delete s._childConstructors):i._childConstructors.push(o),d.widget.bridge(t,o)},d.widget.extend=function(t){for(var e,i,n=u.call(arguments,1),s=0,o=n.length;s<o;s++)for(e in n[s])i=n[s][e],n[s].hasOwnProperty(e)&&i!==r&&(d.isPlainObject(i)?t[e]=d.isPlainObject(t[e])?d.widget.extend({},t[e],i):d.widget.extend({},i):t[e]=i);return t},d.widget.bridge=function(o,e){var a=e.prototype.widgetFullName||o;d.fn[o]=function(i){var t="string"==typeof i,n=u.call(arguments,1),s=this;return i=!t&&n.length?d.widget.extend.apply(null,[i].concat(n)):i,t?this.each(function(){var t,e=d.data(this,a);return e?d.isFunction(e[i])&&"_"!==i.charAt(0)?(t=e[i].apply(e,n))!==e&&t!==r?(s=t&&t.jquery?s.pushStack(t.get()):t,!1):void 0:d.error("no such method '"+i+"' for "+o+" widget instance"):d.error("cannot call methods on "+o+" prior to initialization; attempted to call method '"+i+"'")}):this.each(function(){var t=d.data(this,a);t?t.option(i||{})._init():d.data(this,a,new e(i,this))}),s}},d.Widget=function(){},d.Widget._childConstructors=[],d.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{disabled:!1,create:null},_createWidget:function(t,e){e=d(e||this.defaultElement||this)[0],this.element=d(e),this.uuid=i++,this.eventNamespace="."+this.widgetName+this.uuid,this.options=d.widget.extend({},this.options,this._getCreateOptions(),t),this.bindings=d(),this.hoverable=d(),this.focusable=d(),e!==this&&(d.data(e,this.widgetFullName,this),this._on(!0,this.element,{remove:function(t){t.target===e&&this.destroy()}}),this.document=d(e.style?e.ownerDocument:e.document||e),this.window=d(this.document[0].defaultView||this.document[0].parentWindow)),this._create(),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:d.noop,_getCreateEventData:d.noop,_create:d.noop,_init:d.noop,destroy:function(){this._destroy(),this.element.unbind(this.eventNamespace).removeData(this.widgetName).removeData(this.widgetFullName).removeData(d.camelCase(this.widgetFullName)),this.widget().unbind(this.eventNamespace).removeAttr("aria-disabled").removeClass(this.widgetFullName+"-disabled ui-state-disabled"),this.bindings.unbind(this.eventNamespace),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")},_destroy:d.noop,widget:function(){return this.element},option:function(t,e){var i,n,s,o=t;if(0===arguments.length)return d.widget.extend({},this.options);if("string"==typeof t)if(o={},t=(i=t.split(".")).shift(),i.length){for(n=o[t]=d.widget.extend({},this.options[t]),s=0;s<i.length-1;s++)n[i[s]]=n[i[s]]||{},n=n[i[s]];if(t=i.pop(),1===arguments.length)return n[t]===r?null:n[t];n[t]=e}else{if(1===arguments.length)return this.options[t]===r?null:this.options[t];o[t]=e}return this._setOptions(o),this},_setOptions:function(t){for(var e in t)this._setOption(e,t[e]);return this},_setOption:function(t,e){return this.options[t]=e,"disabled"===t&&(this.widget().toggleClass(this.widgetFullName+"-disabled ui-state-disabled",!!e).attr("aria-disabled",e),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")),this},enable:function(){return this._setOption("disabled",!1)},disable:function(){return this._setOption("disabled",!0)},_on:function(s,o,t){var a,r=this;"boolean"!=typeof s&&(t=o,o=s,s=!1),t?(o=a=d(o),this.bindings=this.bindings.add(o)):(t=o,o=this.element,a=this.widget()),d.each(t,function(t,e){function i(){if(s||!0!==r.options.disabled&&!d(this).hasClass("ui-state-disabled"))return("string"==typeof e?r[e]:e).apply(r,arguments)}"string"!=typeof e&&(i.guid=e.guid=e.guid||i.guid||d.guid++);var n=t.match(/^(\w+)\s*(.*)$/),t=n[1]+r.eventNamespace,n=n[2];n?a.delegate(n,t,i):o.bind(t,i)})},_off:function(t,e){e=(e||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,t.unbind(e).undelegate(e)},_delay:function(t,e){var i=this;return setTimeout(function(){return("string"==typeof t?i[t]:t).apply(i,arguments)},e||0)},_hoverable:function(t){this.hoverable=this.hoverable.add(t),this._on(t,{mouseenter:function(t){d(t.currentTarget).addClass("ui-state-hover")},mouseleave:function(t){d(t.currentTarget).removeClass("ui-state-hover")}})},_focusable:function(t){this.focusable=this.focusable.add(t),this._on(t,{focusin:function(t){d(t.currentTarget).addClass("ui-state-focus")},focusout:function(t){d(t.currentTarget).removeClass("ui-state-focus")}})},_trigger:function(t,e,i){var n,s,o=this.options[t];if(i=i||{},(e=d.Event(e)).type=(t===this.widgetEventPrefix?t:this.widgetEventPrefix+t).toLowerCase(),e.target=this.element[0],s=e.originalEvent)for(n in s)n in e||(e[n]=s[n]);return this.element.trigger(e,i),!(d.isFunction(o)&&!1===o.apply(this.element[0],[e].concat(i))||e.isDefaultPrevented())}},d.each({show:"fadeIn",hide:"fadeOut"},function(o,a){d.Widget.prototype["_"+o]=function(e,t,i){var n=(t="string"==typeof t?{effect:t}:t)?!0!==t&&"number"!=typeof t&&t.effect||a:o,s=!d.isEmptyObject(t="number"==typeof(t=t||{})?{duration:t}:t);t.complete=i,t.delay&&e.delay(t.delay),s&&d.effects&&d.effects.effect[n]?e[o](t):n!==o&&e[n]?e[n](t.duration,t.easing,i):e.queue(function(t){d(this)[o](),i&&i.call(e[0]),t()})}})}(jQuery);!function(i){var u=!1;i(document).mouseup(function(){u=!1}),i.widget("ui.mouse",{version:"1.10.4",options:{cancel:"input,textarea,button,select,option",distance:1,delay:0},_mouseInit:function(){var t=this;this.element.bind("mousedown."+this.widgetName,function(e){return t._mouseDown(e)}).bind("click."+this.widgetName,function(e){if(!0===i.data(e.target,t.widgetName+".preventClickEvent"))return i.removeData(e.target,t.widgetName+".preventClickEvent"),e.stopImmediatePropagation(),!1}),this.started=!1},_mouseDestroy:function(){this.element.unbind("."+this.widgetName),this._mouseMoveDelegate&&i(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(e){if(!u){this._mouseStarted&&this._mouseUp(e),this._mouseDownEvent=e;var t=this,s=1===e.which,o=!("string"!=typeof this.options.cancel||!e.target.nodeName)&&i(e.target).closest(this.options.cancel).length;return s&&!o&&this._mouseCapture(e)?(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){t.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(e),!this._mouseStarted)?(e.preventDefault(),!0):(!0===i.data(e.target,this.widgetName+".preventClickEvent")&&i.removeData(e.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(e){return t._mouseMove(e)},this._mouseUpDelegate=function(e){return t._mouseUp(e)},i(document).bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+this.widgetName,this._mouseUpDelegate),e.preventDefault(),u=!0)):!0}},_mouseMove:function(e){return i.ui.ie&&(!document.documentMode||document.documentMode<9)&&!e.button?this._mouseUp(e):this._mouseStarted?(this._mouseDrag(e),e.preventDefault()):(this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,e),this._mouseStarted?this._mouseDrag(e):this._mouseUp(e)),!this._mouseStarted)},_mouseUp:function(e){return i(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,e.target===this._mouseDownEvent.target&&i.data(e.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(e)),!1},_mouseDistanceMet:function(e){return Math.max(Math.abs(this._mouseDownEvent.pageX-e.pageX),Math.abs(this._mouseDownEvent.pageY-e.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}})}(jQuery);!function(b){b.widget("ui.draggable",b.ui.mouse,{version:"1.10.4",widgetEventPrefix:"drag",options:{addClasses:!0,appendTo:"parent",axis:!1,connectToSortable:!1,containment:!1,cursor:"auto",cursorAt:!1,grid:!1,handle:!1,helper:"original",iframeFix:!1,opacity:!1,refreshPositions:!1,revert:!1,revertDuration:500,scope:"default",scroll:!0,scrollSensitivity:20,scrollSpeed:20,snap:!1,snapMode:"both",snapTolerance:20,stack:!1,zIndex:!1,drag:null,start:null,stop:null},_create:function(){"original"!==this.options.helper||/^(?:r|a|f)/.test(this.element.css("position"))||(this.element[0].style.position="relative"),this.options.addClasses&&this.element.addClass("ui-draggable"),this.options.disabled&&this.element.addClass("ui-draggable-disabled"),this._mouseInit()},_destroy:function(){this.element.removeClass("ui-draggable ui-draggable-dragging ui-draggable-disabled"),this._mouseDestroy()},_mouseCapture:function(t){var e=this.options;return!(this.helper||e.disabled||0<b(t.target).closest(".ui-resizable-handle").length)&&(this.handle=this._getHandle(t),!!this.handle&&(b(!0===e.iframeFix?"iframe":e.iframeFix).each(function(){b("<div class='ui-draggable-iframeFix' style='background: #fff;'></div>").css({width:this.offsetWidth+"px",height:this.offsetHeight+"px",position:"absolute",opacity:"0.001",zIndex:1e3}).css(b(this).offset()).appendTo("body")}),!0))},_mouseStart:function(t){var e=this.options;return this.helper=this._createHelper(t),this.helper.addClass("ui-draggable-dragging"),this._cacheHelperProportions(),b.ui.ddmanager&&(b.ui.ddmanager.current=this),this._cacheMargins(),this.cssPosition=this.helper.css("position"),this.scrollParent=this.helper.scrollParent(),this.offsetParent=this.helper.offsetParent(),this.offsetParentCssPosition=this.offsetParent.css("position"),this.offset=this.positionAbs=this.element.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},this.offset.scroll=!1,b.extend(this.offset,{click:{left:t.pageX-this.offset.left,top:t.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()}),this.originalPosition=this.position=this._generatePosition(t),this.originalPageX=t.pageX,this.originalPageY=t.pageY,e.cursorAt&&this._adjustOffsetFromHelper(e.cursorAt),this._setContainment(),!1===this._trigger("start",t)?(this._clear(),!1):(this._cacheHelperProportions(),b.ui.ddmanager&&!e.dropBehaviour&&b.ui.ddmanager.prepareOffsets(this,t),this._mouseDrag(t,!0),b.ui.ddmanager&&b.ui.ddmanager.dragStart(this,t),!0)},_mouseDrag:function(t,e){if("fixed"===this.offsetParentCssPosition&&(this.offset.parent=this._getParentOffset()),this.position=this._generatePosition(t),this.positionAbs=this._convertPositionTo("absolute"),!e){e=this._uiHash();if(!1===this._trigger("drag",t,e))return this._mouseUp({}),!1;this.position=e.position}return this.options.axis&&"y"===this.options.axis||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&"x"===this.options.axis||(this.helper[0].style.top=this.position.top+"px"),b.ui.ddmanager&&b.ui.ddmanager.drag(this,t),!1},_mouseStop:function(t){var e=this,s=!1;return b.ui.ddmanager&&!this.options.dropBehaviour&&(s=b.ui.ddmanager.drop(this,t)),this.dropped&&(s=this.dropped,this.dropped=!1),("original"!==this.options.helper||b.contains(this.element[0].ownerDocument,this.element[0]))&&("invalid"===this.options.revert&&!s||"valid"===this.options.revert&&s||!0===this.options.revert||b.isFunction(this.options.revert)&&this.options.revert.call(this.element,s)?b(this.helper).animate(this.originalPosition,parseInt(this.options.revertDuration,10),function(){!1!==e._trigger("stop",t)&&e._clear()}):!1!==this._trigger("stop",t)&&this._clear()),!1},_mouseUp:function(t){return b("div.ui-draggable-iframeFix").each(function(){this.parentNode.removeChild(this)}),b.ui.ddmanager&&b.ui.ddmanager.dragStop(this,t),b.ui.mouse.prototype._mouseUp.call(this,t)},cancel:function(){return this.helper.is(".ui-draggable-dragging")?this._mouseUp({}):this._clear(),this},_getHandle:function(t){return!this.options.handle||!!b(t.target).closest(this.element.find(this.options.handle)).length},_createHelper:function(t){var e=this.options,t=b.isFunction(e.helper)?b(e.helper.apply(this.element[0],[t])):"clone"===e.helper?this.element.clone().removeAttr("id"):this.element;return t.parents("body").length||t.appendTo("parent"===e.appendTo?this.element[0].parentNode:e.appendTo),t[0]===this.element[0]||/(fixed|absolute)/.test(t.css("position"))||t.css("position","absolute"),t},_adjustOffsetFromHelper:function(t){"string"==typeof t&&(t=t.split(" ")),"left"in(t=b.isArray(t)?{left:+t[0],top:+t[1]||0}:t)&&(this.offset.click.left=t.left+this.margins.left),"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left),"top"in t&&(this.offset.click.top=t.top+this.margins.top),"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_getParentOffset:function(){var t=this.offsetParent.offset();return"absolute"===this.cssPosition&&this.scrollParent[0]!==document&&b.contains(this.scrollParent[0],this.offsetParent[0])&&(t.left+=this.scrollParent.scrollLeft(),t.top+=this.scrollParent.scrollTop()),{top:(t=this.offsetParent[0]===document.body||this.offsetParent[0].tagName&&"html"===this.offsetParent[0].tagName.toLowerCase()&&b.ui.ie?{top:0,left:0}:t).top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:t.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"!==this.cssPosition)return{top:0,left:0};var t=this.element.position();return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:t.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}},_cacheMargins:function(){this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var t,e,s,i=this.options;i.containment?"window"!==i.containment?"document"!==i.containment?i.containment.constructor!==Array?("parent"===i.containment&&(i.containment=this.helper[0].parentNode),(s=(e=b(i.containment))[0])&&(t="hidden"!==e.css("overflow"),this.containment=[(parseInt(e.css("borderLeftWidth"),10)||0)+(parseInt(e.css("paddingLeft"),10)||0),(parseInt(e.css("borderTopWidth"),10)||0)+(parseInt(e.css("paddingTop"),10)||0),(t?Math.max(s.scrollWidth,s.offsetWidth):s.offsetWidth)-(parseInt(e.css("borderRightWidth"),10)||0)-(parseInt(e.css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left-this.margins.right,(t?Math.max(s.scrollHeight,s.offsetHeight):s.offsetHeight)-(parseInt(e.css("borderBottomWidth"),10)||0)-(parseInt(e.css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top-this.margins.bottom],this.relative_container=e)):this.containment=i.containment:this.containment=[0,0,b(document).width()-this.helperProportions.width-this.margins.left,(b(document).height()||document.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]:this.containment=[b(window).scrollLeft()-this.offset.relative.left-this.offset.parent.left,b(window).scrollTop()-this.offset.relative.top-this.offset.parent.top,b(window).scrollLeft()+b(window).width()-this.helperProportions.width-this.margins.left,b(window).scrollTop()+(b(window).height()||document.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]:this.containment=null},_convertPositionTo:function(t,e){e=e||this.position;var s="absolute"===t?1:-1,t="absolute"!==this.cssPosition||this.scrollParent[0]!==document&&b.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent;return this.offset.scroll||(this.offset.scroll={top:t.scrollTop(),left:t.scrollLeft()}),{top:e.top+this.offset.relative.top*s+this.offset.parent.top*s-("fixed"===this.cssPosition?-this.scrollParent.scrollTop():this.offset.scroll.top)*s,left:e.left+this.offset.relative.left*s+this.offset.parent.left*s-("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():this.offset.scroll.left)*s}},_generatePosition:function(t){var e,s=this.options,i="absolute"!==this.cssPosition||this.scrollParent[0]!==document&&b.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,o=t.pageX,n=t.pageY;return this.offset.scroll||(this.offset.scroll={top:i.scrollTop(),left:i.scrollLeft()}),this.originalPosition&&(this.containment&&(e=this.relative_container?(e=this.relative_container.offset(),[this.containment[0]+e.left,this.containment[1]+e.top,this.containment[2]+e.left,this.containment[3]+e.top]):this.containment,t.pageX-this.offset.click.left<e[0]&&(o=e[0]+this.offset.click.left),t.pageY-this.offset.click.top<e[1]&&(n=e[1]+this.offset.click.top),t.pageX-this.offset.click.left>e[2]&&(o=e[2]+this.offset.click.left),t.pageY-this.offset.click.top>e[3]&&(n=e[3]+this.offset.click.top)),s.grid&&(t=s.grid[1]?this.originalPageY+Math.round((n-this.originalPageY)/s.grid[1])*s.grid[1]:this.originalPageY,n=!e||t-this.offset.click.top>=e[1]||t-this.offset.click.top>e[3]?t:t-this.offset.click.top>=e[1]?t-s.grid[1]:t+s.grid[1],t=s.grid[0]?this.originalPageX+Math.round((o-this.originalPageX)/s.grid[0])*s.grid[0]:this.originalPageX,o=!e||t-this.offset.click.left>=e[0]||t-this.offset.click.left>e[2]?t:t-this.offset.click.left>=e[0]?t-s.grid[0]:t+s.grid[0])),{top:n-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.scrollParent.scrollTop():this.offset.scroll.top),left:o-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():this.offset.scroll.left)}},_clear:function(){this.helper.removeClass("ui-draggable-dragging"),this.helper[0]===this.element[0]||this.cancelHelperRemoval||this.helper.remove(),this.helper=null,this.cancelHelperRemoval=!1},_trigger:function(t,e,s){return s=s||this._uiHash(),b.ui.plugin.call(this,t,[e,s]),"drag"===t&&(this.positionAbs=this._convertPositionTo("absolute")),b.Widget.prototype._trigger.call(this,t,e,s)},plugins:{},_uiHash:function(){return{helper:this.helper,position:this.position,originalPosition:this.originalPosition,offset:this.positionAbs}}}),b.ui.plugin.add("draggable","connectToSortable",{start:function(e,t){var s=b(this).data("ui-draggable"),i=s.options,o=b.extend({},t,{item:s.element});s.sortables=[],b(i.connectToSortable).each(function(){var t=b.data(this,"ui-sortable");t&&!t.options.disabled&&(s.sortables.push({instance:t,shouldRevert:t.options.revert}),t.refreshPositions(),t._trigger("activate",e,o))})},stop:function(t,e){var s=b(this).data("ui-draggable"),i=b.extend({},e,{item:s.element});b.each(s.sortables,function(){this.instance.isOver?(this.instance.isOver=0,s.cancelHelperRemoval=!0,this.instance.cancelHelperRemoval=!1,this.shouldRevert&&(this.instance.options.revert=this.shouldRevert),this.instance._mouseStop(t),this.instance.options.helper=this.instance.options._helper,"original"===s.options.helper&&this.instance.currentItem.css({top:"auto",left:"auto"})):(this.instance.cancelHelperRemoval=!1,this.instance._trigger("deactivate",t,i))})},drag:function(s,i){var o=b(this).data("ui-draggable"),n=this;b.each(o.sortables,function(){var t=!1,e=this;this.instance.positionAbs=o.positionAbs,this.instance.helperProportions=o.helperProportions,this.instance.offset.click=o.offset.click,this.instance._intersectsWith(this.instance.containerCache)&&(t=!0,b.each(o.sortables,function(){return this.instance.positionAbs=o.positionAbs,this.instance.helperProportions=o.helperProportions,this.instance.offset.click=o.offset.click,t=this!==e&&this.instance._intersectsWith(this.instance.containerCache)&&b.contains(e.instance.element[0],this.instance.element[0])?!1:t})),t?(this.instance.isOver||(this.instance.isOver=1,this.instance.currentItem=b(n).clone().removeAttr("id").appendTo(this.instance.element).data("ui-sortable-item",!0),this.instance.options._helper=this.instance.options.helper,this.instance.options.helper=function(){return i.helper[0]},s.target=this.instance.currentItem[0],this.instance._mouseCapture(s,!0),this.instance._mouseStart(s,!0,!0),this.instance.offset.click.top=o.offset.click.top,this.instance.offset.click.left=o.offset.click.left,this.instance.offset.parent.left-=o.offset.parent.left-this.instance.offset.parent.left,this.instance.offset.parent.top-=o.offset.parent.top-this.instance.offset.parent.top,o._trigger("toSortable",s),o.dropped=this.instance.element,o.currentItem=o.element,this.instance.fromOutside=o),this.instance.currentItem&&this.instance._mouseDrag(s)):this.instance.isOver&&(this.instance.isOver=0,this.instance.cancelHelperRemoval=!0,this.instance.options.revert=!1,this.instance._trigger("out",s,this.instance._uiHash(this.instance)),this.instance._mouseStop(s,!0),this.instance.options.helper=this.instance.options._helper,this.instance.currentItem.remove(),this.instance.placeholder&&this.instance.placeholder.remove(),o._trigger("fromSortable",s),o.dropped=!1)})}}),b.ui.plugin.add("draggable","cursor",{start:function(){var t=b("body"),e=b(this).data("ui-draggable").options;t.css("cursor")&&(e._cursor=t.css("cursor")),t.css("cursor",e.cursor)},stop:function(){var t=b(this).data("ui-draggable").options;t._cursor&&b("body").css("cursor",t._cursor)}}),b.ui.plugin.add("draggable","opacity",{start:function(t,e){var s=b(e.helper),e=b(this).data("ui-draggable").options;s.css("opacity")&&(e._opacity=s.css("opacity")),s.css("opacity",e.opacity)},stop:function(t,e){var s=b(this).data("ui-draggable").options;s._opacity&&b(e.helper).css("opacity",s._opacity)}}),b.ui.plugin.add("draggable","scroll",{start:function(){var t=b(this).data("ui-draggable");t.scrollParent[0]!==document&&"HTML"!==t.scrollParent[0].tagName&&(t.overflowOffset=t.scrollParent.offset())},drag:function(t){var e=b(this).data("ui-draggable"),s=e.options,i=!1;e.scrollParent[0]!==document&&"HTML"!==e.scrollParent[0].tagName?(s.axis&&"x"===s.axis||(e.overflowOffset.top+e.scrollParent[0].offsetHeight-t.pageY<s.scrollSensitivity?e.scrollParent[0].scrollTop=i=e.scrollParent[0].scrollTop+s.scrollSpeed:t.pageY-e.overflowOffset.top<s.scrollSensitivity&&(e.scrollParent[0].scrollTop=i=e.scrollParent[0].scrollTop-s.scrollSpeed)),s.axis&&"y"===s.axis||(e.overflowOffset.left+e.scrollParent[0].offsetWidth-t.pageX<s.scrollSensitivity?e.scrollParent[0].scrollLeft=i=e.scrollParent[0].scrollLeft+s.scrollSpeed:t.pageX-e.overflowOffset.left<s.scrollSensitivity&&(e.scrollParent[0].scrollLeft=i=e.scrollParent[0].scrollLeft-s.scrollSpeed))):(s.axis&&"x"===s.axis||(t.pageY-b(document).scrollTop()<s.scrollSensitivity?i=b(document).scrollTop(b(document).scrollTop()-s.scrollSpeed):b(window).height()-(t.pageY-b(document).scrollTop())<s.scrollSensitivity&&(i=b(document).scrollTop(b(document).scrollTop()+s.scrollSpeed))),s.axis&&"y"===s.axis||(t.pageX-b(document).scrollLeft()<s.scrollSensitivity?i=b(document).scrollLeft(b(document).scrollLeft()-s.scrollSpeed):b(window).width()-(t.pageX-b(document).scrollLeft())<s.scrollSensitivity&&(i=b(document).scrollLeft(b(document).scrollLeft()+s.scrollSpeed)))),!1!==i&&b.ui.ddmanager&&!s.dropBehaviour&&b.ui.ddmanager.prepareOffsets(e,t)}}),b.ui.plugin.add("draggable","snap",{start:function(){var s=b(this).data("ui-draggable"),t=s.options;s.snapElements=[],b(t.snap.constructor!==String?t.snap.items||":data(ui-draggable)":t.snap).each(function(){var t=b(this),e=t.offset();this!==s.element[0]&&s.snapElements.push({item:this,width:t.outerWidth(),height:t.outerHeight(),top:e.top,left:e.left})})},drag:function(t,e){for(var s,i,o,n,r,a,l,h,c,p=b(this).data("ui-draggable"),f=p.options,d=f.snapTolerance,g=e.offset.left,u=g+p.helperProportions.width,m=e.offset.top,v=m+p.helperProportions.height,P=p.snapElements.length-1;0<=P;P--)a=(r=p.snapElements[P].left)+p.snapElements[P].width,h=(l=p.snapElements[P].top)+p.snapElements[P].height,u<r-d||a+d<g||v<l-d||h+d<m||!b.contains(p.snapElements[P].item.ownerDocument,p.snapElements[P].item)?(p.snapElements[P].snapping&&p.options.snap.release&&p.options.snap.release.call(p.element,t,b.extend(p._uiHash(),{snapItem:p.snapElements[P].item})),p.snapElements[P].snapping=!1):("inner"!==f.snapMode&&(s=Math.abs(l-v)<=d,i=Math.abs(h-m)<=d,o=Math.abs(r-u)<=d,n=Math.abs(a-g)<=d,s&&(e.position.top=p._convertPositionTo("relative",{top:l-p.helperProportions.height,left:0}).top-p.margins.top),i&&(e.position.top=p._convertPositionTo("relative",{top:h,left:0}).top-p.margins.top),o&&(e.position.left=p._convertPositionTo("relative",{top:0,left:r-p.helperProportions.width}).left-p.margins.left),n&&(e.position.left=p._convertPositionTo("relative",{top:0,left:a}).left-p.margins.left)),c=s||i||o||n,"outer"!==f.snapMode&&(s=Math.abs(l-m)<=d,i=Math.abs(h-v)<=d,o=Math.abs(r-g)<=d,n=Math.abs(a-u)<=d,s&&(e.position.top=p._convertPositionTo("relative",{top:l,left:0}).top-p.margins.top),i&&(e.position.top=p._convertPositionTo("relative",{top:h-p.helperProportions.height,left:0}).top-p.margins.top),o&&(e.position.left=p._convertPositionTo("relative",{top:0,left:r}).left-p.margins.left),n&&(e.position.left=p._convertPositionTo("relative",{top:0,left:a-p.helperProportions.width}).left-p.margins.left)),!p.snapElements[P].snapping&&(s||i||o||n||c)&&p.options.snap.snap&&p.options.snap.snap.call(p.element,t,b.extend(p._uiHash(),{snapItem:p.snapElements[P].item})),p.snapElements[P].snapping=s||i||o||n||c)}}),b.ui.plugin.add("draggable","stack",{start:function(){var e,t=this.data("ui-draggable").options,t=b.makeArray(b(t.stack)).sort(function(t,e){return(parseInt(b(t).css("zIndex"),10)||0)-(parseInt(b(e).css("zIndex"),10)||0)});t.length&&(e=parseInt(b(t[0]).css("zIndex"),10)||0,b(t).each(function(t){b(this).css("zIndex",e+t)}),this.css("zIndex",e+t.length))}}),b.ui.plugin.add("draggable","zIndex",{start:function(t,e){var s=b(e.helper),e=b(this).data("ui-draggable").options;s.css("zIndex")&&(e._zIndex=s.css("zIndex")),s.css("zIndex",e.zIndex)},stop:function(t,e){var s=b(this).data("ui-draggable").options;s._zIndex&&b(e.helper).css("zIndex",s._zIndex)}})}(jQuery);!function(a){function d(e,t,i){return t<e&&e<t+i}a.widget("ui.droppable",{version:"1.10.4",widgetEventPrefix:"drop",options:{accept:"*",activeClass:!1,addClasses:!0,greedy:!1,hoverClass:!1,scope:"default",tolerance:"intersect",activate:null,deactivate:null,drop:null,out:null,over:null},_create:function(){var e,t=this.options,i=t.accept;this.isover=!1,this.isout=!0,this.accept=a.isFunction(i)?i:function(e){return e.is(i)},this.proportions=function(){if(!arguments.length)return e=e||{width:this.element[0].offsetWidth,height:this.element[0].offsetHeight};e=arguments[0]},a.ui.ddmanager.droppables[t.scope]=a.ui.ddmanager.droppables[t.scope]||[],a.ui.ddmanager.droppables[t.scope].push(this),t.addClasses&&this.element.addClass("ui-droppable")},_destroy:function(){for(var e=0,t=a.ui.ddmanager.droppables[this.options.scope];e<t.length;e++)t[e]===this&&t.splice(e,1);this.element.removeClass("ui-droppable ui-droppable-disabled")},_setOption:function(e,t){"accept"===e&&(this.accept=a.isFunction(t)?t:function(e){return e.is(t)}),a.Widget.prototype._setOption.apply(this,arguments)},_activate:function(e){var t=a.ui.ddmanager.current;this.options.activeClass&&this.element.addClass(this.options.activeClass),t&&this._trigger("activate",e,this.ui(t))},_deactivate:function(e){var t=a.ui.ddmanager.current;this.options.activeClass&&this.element.removeClass(this.options.activeClass),t&&this._trigger("deactivate",e,this.ui(t))},_over:function(e){var t=a.ui.ddmanager.current;t&&(t.currentItem||t.element)[0]!==this.element[0]&&this.accept.call(this.element[0],t.currentItem||t.element)&&(this.options.hoverClass&&this.element.addClass(this.options.hoverClass),this._trigger("over",e,this.ui(t)))},_out:function(e){var t=a.ui.ddmanager.current;t&&(t.currentItem||t.element)[0]!==this.element[0]&&this.accept.call(this.element[0],t.currentItem||t.element)&&(this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("out",e,this.ui(t)))},_drop:function(e,t){var i=t||a.ui.ddmanager.current,s=!1;return!(!i||(i.currentItem||i.element)[0]===this.element[0])&&(this.element.find(":data(ui-droppable)").not(".ui-draggable-dragging").each(function(){var e=a.data(this,"ui-droppable");if(e.options.greedy&&!e.options.disabled&&e.options.scope===i.options.scope&&e.accept.call(e.element[0],i.currentItem||i.element)&&a.ui.intersect(i,a.extend(e,{offset:e.element.offset()}),e.options.tolerance))return!(s=!0)}),!s&&(!!this.accept.call(this.element[0],i.currentItem||i.element)&&(this.options.activeClass&&this.element.removeClass(this.options.activeClass),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("drop",e,this.ui(i)),this.element)))},ui:function(e){return{draggable:e.currentItem||e.element,helper:e.helper,position:e.position,offset:e.positionAbs}}}),a.ui.intersect=function(e,t,i){if(!t.offset)return!1;var s,o=(e.positionAbs||e.position.absolute).left,n=(e.positionAbs||e.position.absolute).top,r=o+e.helperProportions.width,a=n+e.helperProportions.height,l=t.offset.left,p=t.offset.top,h=l+t.proportions().width,c=p+t.proportions().height;switch(i){case"fit":return l<=o&&r<=h&&p<=n&&a<=c;case"intersect":return l<o+e.helperProportions.width/2&&r-e.helperProportions.width/2<h&&p<n+e.helperProportions.height/2&&a-e.helperProportions.height/2<c;case"pointer":return s=(e.positionAbs||e.position.absolute).left+(e.clickOffset||e.offset.click).left,d((e.positionAbs||e.position.absolute).top+(e.clickOffset||e.offset.click).top,p,t.proportions().height)&&d(s,l,t.proportions().width);case"touch":return(p<=n&&n<=c||p<=a&&a<=c||n<p&&c<a)&&(l<=o&&o<=h||l<=r&&r<=h||o<l&&h<r);default:return!1}},a.ui.ddmanager={current:null,droppables:{default:[]},prepareOffsets:function(e,t){var i,s,o=a.ui.ddmanager.droppables[e.options.scope]||[],n=t?t.type:null,r=(e.currentItem||e.element).find(":data(ui-droppable)").addBack();e:for(i=0;i<o.length;i++)if(!(o[i].options.disabled||e&&!o[i].accept.call(o[i].element[0],e.currentItem||e.element))){for(s=0;s<r.length;s++)if(r[s]===o[i].element[0]){o[i].proportions().height=0;continue e}o[i].visible="none"!==o[i].element.css("display"),o[i].visible&&("mousedown"===n&&o[i]._activate.call(o[i],t),o[i].offset=o[i].element.offset(),o[i].proportions({width:o[i].element[0].offsetWidth,height:o[i].element[0].offsetHeight}))}},drop:function(e,t){var i=!1;return a.each((a.ui.ddmanager.droppables[e.options.scope]||[]).slice(),function(){this.options&&(!this.options.disabled&&this.visible&&a.ui.intersect(e,this,this.options.tolerance)&&(i=this._drop.call(this,t)||i),!this.options.disabled&&this.visible&&this.accept.call(this.element[0],e.currentItem||e.element)&&(this.isout=!0,this.isover=!1,this._deactivate.call(this,t)))}),i},dragStart:function(e,t){e.element.parentsUntil("body").bind("scroll.droppable",function(){e.options.refreshPositions||a.ui.ddmanager.prepareOffsets(e,t)})},drag:function(o,n){o.options.refreshPositions&&a.ui.ddmanager.prepareOffsets(o,n),a.each(a.ui.ddmanager.droppables[o.options.scope]||[],function(){var e,t,i,s;this.options.disabled||this.greedyChild||!this.visible||(s=!(i=a.ui.intersect(o,this,this.options.tolerance))&&this.isover?"isout":i&&!this.isover?"isover":null)&&(this.options.greedy&&(t=this.options.scope,(i=this.element.parents(":data(ui-droppable)").filter(function(){return a.data(this,"ui-droppable").options.scope===t})).length&&((e=a.data(i[0],"ui-droppable")).greedyChild="isover"===s)),e&&"isover"===s&&(e.isover=!1,e.isout=!0,e._out.call(e,n)),this[s]=!0,this["isout"===s?"isover":"isout"]=!1,this["isover"===s?"_over":"_out"].call(this,n),e&&"isout"===s&&(e.isout=!1,e.isover=!0,e._over.call(e,n)))})},dragStop:function(e,t){e.element.parentsUntil("body").unbind("scroll.droppable"),e.options.refreshPositions||a.ui.ddmanager.prepareOffsets(e,t)}}}(jQuery);!function(m){function a(t){return parseInt(t,10)||0}function p(t){return!isNaN(parseInt(t,10))}m.widget("ui.resizable",m.ui.mouse,{version:"1.10.4",widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:90,resize:null,start:null,stop:null},_create:function(){var t,i,e,s,h=this,n=this.options;if(this.element.addClass("ui-resizable"),m.extend(this,{_aspectRatio:!!n.aspectRatio,aspectRatio:n.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],_helper:n.helper||n.ghost||n.animate?n.helper||"ui-resizable-helper":null}),this.element[0].nodeName.match(/canvas|textarea|input|select|button|img/i)&&(this.element.wrap(m("<div class='ui-wrapper' style='overflow: hidden;'></div>").css({position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("ui-resizable",this.element.data("ui-resizable")),this.elementIsWrapper=!0,this.element.css({marginLeft:this.originalElement.css("marginLeft"),marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom")}),this.originalElement.css({marginLeft:0,marginTop:0,marginRight:0,marginBottom:0}),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,display:"block"})),this.originalElement.css({margin:this.originalElement.css("margin")}),this._proportionallyResize()),this.handles=n.handles||(m(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se"),this.handles.constructor===String)for("all"===this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw"),t=this.handles.split(","),this.handles={},i=0;i<t.length;i++)e=m.trim(t[i]),(s=m("<div class='ui-resizable-handle "+("ui-resizable-"+e)+"'></div>")).css({zIndex:n.zIndex}),"se"===e&&s.addClass("ui-icon ui-icon-gripsmall-diagonal-se"),this.handles[e]=".ui-resizable-"+e,this.element.append(s);this._renderAxis=function(t){var i,e,s;for(i in t=t||this.element,this.handles)this.handles[i].constructor===String&&(this.handles[i]=m(this.handles[i],this.element).show()),this.elementIsWrapper&&this.originalElement[0].nodeName.match(/textarea|input|select|button/i)&&(e=m(this.handles[i],this.element),s=/sw|ne|nw|se|n|s/.test(i)?e.outerHeight():e.outerWidth(),e=["padding",/ne|nw|n/.test(i)?"Top":/se|sw|s/.test(i)?"Bottom":/^e$/.test(i)?"Right":"Left"].join(""),t.css(e,s),this._proportionallyResize()),m(this.handles[i]).length},this._renderAxis(this.element),this._handles=m(".ui-resizable-handle",this.element).disableSelection(),this._handles.mouseover(function(){h.resizing||(this.className&&(s=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)),h.axis=s&&s[1]?s[1]:"se")}),n.autoHide&&(this._handles.hide(),m(this.element).addClass("ui-resizable-autohide").mouseenter(function(){n.disabled||(m(this).removeClass("ui-resizable-autohide"),h._handles.show())}).mouseleave(function(){n.disabled||h.resizing||(m(this).addClass("ui-resizable-autohide"),h._handles.hide())})),this._mouseInit()},_destroy:function(){this._mouseDestroy();function t(t){m(t).removeClass("ui-resizable ui-resizable-disabled ui-resizable-resizing").removeData("resizable").removeData("ui-resizable").unbind(".resizable").find(".ui-resizable-handle").remove()}var i;return this.elementIsWrapper&&(t(this.element),i=this.element,this.originalElement.css({position:i.css("position"),width:i.outerWidth(),height:i.outerHeight(),top:i.css("top"),left:i.css("left")}).insertAfter(i),i.remove()),this.originalElement.css("resize",this.originalResizeStyle),t(this.originalElement),this},_mouseCapture:function(t){var i,e,s=!1;for(i in this.handles)(e=m(this.handles[i])[0])!==t.target&&!m.contains(e,t.target)||(s=!0);return!this.options.disabled&&s},_mouseStart:function(t){var i,e=this.options,s=this.element.position(),h=this.element;return this.resizing=!0,/absolute/.test(h.css("position"))?h.css({position:"absolute",top:h.css("top"),left:h.css("left")}):h.is(".ui-draggable")&&h.css({position:"absolute",top:s.top,left:s.left}),this._renderProxy(),i=a(this.helper.css("left")),s=a(this.helper.css("top")),e.containment&&(i+=m(e.containment).scrollLeft()||0,s+=m(e.containment).scrollTop()||0),this.offset=this.helper.offset(),this.position={left:i,top:s},this.size=this._helper?{width:this.helper.width(),height:this.helper.height()}:{width:h.width(),height:h.height()},this.originalSize=this._helper?{width:h.outerWidth(),height:h.outerHeight()}:{width:h.width(),height:h.height()},this.originalPosition={left:i,top:s},this.sizeDiff={width:h.outerWidth()-h.width(),height:h.outerHeight()-h.height()},this.originalMousePosition={left:t.pageX,top:t.pageY},this.aspectRatio="number"==typeof e.aspectRatio?e.aspectRatio:this.originalSize.width/this.originalSize.height||1,e=m(".ui-resizable-"+this.axis).css("cursor"),m("body").css("cursor","auto"===e?this.axis+"-resize":e),h.addClass("ui-resizable-resizing"),this._propagate("start",t),!0},_mouseDrag:function(t){var i=this.helper,e={},s=this.originalMousePosition,h=this.axis,n=this.position.top,o=this.position.left,a=this.size.width,l=this.size.height,r=t.pageX-s.left||0,s=t.pageY-s.top||0,h=this._change[h];return h&&(s=h.apply(this,[t,r,s]),this._updateVirtualBoundaries(t.shiftKey),(this._aspectRatio||t.shiftKey)&&(s=this._updateRatio(s,t)),s=this._respectSize(s,t),this._updateCache(s),this._propagate("resize",t),this.position.top!==n&&(e.top=this.position.top+"px"),this.position.left!==o&&(e.left=this.position.left+"px"),this.size.width!==a&&(e.width=this.size.width+"px"),this.size.height!==l&&(e.height=this.size.height+"px"),i.css(e),!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize(),m.isEmptyObject(e)||this._trigger("resize",t,this.ui())),!1},_mouseStop:function(t){this.resizing=!1;var i,e,s,h=this.options,n=this;return this._helper&&(s=(i=(e=this._proportionallyResizeElements).length&&/textarea/i.test(e[0].nodeName))&&m.ui.hasScroll(e[0],"left")?0:n.sizeDiff.height,e=i?0:n.sizeDiff.width,i={width:n.helper.width()-e,height:n.helper.height()-s},e=parseInt(n.element.css("left"),10)+(n.position.left-n.originalPosition.left)||null,s=parseInt(n.element.css("top"),10)+(n.position.top-n.originalPosition.top)||null,h.animate||this.element.css(m.extend(i,{top:s,left:e})),n.helper.height(n.size.height),n.helper.width(n.size.width),this._helper&&!h.animate&&this._proportionallyResize()),m("body").css("cursor","auto"),this.element.removeClass("ui-resizable-resizing"),this._propagate("stop",t),this._helper&&this.helper.remove(),!1},_updateVirtualBoundaries:function(t){var i,e,s=this.options,h={minWidth:p(s.minWidth)?s.minWidth:0,maxWidth:p(s.maxWidth)?s.maxWidth:1/0,minHeight:p(s.minHeight)?s.minHeight:0,maxHeight:p(s.maxHeight)?s.maxHeight:1/0};(this._aspectRatio||t)&&(i=h.minHeight*this.aspectRatio,e=h.minWidth/this.aspectRatio,s=h.maxHeight*this.aspectRatio,t=h.maxWidth/this.aspectRatio,i>h.minWidth&&(h.minWidth=i),e>h.minHeight&&(h.minHeight=e),s<h.maxWidth&&(h.maxWidth=s),t<h.maxHeight&&(h.maxHeight=t)),this._vBoundaries=h},_updateCache:function(t){this.offset=this.helper.offset(),p(t.left)&&(this.position.left=t.left),p(t.top)&&(this.position.top=t.top),p(t.height)&&(this.size.height=t.height),p(t.width)&&(this.size.width=t.width)},_updateRatio:function(t){var i=this.position,e=this.size,s=this.axis;return p(t.height)?t.width=t.height*this.aspectRatio:p(t.width)&&(t.height=t.width/this.aspectRatio),"sw"===s&&(t.left=i.left+(e.width-t.width),t.top=null),"nw"===s&&(t.top=i.top+(e.height-t.height),t.left=i.left+(e.width-t.width)),t},_respectSize:function(t){var i=this._vBoundaries,e=this.axis,s=p(t.width)&&i.maxWidth&&i.maxWidth<t.width,h=p(t.height)&&i.maxHeight&&i.maxHeight<t.height,n=p(t.width)&&i.minWidth&&i.minWidth>t.width,o=p(t.height)&&i.minHeight&&i.minHeight>t.height,a=this.originalPosition.left+this.originalSize.width,l=this.position.top+this.size.height,r=/sw|nw|w/.test(e),e=/nw|ne|n/.test(e);return n&&(t.width=i.minWidth),o&&(t.height=i.minHeight),s&&(t.width=i.maxWidth),h&&(t.height=i.maxHeight),n&&r&&(t.left=a-i.minWidth),s&&r&&(t.left=a-i.maxWidth),o&&e&&(t.top=l-i.minHeight),h&&e&&(t.top=l-i.maxHeight),t.width||t.height||t.left||!t.top?t.width||t.height||t.top||!t.left||(t.left=null):t.top=null,t},_proportionallyResize:function(){if(this._proportionallyResizeElements.length)for(var t,i,e,s,h=this.helper||this.element,n=0;n<this._proportionallyResizeElements.length;n++){if(s=this._proportionallyResizeElements[n],!this.borderDif)for(this.borderDif=[],i=[s.css("borderTopWidth"),s.css("borderRightWidth"),s.css("borderBottomWidth"),s.css("borderLeftWidth")],e=[s.css("paddingTop"),s.css("paddingRight"),s.css("paddingBottom"),s.css("paddingLeft")],t=0;t<i.length;t++)this.borderDif[t]=(parseInt(i[t],10)||0)+(parseInt(e[t],10)||0);s.css({height:h.height()-this.borderDif[0]-this.borderDif[2]||0,width:h.width()-this.borderDif[1]-this.borderDif[3]||0})}},_renderProxy:function(){var t=this.element,i=this.options;this.elementOffset=t.offset(),this._helper?(this.helper=this.helper||m("<div style='overflow:hidden;'></div>"),this.helper.addClass(this._helper).css({width:this.element.outerWidth()-1,height:this.element.outerHeight()-1,position:"absolute",left:this.elementOffset.left+"px",top:this.elementOffset.top+"px",zIndex:++i.zIndex}),this.helper.appendTo("body").disableSelection()):this.helper=this.element},_change:{e:function(t,i){return{width:this.originalSize.width+i}},w:function(t,i){var e=this.originalSize;return{left:this.originalPosition.left+i,width:e.width-i}},n:function(t,i,e){var s=this.originalSize;return{top:this.originalPosition.top+e,height:s.height-e}},s:function(t,i,e){return{height:this.originalSize.height+e}},se:function(t,i,e){return m.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[t,i,e]))},sw:function(t,i,e){return m.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[t,i,e]))},ne:function(t,i,e){return m.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[t,i,e]))},nw:function(t,i,e){return m.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[t,i,e]))}},_propagate:function(t,i){m.ui.plugin.call(this,t,[i,this.ui()]),"resize"!==t&&this._trigger(t,i,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}}),m.ui.plugin.add("resizable","animate",{stop:function(i){var e=m(this).data("ui-resizable"),t=e.options,s=e._proportionallyResizeElements,h=s.length&&/textarea/i.test(s[0].nodeName),n=h&&m.ui.hasScroll(s[0],"left")?0:e.sizeDiff.height,o=h?0:e.sizeDiff.width,h={width:e.size.width-o,height:e.size.height-n},o=parseInt(e.element.css("left"),10)+(e.position.left-e.originalPosition.left)||null,n=parseInt(e.element.css("top"),10)+(e.position.top-e.originalPosition.top)||null;e.element.animate(m.extend(h,n&&o?{top:n,left:o}:{}),{duration:t.animateDuration,easing:t.animateEasing,step:function(){var t={width:parseInt(e.element.css("width"),10),height:parseInt(e.element.css("height"),10),top:parseInt(e.element.css("top"),10),left:parseInt(e.element.css("left"),10)};s&&s.length&&m(s[0]).css({width:t.width,height:t.height}),e._updateCache(t),e._propagate("resize",i)}})}}),m.ui.plugin.add("resizable","containment",{start:function(){var e,s,t=m(this).data("ui-resizable"),i=t.options,h=t.element,n=i.containment,o=n instanceof m?n.get(0):/parent/.test(n)?h.parent().get(0):n;o&&(t.containerElement=m(o),/document/.test(n)||n===document?(t.containerOffset={left:0,top:0},t.containerPosition={left:0,top:0},t.parentData={element:m(document),left:0,top:0,width:m(document).width(),height:m(document).height()||document.body.parentNode.scrollHeight}):(e=m(o),s=[],m(["Top","Right","Left","Bottom"]).each(function(t,i){s[t]=a(e.css("padding"+i))}),t.containerOffset=e.offset(),t.containerPosition=e.position(),t.containerSize={height:e.innerHeight()-s[3],width:e.innerWidth()-s[1]},i=t.containerOffset,h=t.containerSize.height,n=t.containerSize.width,n=m.ui.hasScroll(o,"left")?o.scrollWidth:n,h=m.ui.hasScroll(o)?o.scrollHeight:h,t.parentData={element:o,left:i.left,top:i.top,width:n,height:h}))},resize:function(t){var i=m(this).data("ui-resizable"),e=i.options,s=i.containerOffset,h=i.position,n=i._aspectRatio||t.shiftKey,o={top:0,left:0},t=i.containerElement;t[0]!==document&&/static/.test(t.css("position"))&&(o=s),h.left<(i._helper?s.left:0)&&(i.size.width=i.size.width+(i._helper?i.position.left-s.left:i.position.left-o.left),n&&(i.size.height=i.size.width/i.aspectRatio),i.position.left=e.helper?s.left:0),h.top<(i._helper?s.top:0)&&(i.size.height=i.size.height+(i._helper?i.position.top-s.top:i.position.top),n&&(i.size.width=i.size.height*i.aspectRatio),i.position.top=i._helper?s.top:0),i.offset.left=i.parentData.left+i.position.left,i.offset.top=i.parentData.top+i.position.top,e=Math.abs((i._helper,i.offset.left-o.left+i.sizeDiff.width)),h=Math.abs((i._helper?i.offset.top-o.top:i.offset.top-s.top)+i.sizeDiff.height),o=i.containerElement.get(0)===i.element.parent().get(0),s=/relative|absolute/.test(i.containerElement.css("position")),o&&s&&(e-=Math.abs(i.parentData.left)),e+i.size.width>=i.parentData.width&&(i.size.width=i.parentData.width-e,n&&(i.size.height=i.size.width/i.aspectRatio)),h+i.size.height>=i.parentData.height&&(i.size.height=i.parentData.height-h,n&&(i.size.width=i.size.height*i.aspectRatio))},stop:function(){var t=m(this).data("ui-resizable"),i=t.options,e=t.containerOffset,s=t.containerPosition,h=t.containerElement,n=m(t.helper),o=n.offset(),a=n.outerWidth()-t.sizeDiff.width,n=n.outerHeight()-t.sizeDiff.height;t._helper&&!i.animate&&/relative/.test(h.css("position"))&&m(this).css({left:o.left-s.left-e.left,width:a,height:n}),t._helper&&!i.animate&&/static/.test(h.css("position"))&&m(this).css({left:o.left-s.left-e.left,width:a,height:n})}}),m.ui.plugin.add("resizable","alsoResize",{start:function(){function i(t){m(t).each(function(){var t=m(this);t.data("ui-resizable-alsoresize",{width:parseInt(t.width(),10),height:parseInt(t.height(),10),left:parseInt(t.css("left"),10),top:parseInt(t.css("top"),10)})})}var t=m(this).data("ui-resizable").options;"object"!=typeof t.alsoResize||t.alsoResize.parentNode?i(t.alsoResize):t.alsoResize.length?(t.alsoResize=t.alsoResize[0],i(t.alsoResize)):m.each(t.alsoResize,function(t){i(t)})},resize:function(t,n){function e(t,e){m(t).each(function(){var t=m(this),s=m(this).data("ui-resizable-alsoresize"),h={},i=e&&e.length?e:t.parents(n.originalElement[0]).length?["width","height"]:["width","height","top","left"];m.each(i,function(t,i){var e=(s[i]||0)+(a[i]||0);e&&0<=e&&(h[i]=e||null)}),t.css(h)})}var i=m(this).data("ui-resizable"),s=i.options,h=i.originalSize,o=i.originalPosition,a={height:i.size.height-h.height||0,width:i.size.width-h.width||0,top:i.position.top-o.top||0,left:i.position.left-o.left||0};"object"!=typeof s.alsoResize||s.alsoResize.nodeType?e(s.alsoResize):m.each(s.alsoResize,function(t,i){e(t,i)})},stop:function(){m(this).removeData("resizable-alsoresize")}}),m.ui.plugin.add("resizable","ghost",{start:function(){var t=m(this).data("ui-resizable"),i=t.options,e=t.size;t.ghost=t.originalElement.clone(),t.ghost.css({opacity:.25,display:"block",position:"relative",height:e.height,width:e.width,margin:0,left:0,top:0}).addClass("ui-resizable-ghost").addClass("string"==typeof i.ghost?i.ghost:""),t.ghost.appendTo(t.helper)},resize:function(){var t=m(this).data("ui-resizable");t.ghost&&t.ghost.css({position:"relative",height:t.size.height,width:t.size.width})},stop:function(){var t=m(this).data("ui-resizable");t.ghost&&t.helper&&t.helper.get(0).removeChild(t.ghost.get(0))}}),m.ui.plugin.add("resizable","grid",{resize:function(){var t=m(this).data("ui-resizable"),i=t.options,e=t.size,s=t.originalSize,h=t.originalPosition,n=t.axis,o="number"==typeof i.grid?[i.grid,i.grid]:i.grid,a=o[0]||1,l=o[1]||1,r=Math.round((e.width-s.width)/a)*a,p=Math.round((e.height-s.height)/l)*l,d=s.width+r,g=s.height+p,f=i.maxWidth&&i.maxWidth<d,c=i.maxHeight&&i.maxHeight<g,u=i.minWidth&&i.minWidth>d,e=i.minHeight&&i.minHeight>g;i.grid=o,u&&(d+=a),e&&(g+=l),f&&(d-=a),c&&(g-=l),/^(se|s|e)$/.test(n)?(t.size.width=d,t.size.height=g):/^(ne)$/.test(n)?(t.size.width=d,t.size.height=g,t.position.top=h.top-p):/^(sw)$/.test(n)?(t.size.width=d,t.size.height=g,t.position.left=h.left-r):(0<g-l?(t.size.height=g,t.position.top=h.top-p):(t.size.height=l,t.position.top=h.top+s.height-l),0<d-a?(t.size.width=d,t.position.left=h.left-r):(t.size.width=a,t.position.left=h.left+s.width-a))}})}(jQuery);!function(u){u.widget("ui.selectable",u.ui.mouse,{version:"1.10.4",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var e,t=this;this.element.addClass("ui-selectable"),this.dragged=!1,this.refresh=function(){(e=u(t.options.filter,t.element[0])).addClass("ui-selectee"),e.each(function(){var e=u(this),t=e.offset();u.data(this,"selectable-item",{element:this,$element:e,left:t.left,top:t.top,right:t.left+e.outerWidth(),bottom:t.top+e.outerHeight(),startselected:!1,selected:e.hasClass("ui-selected"),selecting:e.hasClass("ui-selecting"),unselecting:e.hasClass("ui-unselecting")})})},this.refresh(),this.selectees=e.addClass("ui-selectee"),this._mouseInit(),this.helper=u("<div class='ui-selectable-helper'></div>")},_destroy:function(){this.selectees.removeClass("ui-selectee").removeData("selectable-item"),this.element.removeClass("ui-selectable ui-selectable-disabled"),this._mouseDestroy()},_mouseStart:function(s){var l=this,e=this.options;this.opos=[s.pageX,s.pageY],this.options.disabled||(this.selectees=u(e.filter,this.element[0]),this._trigger("start",s),u(e.appendTo).append(this.helper),this.helper.css({left:s.pageX,top:s.pageY,width:0,height:0}),e.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each(function(){var e=u.data(this,"selectable-item");e.startselected=!0,s.metaKey||s.ctrlKey||(e.$element.removeClass("ui-selected"),e.selected=!1,e.$element.addClass("ui-unselecting"),e.unselecting=!0,l._trigger("unselecting",s,{unselecting:e.element}))}),u(s.target).parents().addBack().each(function(){var e,t=u.data(this,"selectable-item");if(t)return e=!s.metaKey&&!s.ctrlKey||!t.$element.hasClass("ui-selected"),t.$element.removeClass(e?"ui-unselecting":"ui-selected").addClass(e?"ui-selecting":"ui-unselecting"),t.unselecting=!e,t.selecting=e,(t.selected=e)?l._trigger("selecting",s,{selecting:t.element}):l._trigger("unselecting",s,{unselecting:t.element}),!1}))},_mouseDrag:function(s){if(this.dragged=!0,!this.options.disabled){var e,l=this,i=this.options,n=this.opos[0],c=this.opos[1],a=s.pageX,r=s.pageY;return a<n&&(e=a,a=n,n=e),r<c&&(e=r,r=c,c=e),this.helper.css({left:n,top:c,width:a-n,height:r-c}),this.selectees.each(function(){var e=u.data(this,"selectable-item"),t=!1;e&&e.element!==l.element[0]&&("touch"===i.tolerance?t=!(e.left>a||e.right<n||e.top>r||e.bottom<c):"fit"===i.tolerance&&(t=e.left>n&&e.right<a&&e.top>c&&e.bottom<r),t?(e.selected&&(e.$element.removeClass("ui-selected"),e.selected=!1),e.unselecting&&(e.$element.removeClass("ui-unselecting"),e.unselecting=!1),e.selecting||(e.$element.addClass("ui-selecting"),e.selecting=!0,l._trigger("selecting",s,{selecting:e.element}))):(e.selecting&&((s.metaKey||s.ctrlKey)&&e.startselected?(e.$element.removeClass("ui-selecting"),e.selecting=!1,e.$element.addClass("ui-selected"),e.selected=!0):(e.$element.removeClass("ui-selecting"),e.selecting=!1,e.startselected&&(e.$element.addClass("ui-unselecting"),e.unselecting=!0),l._trigger("unselecting",s,{unselecting:e.element}))),e.selected&&(s.metaKey||s.ctrlKey||e.startselected||(e.$element.removeClass("ui-selected"),e.selected=!1,e.$element.addClass("ui-unselecting"),e.unselecting=!0,l._trigger("unselecting",s,{unselecting:e.element})))))}),!1}},_mouseStop:function(t){var s=this;return this.dragged=!1,u(".ui-unselecting",this.element[0]).each(function(){var e=u.data(this,"selectable-item");e.$element.removeClass("ui-unselecting"),e.unselecting=!1,e.startselected=!1,s._trigger("unselected",t,{unselected:e.element})}),u(".ui-selecting",this.element[0]).each(function(){var e=u.data(this,"selectable-item");e.$element.removeClass("ui-selecting").addClass("ui-selected"),e.selecting=!1,e.selected=!0,e.startselected=!0,s._trigger("selected",t,{selected:e.element})}),this._trigger("stop",t),this.helper.remove(),!1}})}(jQuery);!function(u){function d(t,e,i){return e<t&&t<e+i}function m(t){return/left|right/.test(t.css("float"))||/inline|table-cell/.test(t.css("display"))}u.widget("ui.sortable",u.ui.mouse,{version:"1.10.4",widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1e3,activate:null,beforeStop:null,change:null,deactivate:null,out:null,over:null,receive:null,remove:null,sort:null,start:null,stop:null,update:null},_create:function(){var t=this.options;this.containerCache={},this.element.addClass("ui-sortable"),this.refresh(),this.floating=!!this.items.length&&("x"===t.axis||m(this.items[0].item)),this.offset=this.element.offset(),this._mouseInit(),this.ready=!0},_destroy:function(){this.element.removeClass("ui-sortable ui-sortable-disabled"),this._mouseDestroy();for(var t=this.items.length-1;0<=t;t--)this.items[t].item.removeData(this.widgetName+"-item");return this},_setOption:function(t,e){"disabled"===t?(this.options[t]=e,this.widget().toggleClass("ui-sortable-disabled",!!e)):u.Widget.prototype._setOption.apply(this,arguments)},_mouseCapture:function(t,e){var i=null,s=!1,o=this;return!this.reverting&&(!this.options.disabled&&"static"!==this.options.type&&(this._refreshItems(t),u(t.target).parents().each(function(){if(u.data(this,o.widgetName+"-item")===o)return i=u(this),!1}),!!(i=u.data(t.target,o.widgetName+"-item")===o?u(t.target):i)&&(!(this.options.handle&&!e&&(u(this.options.handle,i).find("*").addBack().each(function(){this===t.target&&(s=!0)}),!s))&&(this.currentItem=i,this._removeCurrentsFromItems(),!0))))},_mouseStart:function(t,e,i){var s,o,r=this.options;if((this.currentContainer=this).refreshPositions(),this.helper=this._createHelper(t),this._cacheHelperProportions(),this._cacheMargins(),this.scrollParent=this.helper.scrollParent(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},u.extend(this.offset,{click:{left:t.pageX-this.offset.left,top:t.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()}),this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),this.originalPosition=this._generatePosition(t),this.originalPageX=t.pageX,this.originalPageY=t.pageY,r.cursorAt&&this._adjustOffsetFromHelper(r.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!==this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),r.containment&&this._setContainment(),r.cursor&&"auto"!==r.cursor&&(o=this.document.find("body"),this.storedCursor=o.css("cursor"),o.css("cursor",r.cursor),this.storedStylesheet=u("<style>*{ cursor: "+r.cursor+" !important; }</style>").appendTo(o)),r.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",r.opacity)),r.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",r.zIndex)),this.scrollParent[0]!==document&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",t,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions(),!i)for(s=this.containers.length-1;0<=s;s--)this.containers[s]._trigger("activate",t,this._uiHash(this));return u.ui.ddmanager&&(u.ui.ddmanager.current=this),u.ui.ddmanager&&!r.dropBehaviour&&u.ui.ddmanager.prepareOffsets(this,t),this.dragging=!0,this.helper.addClass("ui-sortable-helper"),this._mouseDrag(t),!0},_mouseDrag:function(t){var e,i,s,o,r=this.options,n=!1;for(this.position=this._generatePosition(t),this.positionAbs=this._convertPositionTo("absolute"),this.lastPositionAbs||(this.lastPositionAbs=this.positionAbs),this.options.scroll&&(this.scrollParent[0]!==document&&"HTML"!==this.scrollParent[0].tagName?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-t.pageY<r.scrollSensitivity?this.scrollParent[0].scrollTop=n=this.scrollParent[0].scrollTop+r.scrollSpeed:t.pageY-this.overflowOffset.top<r.scrollSensitivity&&(this.scrollParent[0].scrollTop=n=this.scrollParent[0].scrollTop-r.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-t.pageX<r.scrollSensitivity?this.scrollParent[0].scrollLeft=n=this.scrollParent[0].scrollLeft+r.scrollSpeed:t.pageX-this.overflowOffset.left<r.scrollSensitivity&&(this.scrollParent[0].scrollLeft=n=this.scrollParent[0].scrollLeft-r.scrollSpeed)):(t.pageY-u(document).scrollTop()<r.scrollSensitivity?n=u(document).scrollTop(u(document).scrollTop()-r.scrollSpeed):u(window).height()-(t.pageY-u(document).scrollTop())<r.scrollSensitivity&&(n=u(document).scrollTop(u(document).scrollTop()+r.scrollSpeed)),t.pageX-u(document).scrollLeft()<r.scrollSensitivity?n=u(document).scrollLeft(u(document).scrollLeft()-r.scrollSpeed):u(window).width()-(t.pageX-u(document).scrollLeft())<r.scrollSensitivity&&(n=u(document).scrollLeft(u(document).scrollLeft()+r.scrollSpeed))),!1!==n&&u.ui.ddmanager&&!r.dropBehaviour&&u.ui.ddmanager.prepareOffsets(this,t)),this.positionAbs=this._convertPositionTo("absolute"),this.options.axis&&"y"===this.options.axis||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&"x"===this.options.axis||(this.helper[0].style.top=this.position.top+"px"),e=this.items.length-1;0<=e;e--)if(s=(i=this.items[e]).item[0],(o=this._intersectsWithPointer(i))&&i.instance===this.currentContainer&&!(s===this.currentItem[0]||this.placeholder[1===o?"next":"prev"]()[0]===s||u.contains(this.placeholder[0],s)||"semi-dynamic"===this.options.type&&u.contains(this.element[0],s))){if(this.direction=1===o?"down":"up","pointer"!==this.options.tolerance&&!this._intersectsWithSides(i))break;this._rearrange(t,i),this._trigger("change",t,this._uiHash());break}return this._contactContainers(t),u.ui.ddmanager&&u.ui.ddmanager.drag(this,t),this._trigger("sort",t,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(t,e){var i,s,o,r;if(t)return u.ui.ddmanager&&!this.options.dropBehaviour&&u.ui.ddmanager.drop(this,t),this.options.revert?(s=(i=this).placeholder.offset(),r={},(o=this.options.axis)&&"x"!==o||(r.left=s.left-this.offset.parent.left-this.margins.left+(this.offsetParent[0]===document.body?0:this.offsetParent[0].scrollLeft)),o&&"y"!==o||(r.top=s.top-this.offset.parent.top-this.margins.top+(this.offsetParent[0]===document.body?0:this.offsetParent[0].scrollTop)),this.reverting=!0,u(this.helper).animate(r,parseInt(this.options.revert,10)||500,function(){i._clear(t)})):this._clear(t,e),!1},cancel:function(){if(this.dragging){this._mouseUp({target:null}),"original"===this.options.helper?this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper"):this.currentItem.show();for(var t=this.containers.length-1;0<=t;t--)this.containers[t]._trigger("deactivate",null,this._uiHash(this)),this.containers[t].containerCache.over&&(this.containers[t]._trigger("out",null,this._uiHash(this)),this.containers[t].containerCache.over=0)}return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),"original"!==this.options.helper&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),u.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?u(this.domPosition.prev).after(this.currentItem):u(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(e){var t=this._getItemsAsjQuery(e&&e.connected),i=[];return e=e||{},u(t).each(function(){var t=(u(e.item||this).attr(e.attribute||"id")||"").match(e.expression||/(.+)[\-=_](.+)/);t&&i.push((e.key||t[1]+"[]")+"="+(e.key&&e.expression?t[1]:t[2]))}),!i.length&&e.key&&i.push(e.key+"="),i.join("&")},toArray:function(t){var e=this._getItemsAsjQuery(t&&t.connected),i=[];return t=t||{},e.each(function(){i.push(u(t.item||this).attr(t.attribute||"id")||"")}),i},_intersectsWith:function(t){var e=this.positionAbs.left,i=e+this.helperProportions.width,s=this.positionAbs.top,o=s+this.helperProportions.height,r=t.left,n=r+t.width,h=t.top,a=h+t.height,l=this.offset.click.top,c=this.offset.click.left,l="x"===this.options.axis||h<s+l&&s+l<a,c="y"===this.options.axis||r<e+c&&e+c<n;return"pointer"===this.options.tolerance||this.options.forcePointerForContainers||"pointer"!==this.options.tolerance&&this.helperProportions[this.floating?"width":"height"]>t[this.floating?"width":"height"]?l&&c:r<e+this.helperProportions.width/2&&i-this.helperProportions.width/2<n&&h<s+this.helperProportions.height/2&&o-this.helperProportions.height/2<a},_intersectsWithPointer:function(t){var e="x"===this.options.axis||d(this.positionAbs.top+this.offset.click.top,t.top,t.height),i="y"===this.options.axis||d(this.positionAbs.left+this.offset.click.left,t.left,t.width),t=e&&i,e=this._getDragVerticalDirection(),i=this._getDragHorizontalDirection();return!!t&&(this.floating?i&&"right"===i||"down"===e?2:1:e&&("down"===e?2:1))},_intersectsWithSides:function(t){var e=d(this.positionAbs.top+this.offset.click.top,t.top+t.height/2,t.height),i=d(this.positionAbs.left+this.offset.click.left,t.left+t.width/2,t.width),s=this._getDragVerticalDirection(),t=this._getDragHorizontalDirection();return this.floating&&t?"right"===t&&i||"left"===t&&!i:s&&("down"===s&&e||"up"===s&&!e)},_getDragVerticalDirection:function(){var t=this.positionAbs.top-this.lastPositionAbs.top;return 0!=t&&(0<t?"down":"up")},_getDragHorizontalDirection:function(){var t=this.positionAbs.left-this.lastPositionAbs.left;return 0!=t&&(0<t?"right":"left")},refresh:function(t){return this._refreshItems(t),this.refreshPositions(),this},_connectWith:function(){var t=this.options;return t.connectWith.constructor===String?[t.connectWith]:t.connectWith},_getItemsAsjQuery:function(t){var e,i,s,o,r=[],n=[],h=this._connectWith();if(h&&t)for(e=h.length-1;0<=e;e--)for(i=(s=u(h[e])).length-1;0<=i;i--)(o=u.data(s[i],this.widgetFullName))&&o!==this&&!o.options.disabled&&n.push([u.isFunction(o.options.items)?o.options.items.call(o.element):u(o.options.items,o.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),o]);function a(){r.push(this)}for(n.push([u.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):u(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]),e=n.length-1;0<=e;e--)n[e][0].each(a);return u(r)},_removeCurrentsFromItems:function(){var i=this.currentItem.find(":data("+this.widgetName+"-item)");this.items=u.grep(this.items,function(t){for(var e=0;e<i.length;e++)if(i[e]===t.item[0])return!1;return!0})},_refreshItems:function(t){this.items=[],this.containers=[this];var e,i,s,o,r,n,h,a,l=this.items,c=[[u.isFunction(this.options.items)?this.options.items.call(this.element[0],t,{item:this.currentItem}):u(this.options.items,this.element),this]],p=this._connectWith();if(p&&this.ready)for(e=p.length-1;0<=e;e--)for(i=(s=u(p[e])).length-1;0<=i;i--)(o=u.data(s[i],this.widgetFullName))&&o!==this&&!o.options.disabled&&(c.push([u.isFunction(o.options.items)?o.options.items.call(o.element[0],t,{item:this.currentItem}):u(o.options.items,o.element),o]),this.containers.push(o));for(e=c.length-1;0<=e;e--)for(r=c[e][1],a=(n=c[e][i=0]).length;i<a;i++)(h=u(n[i])).data(this.widgetName+"-item",r),l.push({item:h,instance:r,width:0,height:0,left:0,top:0})},refreshPositions:function(t){var e,i,s,o;for(this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset()),e=this.items.length-1;0<=e;e--)(i=this.items[e]).instance!==this.currentContainer&&this.currentContainer&&i.item[0]!==this.currentItem[0]||(s=this.options.toleranceElement?u(this.options.toleranceElement,i.item):i.item,t||(i.width=s.outerWidth(),i.height=s.outerHeight()),o=s.offset(),i.left=o.left,i.top=o.top);if(this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);else for(e=this.containers.length-1;0<=e;e--)o=this.containers[e].element.offset(),this.containers[e].containerCache.left=o.left,this.containers[e].containerCache.top=o.top,this.containers[e].containerCache.width=this.containers[e].element.outerWidth(),this.containers[e].containerCache.height=this.containers[e].element.outerHeight();return this},_createPlaceholder:function(i){var s,o=(i=i||this).options;o.placeholder&&o.placeholder.constructor!==String||(s=o.placeholder,o.placeholder={element:function(){var t=i.currentItem[0].nodeName.toLowerCase(),e=u("<"+t+">",i.document[0]).addClass(s||i.currentItem[0].className+" ui-sortable-placeholder").removeClass("ui-sortable-helper");return"tr"===t?i.currentItem.children().each(function(){u("<td>&#160;</td>",i.document[0]).attr("colspan",u(this).attr("colspan")||1).appendTo(e)}):"img"===t&&e.attr("src",i.currentItem.attr("src")),s||e.css("visibility","hidden"),e},update:function(t,e){s&&!o.forcePlaceholderSize||(e.height()||e.height(i.currentItem.innerHeight()-parseInt(i.currentItem.css("paddingTop")||0,10)-parseInt(i.currentItem.css("paddingBottom")||0,10)),e.width()||e.width(i.currentItem.innerWidth()-parseInt(i.currentItem.css("paddingLeft")||0,10)-parseInt(i.currentItem.css("paddingRight")||0,10)))}}),i.placeholder=u(o.placeholder.element.call(i.element,i.currentItem)),i.currentItem.after(i.placeholder),o.placeholder.update(i,i.placeholder)},_contactContainers:function(t){for(var e,i,s,o,r,n,h,a,l,c=null,p=null,f=this.containers.length-1;0<=f;f--)u.contains(this.currentItem[0],this.containers[f].element[0])||(this._intersectsWith(this.containers[f].containerCache)?c&&u.contains(this.containers[f].element[0],c.element[0])||(c=this.containers[f],p=f):this.containers[f].containerCache.over&&(this.containers[f]._trigger("out",t,this._uiHash(this)),this.containers[f].containerCache.over=0));if(c)if(1===this.containers.length)this.containers[p].containerCache.over||(this.containers[p]._trigger("over",t,this._uiHash(this)),this.containers[p].containerCache.over=1);else{for(i=1e4,s=null,r=(l=c.floating||m(this.currentItem))?"width":"height",n=this.positionAbs[o=l?"left":"top"]+this.offset.click[o],e=this.items.length-1;0<=e;e--)u.contains(this.containers[p].element[0],this.items[e].item[0])&&this.items[e].item[0]!==this.currentItem[0]&&(l&&!d(this.positionAbs.top+this.offset.click.top,this.items[e].top,this.items[e].height)||(h=this.items[e].item.offset()[o],a=!1,Math.abs(h-n)>Math.abs(h+this.items[e][r]-n)&&(a=!0,h+=this.items[e][r]),Math.abs(h-n)<i&&(i=Math.abs(h-n),s=this.items[e],this.direction=a?"up":"down")));(s||this.options.dropOnEmpty)&&this.currentContainer!==this.containers[p]&&(s?this._rearrange(t,s,null,!0):this._rearrange(t,null,this.containers[p].element,!0),this._trigger("change",t,this._uiHash()),this.containers[p]._trigger("change",t,this._uiHash(this)),this.currentContainer=this.containers[p],this.options.placeholder.update(this.currentContainer,this.placeholder),this.containers[p]._trigger("over",t,this._uiHash(this)),this.containers[p].containerCache.over=1)}},_createHelper:function(t){var e=this.options,t=u.isFunction(e.helper)?u(e.helper.apply(this.element[0],[t,this.currentItem])):"clone"===e.helper?this.currentItem.clone():this.currentItem;return t.parents("body").length||u("parent"!==e.appendTo?e.appendTo:this.currentItem[0].parentNode)[0].appendChild(t[0]),t[0]===this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),t[0].style.width&&!e.forceHelperSize||t.width(this.currentItem.width()),t[0].style.height&&!e.forceHelperSize||t.height(this.currentItem.height()),t},_adjustOffsetFromHelper:function(t){"string"==typeof t&&(t=t.split(" ")),"left"in(t=u.isArray(t)?{left:+t[0],top:+t[1]||0}:t)&&(this.offset.click.left=t.left+this.margins.left),"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left),"top"in t&&(this.offset.click.top=t.top+this.margins.top),"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var t=this.offsetParent.offset();return"absolute"===this.cssPosition&&this.scrollParent[0]!==document&&u.contains(this.scrollParent[0],this.offsetParent[0])&&(t.left+=this.scrollParent.scrollLeft(),t.top+=this.scrollParent.scrollTop()),{top:(t=this.offsetParent[0]===document.body||this.offsetParent[0].tagName&&"html"===this.offsetParent[0].tagName.toLowerCase()&&u.ui.ie?{top:0,left:0}:t).top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:t.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"!==this.cssPosition)return{top:0,left:0};var t=this.currentItem.position();return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:t.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var t,e,i=this.options;"parent"===i.containment&&(i.containment=this.helper[0].parentNode),"document"!==i.containment&&"window"!==i.containment||(this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,u("document"===i.containment?document:window).width()-this.helperProportions.width-this.margins.left,(u("document"===i.containment?document:window).height()||document.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]),/^(document|window|parent)$/.test(i.containment)||(t=u(i.containment)[0],e=u(i.containment).offset(),i="hidden"!==u(t).css("overflow"),this.containment=[e.left+(parseInt(u(t).css("borderLeftWidth"),10)||0)+(parseInt(u(t).css("paddingLeft"),10)||0)-this.margins.left,e.top+(parseInt(u(t).css("borderTopWidth"),10)||0)+(parseInt(u(t).css("paddingTop"),10)||0)-this.margins.top,e.left+(i?Math.max(t.scrollWidth,t.offsetWidth):t.offsetWidth)-(parseInt(u(t).css("borderLeftWidth"),10)||0)-(parseInt(u(t).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,e.top+(i?Math.max(t.scrollHeight,t.offsetHeight):t.offsetHeight)-(parseInt(u(t).css("borderTopWidth"),10)||0)-(parseInt(u(t).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top])},_convertPositionTo:function(t,e){e=e||this.position;var i="absolute"===t?1:-1,s="absolute"!==this.cssPosition||this.scrollParent[0]!==document&&u.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,t=/(html|body)/i.test(s[0].tagName);return{top:e.top+this.offset.relative.top*i+this.offset.parent.top*i-("fixed"===this.cssPosition?-this.scrollParent.scrollTop():t?0:s.scrollTop())*i,left:e.left+this.offset.relative.left*i+this.offset.parent.left*i-("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():t?0:s.scrollLeft())*i}},_generatePosition:function(t){var e=this.options,i=t.pageX,s=t.pageY,o="absolute"!==this.cssPosition||this.scrollParent[0]!==document&&u.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,r=/(html|body)/i.test(o[0].tagName);return"relative"!==this.cssPosition||this.scrollParent[0]!==document&&this.scrollParent[0]!==this.offsetParent[0]||(this.offset.relative=this._getRelativeOffset()),this.originalPosition&&(this.containment&&(t.pageX-this.offset.click.left<this.containment[0]&&(i=this.containment[0]+this.offset.click.left),t.pageY-this.offset.click.top<this.containment[1]&&(s=this.containment[1]+this.offset.click.top),t.pageX-this.offset.click.left>this.containment[2]&&(i=this.containment[2]+this.offset.click.left),t.pageY-this.offset.click.top>this.containment[3]&&(s=this.containment[3]+this.offset.click.top)),e.grid&&(t=this.originalPageY+Math.round((s-this.originalPageY)/e.grid[1])*e.grid[1],s=!this.containment||t-this.offset.click.top>=this.containment[1]&&t-this.offset.click.top<=this.containment[3]?t:t-this.offset.click.top>=this.containment[1]?t-e.grid[1]:t+e.grid[1],t=this.originalPageX+Math.round((i-this.originalPageX)/e.grid[0])*e.grid[0],i=!this.containment||t-this.offset.click.left>=this.containment[0]&&t-this.offset.click.left<=this.containment[2]?t:t-this.offset.click.left>=this.containment[0]?t-e.grid[0]:t+e.grid[0])),{top:s-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.scrollParent.scrollTop():r?0:o.scrollTop()),left:i-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():r?0:o.scrollLeft())}},_rearrange:function(t,e,i,s){i?i[0].appendChild(this.placeholder[0]):e.item[0].parentNode.insertBefore(this.placeholder[0],"down"===this.direction?e.item[0]:e.item[0].nextSibling),this.counter=this.counter?++this.counter:1;var o=this.counter;this._delay(function(){o===this.counter&&this.refreshPositions(!s)})},_clear:function(t,e){this.reverting=!1;var i,s=[];if(!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem),this._noFinalSort=null,this.helper[0]===this.currentItem[0]){for(i in this._storedCSS)"auto"!==this._storedCSS[i]&&"static"!==this._storedCSS[i]||(this._storedCSS[i]="");this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper")}else this.currentItem.show();function o(e,i,s){return function(t){s._trigger(e,t,i._uiHash(i))}}for(this.fromOutside&&!e&&s.push(function(t){this._trigger("receive",t,this._uiHash(this.fromOutside))}),!this.fromOutside&&this.domPosition.prev===this.currentItem.prev().not(".ui-sortable-helper")[0]&&this.domPosition.parent===this.currentItem.parent()[0]||e||s.push(function(t){this._trigger("update",t,this._uiHash())}),this!==this.currentContainer&&(e||(s.push(function(t){this._trigger("remove",t,this._uiHash())}),s.push(function(e){return function(t){e._trigger("receive",t,this._uiHash(this))}}.call(this,this.currentContainer)),s.push(function(e){return function(t){e._trigger("update",t,this._uiHash(this))}}.call(this,this.currentContainer)))),i=this.containers.length-1;0<=i;i--)e||s.push(o("deactivate",this,this.containers[i])),this.containers[i].containerCache.over&&(s.push(o("out",this,this.containers[i])),this.containers[i].containerCache.over=0);if(this.storedCursor&&(this.document.find("body").css("cursor",this.storedCursor),this.storedStylesheet.remove()),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex","auto"===this._storedZIndex?"":this._storedZIndex),this.dragging=!1,this.cancelHelperRemoval){if(!e){for(this._trigger("beforeStop",t,this._uiHash()),i=0;i<s.length;i++)s[i].call(this,t);this._trigger("stop",t,this._uiHash())}return this.fromOutside=!1}if(e||this._trigger("beforeStop",t,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.helper[0]!==this.currentItem[0]&&this.helper.remove(),this.helper=null,!e){for(i=0;i<s.length;i++)s[i].call(this,t);this._trigger("stop",t,this._uiHash())}return!(this.fromOutside=!1)},_trigger:function(){!1===u.Widget.prototype._trigger.apply(this,arguments)&&this.cancel()},_uiHash:function(t){var e=t||this;return{helper:e.helper,placeholder:e.placeholder||u([]),position:e.position,originalPosition:e.originalPosition,offset:e.positionAbs,item:e.currentItem,sender:t?t.element:null}}})}(jQuery);